# Test script for the automation API endpoints

$baseUrl = "http://localhost:3000"
$apiKey = "aidude_admin_2024_secure_key_xyz789"
$headers = @{
    'x-api-key' = $apiKey
    'Content-Type' = 'application/json'
}

Write-Host "🧪 Testing Automation API Endpoints" -ForegroundColor Cyan
Write-Host ""

# Test 1: List jobs
Write-Host "📋 Testing GET /api/automation/jobs..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/automation/jobs" -Method Get -Headers $headers
    Write-Host "✅ Success: Found $($response.data.jobs.Count) jobs" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 2: Create a web scraping job
Write-Host "🕷️ Testing POST /api/automation/jobs (Web Scraping)..." -ForegroundColor Yellow
try {
    $body = @{
        type = "WEB_SCRAPING"
        data = @{
            url = "https://example.com"
            options = @{
                timeout = 10000
                extractImages = $false
                extractLinks = $false
            }
        }
        options = @{
            priority = "NORMAL"
        }
    }
    $body = $body | ConvertTo-Json -Depth 4

    $response = Invoke-RestMethod -Uri "$baseUrl/api/automation/jobs" -Method Post -Headers $headers -Body $body
    Write-Host "✅ Success: Created job $($response.job.id)" -ForegroundColor Green
    $jobId = $response.job.id
} catch {
    Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: Get job details
if ($jobId) {
    Write-Host "🔍 Testing GET /api/automation/jobs/$jobId..." -ForegroundColor Yellow
    try {
        Start-Sleep -Seconds 2  # Wait a bit for processing
        $response = Invoke-RestMethod -Uri "$baseUrl/api/automation/jobs/$jobId" -Method Get -Headers $headers
        Write-Host "✅ Success: Job status is $($response.data.status)" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""

# Test 4: Test tool submission endpoint
Write-Host "🛠️ Testing POST /api/submissions..." -ForegroundColor Yellow
try {
    $submissionBody = @{
        name = "Test Tool"
        url = "https://example.com"
        description = "A test tool for API testing"
        category = "productivity"
        submitterEmail = "<EMAIL>"
        submitterName = "Test User"
    }
    $submissionBody = $submissionBody | ConvertTo-Json -Depth 3

    $submissionHeaders = @{
        'Content-Type' = 'application/json'
    }

    $response = Invoke-RestMethod -Uri "$baseUrl/api/submissions" -Method Post -Headers $submissionHeaders -Body $submissionBody
    Write-Host "✅ Success: Tool submitted successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 5: List jobs again to see new jobs
Write-Host "📋 Testing GET /api/automation/jobs (after submission)..." -ForegroundColor Yellow
try {
    Start-Sleep -Seconds 1  # Wait a bit
    $response = Invoke-RestMethod -Uri "$baseUrl/api/automation/jobs" -Method Get -Headers $headers
    Write-Host "✅ Success: Found $($response.data.jobs.Count) jobs" -ForegroundColor Green
    
    if ($response.data.jobs.Count -gt 0) {
        Write-Host ""
        Write-Host "📊 Job Status Summary:" -ForegroundColor Cyan
        $statusCounts = @{}
        foreach ($job in $response.data.jobs) {
            if ($statusCounts.ContainsKey($job.status)) {
                $statusCounts[$job.status]++
            } else {
                $statusCounts[$job.status] = 1
            }
        }
        
        foreach ($status in $statusCounts.Keys) {
            $count = $statusCounts[$status]
            Write-Host "  ${status}: $count" -ForegroundColor White
        }
    }
} catch {
    Write-Host "❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 API testing completed!" -ForegroundColor Green
