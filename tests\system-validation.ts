/**
 * System Validation Test
 *
 * Core system validation with proper environment variable loading
 * Tests the fundamental components and architecture of the enhanced AI system
 */

import { createClient } from '@supabase/supabase-js';
import {
  getTestEnvironment,
  printTestEnvironmentStatus,
  createTestSupabaseClient,
  isTestServiceAvailable
} from './utils/test-environment';

interface ValidationResult {
  component: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
}

class SystemValidator {
  private results: ValidationResult[] = [];

  async validateSystem(): Promise<void> {
    console.log('🔍 Enhanced AI System - Core System Validation');
    console.log('==============================================\n');

    // Print environment status first
    printTestEnvironmentStatus();

    await this.validateEnvironmentConfiguration();
    await this.validateDatabaseSchema();
    await this.validateFileStructure();
    await this.validateTypeScriptCompilation();
    await this.validateCoreModules();
    await this.validateMigrationSystem();

    this.generateValidationReport();
  }

  private async validateEnvironmentConfiguration(): Promise<void> {
    console.log('🔧 Validating Environment Configuration...');

    const testEnv = getTestEnvironment();
    const validation = testEnv.validateEnvironment();

    this.addResult('Environment Loading', 'pass',
      'Environment variables loaded successfully');

    if (validation.isValid) {
      this.addResult('Required Variables', 'pass',
        'All required environment variables are present');
    } else {
      this.addResult('Required Variables', 'fail',
        `Missing required variables: ${validation.missingRequired.join(', ')}`);
    }

    if (validation.missingOptional.length > 0) {
      this.addResult('Optional Variables', 'warning',
        `Missing optional variables: ${validation.missingOptional.join(', ')}`);
    } else {
      this.addResult('Optional Variables', 'pass',
        'All optional variables are configured');
    }

    this.addResult('Available Services', 'pass',
      `Services available: ${validation.availableServices.join(', ') || 'None'}`);
  }

  private async validateDatabaseSchema(): Promise<void> {
    console.log('🗄️ Validating Database Schema...');

    try {
      if (!isTestServiceAvailable('database')) {
        this.addResult('Database Connection', 'warning',
          'Database credentials not available - skipping database tests');
        return;
      }

      const supabase = await createTestSupabaseClient();

      // Test basic connectivity
      const { data, error } = await supabase
        .from('tools')
        .select('id')
        .limit(1);

      if (error) {
        this.addResult('Database Connectivity', 'fail', 
          `Database connection failed: ${error.message}`);
        return;
      }

      this.addResult('Database Connectivity', 'pass', 
        'Successfully connected to database');

      // Check for enhanced AI system tables
      const requiredTables = [
        'tools',
        'ai_generation_jobs',
        'media_assets',
        'editorial_reviews',
        'bulk_processing_jobs',
        'system_configuration'
      ];

      for (const tableName of requiredTables) {
        try {
          const { error: tableError } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);

          if (tableError && !tableError.message.includes('does not exist')) {
            this.addResult(`Table: ${tableName}`, 'fail', 
              `Table access failed: ${tableError.message}`);
          } else if (tableError && tableError.message.includes('does not exist')) {
            this.addResult(`Table: ${tableName}`, 'warning', 
              'Table does not exist - may need migration');
          } else {
            this.addResult(`Table: ${tableName}`, 'pass', 
              'Table exists and accessible');
          }
        } catch (error: any) {
          this.addResult(`Table: ${tableName}`, 'fail', 
            `Table validation failed: ${error.message}`);
        }
      }

    } catch (error: any) {
      this.addResult('Database Schema', 'fail', 
        `Database validation failed: ${error.message}`);
    }
  }

  private async validateFileStructure(): Promise<void> {
    console.log('📁 Validating File Structure...');

    const fs = await import('fs/promises');
    const path = await import('path');

    const requiredFiles = [
      // Core AI system files
      'src/lib/ai/index.ts',
      'src/lib/ai/content-generator.ts',
      'src/lib/ai/model-selector.ts',
      'src/lib/ai/providers/openai-client.ts',
      'src/lib/ai/providers/openrouter-client.ts',
      
      // Scraping system files
      'src/lib/scraping/scrape-do-client.ts',
      'src/lib/scraping/content-processor.ts',
      'src/lib/scraping/cost-optimizer.ts',
      
      // Job processing files
      'src/lib/jobs/enhanced-queue.ts',
      'src/lib/jobs/job-manager.ts',
      'src/lib/jobs/progress-tracker.ts',
      
      // Configuration files
      'src/lib/config/configuration-manager.ts',
      'src/lib/config/encryption.ts',
      
      // Error handling files
      'src/lib/error-handling/error-manager.ts',
      'src/lib/error-handling/recovery-strategies.ts',
      
      // Migration files
      'src/lib/migration/data-migration-executor.ts',
      'src/lib/migration/rollback-manager.ts'
    ];

    for (const filePath of requiredFiles) {
      try {
        const fullPath = path.join(process.cwd(), filePath);
        await fs.access(fullPath);
        this.addResult(`File: ${filePath}`, 'pass', 'File exists');
      } catch (error) {
        this.addResult(`File: ${filePath}`, 'fail', 'File missing');
      }
    }

    // Check for test files
    const testFiles = [
      'tests/integration/enhanced-ai-system.test.ts',
      'tests/performance/load-testing.ts',
      'tests/e2e/admin-workflows.test.ts',
      'tests/api/ai-providers.test.ts',
      'tests/migration/rollback-validation.test.ts'
    ];

    for (const testFile of testFiles) {
      try {
        const fullPath = path.join(process.cwd(), testFile);
        await fs.access(fullPath);
        this.addResult(`Test: ${testFile}`, 'pass', 'Test file exists');
      } catch (error) {
        this.addResult(`Test: ${testFile}`, 'fail', 'Test file missing');
      }
    }
  }

  private async validateTypeScriptCompilation(): Promise<void> {
    console.log('🔧 Validating TypeScript Compilation...');

    try {
      // Check if TypeScript config exists
      const fs = await import('fs/promises');
      const path = await import('path');

      const tsconfigPath = path.join(process.cwd(), 'tsconfig.json');
      await fs.access(tsconfigPath);

      this.addResult('TypeScript Config', 'pass',
        'tsconfig.json exists');

      // Try to import and validate key TypeScript files
      const keyFiles = [
        'src/lib/ai/index.ts',
        'src/lib/scraping/scrape-do-client.ts',
        'src/lib/jobs/job-manager.ts'
      ];

      for (const file of keyFiles) {
        try {
          const filePath = path.join(process.cwd(), file);
          const content = await fs.readFile(filePath, 'utf-8');

          // Basic TypeScript syntax validation
          const hasTypeAnnotations = content.includes(': ') || content.includes('interface ') || content.includes('type ');
          const hasImports = content.includes('import ') || content.includes('export ');

          if (hasTypeAnnotations && hasImports) {
            this.addResult(`TypeScript: ${file}`, 'pass',
              'File has proper TypeScript syntax');
          } else {
            this.addResult(`TypeScript: ${file}`, 'warning',
              'File may have TypeScript issues');
          }
        } catch (error: any) {
          this.addResult(`TypeScript: ${file}`, 'fail',
            `File validation failed: ${error.message}`);
        }
      }

    } catch (error: any) {
      this.addResult('TypeScript Compilation', 'warning',
        `TypeScript validation skipped: ${error.message}`);
    }
  }

  private async validateCoreModules(): Promise<void> {
    console.log('⚙️ Validating Core Modules...');

    const modules = [
      {
        name: 'AI Content Generator',
        path: '../src/lib/ai/content-generator',
        className: 'AIContentGenerator'
      },
      {
        name: 'Model Selector',
        path: '../src/lib/ai/model-selector',
        className: 'ModelSelector'
      },
      {
        name: 'Scrape.do Client',
        path: '../src/lib/scraping/scrape-do-client',
        className: 'ScrapeDoClient'
      },
      {
        name: 'Cost Optimizer',
        path: '../src/lib/scraping/cost-optimizer',
        className: 'CostOptimizer'
      },
      {
        name: 'Job Manager',
        path: '../src/lib/jobs/job-manager',
        className: 'JobManager'
      },
      {
        name: 'Configuration Manager',
        path: '../src/lib/config/configuration-manager',
        className: 'ConfigurationManager'
      },
      {
        name: 'Error Manager',
        path: '../src/lib/error-handling/error-manager',
        className: 'ErrorManager'
      }
    ];

    for (const module of modules) {
      try {
        const imported = await import(module.path);
        
        if (imported[module.className]) {
          this.addResult(`Module: ${module.name}`, 'pass', 
            'Module imports successfully');
        } else {
          this.addResult(`Module: ${module.name}`, 'fail', 
            `Class ${module.className} not found in module`);
        }
      } catch (error: any) {
        this.addResult(`Module: ${module.name}`, 'fail', 
          `Module import failed: ${error.message}`);
      }
    }
  }

  private async validateMigrationSystem(): Promise<void> {
    console.log('🔄 Validating Migration System...');

    try {
      // Check migration files
      const fs = await import('fs/promises');
      const path = await import('path');

      const migrationFiles = [
        'src/lib/database/migrations/001_enhanced_ai_system_schema.sql',
        'src/lib/database/migrations/001_enhanced_ai_system_schema_rollback.sql'
      ];

      for (const migrationFile of migrationFiles) {
        try {
          const fullPath = path.join(process.cwd(), migrationFile);
          await fs.access(fullPath);
          this.addResult(`Migration: ${migrationFile}`, 'pass', 'Migration file exists');
        } catch (error) {
          this.addResult(`Migration: ${migrationFile}`, 'warning', 'Migration file missing');
        }
      }

      // Test migration executor
      try {
        const { DataMigrationExecutor } = await import('../src/lib/migration/data-migration-executor');
        const executor = new DataMigrationExecutor();

        this.addResult('Migration Executor', 'pass',
          'Migration executor loads successfully');
      } catch (error: any) {
        this.addResult('Migration Executor', 'fail',
          `Migration executor failed: ${error.message}`);
      }

      // Test rollback manager
      try {
        const { RollbackManager } = await import('../src/lib/migration/rollback-manager');
        const rollbackManager = new RollbackManager();

        this.addResult('Rollback Manager', 'pass',
          'Rollback manager loads successfully');
      } catch (error: any) {
        this.addResult('Rollback Manager', 'fail',
          `Rollback manager failed: ${error.message}`);
      }

    } catch (error: any) {
      this.addResult('Migration System', 'fail', 
        `Migration validation failed: ${error.message}`);
    }
  }

  private addResult(component: string, status: 'pass' | 'fail' | 'warning', message: string, details?: any): void {
    this.results.push({ component, status, message, details });
    
    const icon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
    console.log(`  ${icon} ${component}: ${message}`);
  }

  private generateValidationReport(): void {
    console.log('\n📊 SYSTEM VALIDATION REPORT');
    console.log('==================================================');

    const passed = this.results.filter(r => r.status === 'pass').length;
    const failed = this.results.filter(r => r.status === 'fail').length;
    const warnings = this.results.filter(r => r.status === 'warning').length;
    const total = this.results.length;

    console.log(`\n📈 SUMMARY:`);
    console.log(`   Total Checks: ${total}`);
    console.log(`   Passed: ${passed} (${Math.round((passed / total) * 100)}%)`);
    console.log(`   Failed: ${failed} (${Math.round((failed / total) * 100)}%)`);
    console.log(`   Warnings: ${warnings} (${Math.round((warnings / total) * 100)}%)`);

    if (failed > 0) {
      console.log('\n❌ FAILED CHECKS:');
      this.results
        .filter(r => r.status === 'fail')
        .forEach(r => console.log(`   • ${r.component}: ${r.message}`));
    }

    if (warnings > 0) {
      console.log('\n⚠️ WARNINGS:');
      this.results
        .filter(r => r.status === 'warning')
        .forEach(r => console.log(`   • ${r.component}: ${r.message}`));
    }

    console.log('\n🎯 OVERALL STATUS:');
    if (failed === 0) {
      console.log('✅ SYSTEM VALIDATION PASSED');
      console.log('   Enhanced AI system core components are properly implemented');
    } else if (failed <= 2) {
      console.log('⚠️ SYSTEM VALIDATION MOSTLY PASSED');
      console.log('   Minor issues detected, but core system is functional');
    } else {
      console.log('❌ SYSTEM VALIDATION FAILED');
      console.log('   Significant issues detected, review failed components');
    }

    console.log('\n💡 NEXT STEPS:');
    if (failed === 0 && warnings === 0) {
      console.log('   🚀 System is ready for comprehensive testing with API keys');
      console.log('   🔧 Configure environment variables for full testing');
    } else {
      console.log('   🔧 Address failed checks and warnings');
      console.log('   🧪 Re-run validation after fixes');
    }

    console.log('==================================================\n');
  }
}

// Main execution
async function main(): Promise<void> {
  const validator = new SystemValidator();
  await validator.validateSystem();
}

// Export for use in other files
export { SystemValidator, ValidationResult };

// Main execution when run directly
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ System validation failed:', error);
    process.exit(1);
  });
}
