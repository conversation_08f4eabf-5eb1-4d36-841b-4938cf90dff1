// Test AI Content Generation
async function testAIGeneration() {
  try {
    // First, test web scraping
    console.log('🔍 Testing web scraping...');
    const scrapeResponse = await fetch('http://localhost:3000/api/scrape', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: 'https://openai.com/chatgpt',
        options: {
          captureScreenshot: true,
          extractImages: true
        }
      })
    });
    
    const scrapeData = await scrapeResponse.json();
    console.log('✅ Scraping successful:', scrapeData.success);
    
    if (scrapeData.success) {
      // Test AI content generation
      console.log('🤖 Testing AI content generation...');
      const aiResponse = await fetch('http://localhost:3000/api/generate-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          url: 'https://openai.com/chatgpt',
          scrapedData: scrapeData.data
        })
      });
      
      const aiData = await aiResponse.json();
      console.log('✅ AI Generation successful:', aiData.success);
      
      if (aiData.success) {
        console.log('📝 Generated content preview:');
        console.log('- Description:', aiData.data.aiContent.toolDescription?.substring(0, 100) + '...');
        console.log('- Features count:', aiData.data.aiContent.features?.length || 0);
        console.log('- Pricing type:', aiData.data.aiContent.pricingType?.type);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testAIGeneration();
