import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { getJobQueue } from '@/lib/jobs/queue';
import { JobStatus } from '@/lib/jobs/types';

interface HealthCheck {
  name: string;
  status: 'healthy' | 'warning' | 'error';
  message: string;
  responseTime: number;
  details?: any;
}

interface HealthResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  uptime: number;
  checks: HealthCheck[];
  summary: {
    healthy: number;
    warnings: number;
    errors: number;
  };
}

export async function GET(request: NextRequest): Promise<NextResponse> {
  const startTime = Date.now();
  const checks: HealthCheck[] = [];

  // Database Health Check
  await checkDatabase(checks);
  
  // Job Queue Health Check
  await checkJobQueue(checks);
  
  // OpenAI API Health Check (lightweight)
  await checkOpenAI(checks);
  
  // Email Service Health Check
  await checkEmailService(checks);

  // Calculate overall status
  const healthy = checks.filter(c => c.status === 'healthy').length;
  const warnings = checks.filter(c => c.status === 'warning').length;
  const errors = checks.filter(c => c.status === 'error').length;

  let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
  if (errors > 0) {
    overallStatus = 'unhealthy';
  } else if (warnings > 0) {
    overallStatus = 'degraded';
  }

  const response: HealthResponse = {
    status: overallStatus,
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    uptime: process.uptime(),
    checks,
    summary: {
      healthy,
      warnings,
      errors,
    },
  };

  const statusCode = overallStatus === 'healthy' ? 200 : 
                     overallStatus === 'degraded' ? 200 : 503;

  return NextResponse.json(response, { status: statusCode });
}

async function checkDatabase(checks: HealthCheck[]): Promise<void> {
  const start = Date.now();
  
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Simple query to test connectivity
    const { data, error } = await supabase
      .from('categories')
      .select('count')
      .limit(1);

    const responseTime = Date.now() - start;

    if (error) {
      checks.push({
        name: 'database',
        status: 'error',
        message: `Database query failed: ${error.message}`,
        responseTime,
      });
    } else {
      checks.push({
        name: 'database',
        status: 'healthy',
        message: 'Database connection successful',
        responseTime,
      });
    }
  } catch (error) {
    const responseTime = Date.now() - start;
    checks.push({
      name: 'database',
      status: 'error',
      message: `Database connection failed: ${error}`,
      responseTime,
    });
  }
}

async function checkJobQueue(checks: HealthCheck[]): Promise<void> {
  const start = Date.now();
  
  try {
    if (process.env.JOB_QUEUE_ENABLED !== 'true') {
      checks.push({
        name: 'job_queue',
        status: 'warning',
        message: 'Job queue is disabled',
        responseTime: Date.now() - start,
      });
      return;
    }

    const queue = getJobQueue();
    const jobs = await queue.getJobs();
    const responseTime = Date.now() - start;

    const pendingJobs = jobs.filter(job => job.status === JobStatus.PENDING).length;
    const failedJobs = jobs.filter(job => job.status === JobStatus.FAILED).length;
    const processingJobs = jobs.filter(job => job.status === JobStatus.PROCESSING).length;

    let status: 'healthy' | 'warning' | 'error' = 'healthy';
    let message = `Job queue operational (${jobs.length} total jobs)`;

    // Check for high failure rate
    if (jobs.length > 0 && failedJobs / jobs.length > 0.2) {
      status = 'error';
      message = `High job failure rate: ${failedJobs}/${jobs.length} failed`;
    } else if (failedJobs > 10) {
      status = 'warning';
      message = `${failedJobs} failed jobs detected`;
    } else if (pendingJobs > 50) {
      status = 'warning';
      message = `High job backlog: ${pendingJobs} pending jobs`;
    }

    checks.push({
      name: 'job_queue',
      status,
      message,
      responseTime,
      details: {
        total: jobs.length,
        pending: pendingJobs,
        processing: processingJobs,
        failed: failedJobs,
      },
    });
  } catch (error) {
    const responseTime = Date.now() - start;
    checks.push({
      name: 'job_queue',
      status: 'error',
      message: `Job queue check failed: ${error}`,
      responseTime,
    });
  }
}

async function checkOpenAI(checks: HealthCheck[]): Promise<void> {
  const start = Date.now();
  
  try {
    if (!process.env.OPENAI_API_KEY) {
      checks.push({
        name: 'openai_api',
        status: 'warning',
        message: 'OpenAI API key not configured',
        responseTime: Date.now() - start,
      });
      return;
    }

    if (process.env.CONTENT_GENERATION_ENABLED !== 'true') {
      checks.push({
        name: 'openai_api',
        status: 'warning',
        message: 'Content generation disabled',
        responseTime: Date.now() - start,
      });
      return;
    }

    // For health checks, we just verify the API key format
    // Actual API calls are expensive and not suitable for frequent health checks
    const apiKey = process.env.OPENAI_API_KEY;
    const isValidFormat = apiKey.startsWith('sk-') && apiKey.length > 20;
    
    const responseTime = Date.now() - start;

    if (isValidFormat) {
      checks.push({
        name: 'openai_api',
        status: 'healthy',
        message: 'OpenAI API configured',
        responseTime,
        details: {
          model: process.env.OPENAI_MODEL || 'gpt-4o',
          contentGeneration: process.env.CONTENT_GENERATION_ENABLED === 'true',
        },
      });
    } else {
      checks.push({
        name: 'openai_api',
        status: 'error',
        message: 'Invalid OpenAI API key format',
        responseTime,
      });
    }
  } catch (error) {
    const responseTime = Date.now() - start;
    checks.push({
      name: 'openai_api',
      status: 'error',
      message: `OpenAI API check failed: ${error}`,
      responseTime,
    });
  }
}

async function checkEmailService(checks: HealthCheck[]): Promise<void> {
  const start = Date.now();
  
  try {
    const hasSmtpConfig = !!(
      process.env.SMTP_HOST && 
      process.env.SMTP_USER && 
      process.env.SMTP_PASS
    );

    const responseTime = Date.now() - start;

    if (!hasSmtpConfig) {
      checks.push({
        name: 'email_service',
        status: 'warning',
        message: 'Email service not configured',
        responseTime,
      });
    } else {
      checks.push({
        name: 'email_service',
        status: 'healthy',
        message: 'Email service configured',
        responseTime,
        details: {
          host: process.env.SMTP_HOST,
          port: process.env.SMTP_PORT || '587',
          adminEmail: process.env.ADMIN_EMAIL || 'not-set',
        },
      });
    }
  } catch (error) {
    const responseTime = Date.now() - start;
    checks.push({
      name: 'email_service',
      status: 'error',
      message: `Email service check failed: ${error}`,
      responseTime,
    });
  }
}
