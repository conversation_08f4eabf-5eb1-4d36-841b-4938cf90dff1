-- Enhanced AI System Database Schema Rollback
-- Migration: 001_enhanced_ai_system_schema_rollback
-- Description: Rollback script for Enhanced AI System schema changes
-- WARNING: This will remove all data from the new tables

-- =====================================================
-- ROLLBACK CONFIRMATION
-- =====================================================

-- This rollback script will:
-- 1. Drop all new tables created by the enhanced AI system migration
-- 2. Remove new columns added to existing tables
-- 3. Drop all related indexes and triggers
-- 4. Remove configuration entries

-- =====================================================
-- DROP TRIGGERS
-- =====================================================

DROP TRIGGER IF EXISTS update_ai_generation_jobs_updated_at ON ai_generation_jobs;
DROP TRIGGER IF EXISTS update_media_assets_updated_at ON media_assets;
DROP TRIGGER IF EXISTS update_editorial_reviews_updated_at ON editorial_reviews;
DROP TRIGGER IF EXISTS update_bulk_processing_jobs_updated_at ON bulk_processing_jobs;
DROP TRIGGER IF EXISTS update_system_configuration_updated_at ON system_configuration;

-- =====================================================
-- REMOVE NEW COLUMNS FROM EXISTING TABLES
-- =====================================================

-- Remove new columns from tools table
ALTER TABLE tools DROP COLUMN IF EXISTS scraped_data;
ALTER TABLE tools DROP COLUMN IF EXISTS ai_generation_status;
ALTER TABLE tools DROP COLUMN IF EXISTS last_scraped_at;
ALTER TABLE tools DROP COLUMN IF EXISTS editorial_review_id;
ALTER TABLE tools DROP COLUMN IF EXISTS ai_generation_job_id;
ALTER TABLE tools DROP COLUMN IF EXISTS submission_type;
ALTER TABLE tools DROP COLUMN IF EXISTS submission_source;
ALTER TABLE tools DROP COLUMN IF EXISTS content_quality_score;
ALTER TABLE tools DROP COLUMN IF EXISTS last_ai_update;

-- =====================================================
-- DROP NEW TABLES (in reverse dependency order)
-- =====================================================

-- Drop system_configuration table
DROP TABLE IF EXISTS system_configuration CASCADE;

-- Drop bulk_processing_jobs table
DROP TABLE IF EXISTS bulk_processing_jobs CASCADE;

-- Drop editorial_reviews table
DROP TABLE IF EXISTS editorial_reviews CASCADE;

-- Drop media_assets table
DROP TABLE IF EXISTS media_assets CASCADE;

-- Drop ai_generation_jobs table
DROP TABLE IF EXISTS ai_generation_jobs CASCADE;

-- =====================================================
-- DROP FUNCTIONS
-- =====================================================

DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- =====================================================
-- ROLLBACK COMPLETION LOG
-- =====================================================

-- Note: Since we're dropping system_configuration table, we can't log the rollback there
-- This would need to be logged in a separate audit table if one exists

-- Migration rollback completed
-- All Enhanced AI System schema changes have been reverted
