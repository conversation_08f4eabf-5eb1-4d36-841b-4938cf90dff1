/**
 * Editorial Review API
 * 
 * API endpoints for managing editorial reviews, manual review workflow,
 * and editorial text with exact format requirements.
 */

import { NextRequest, NextResponse } from 'next/server';
import { ManualReview } from '../../../../lib/content-generation/manual-review';
import { EditorialControls } from '../../../../lib/content-generation/editorial-controls';

const manualReview = new ManualReview();
const editorialControls = new EditorialControls();

/**
 * GET /api/editorial/review?toolId=xxx
 * Get review data for a specific tool
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const toolId = searchParams.get('toolId');
    const action = searchParams.get('action');

    if (action === 'queue') {
      // Get review queue with filters
      const status = searchParams.get('status');
      const reviewer = searchParams.get('reviewer');
      const priority = searchParams.get('priority');
      
      const filters: any = {};
      if (status) filters.status = status;
      if (reviewer) filters.reviewer = reviewer;
      if (priority) filters.priority = priority;

      const queue = await manualReview.getReviewQueue(filters);
      
      return NextResponse.json({
        success: true,
        data: queue
      });
    }

    if (action === 'workflow') {
      // Get editorial workflow data
      const workflowData = await editorialControls.getWorkflowData();
      
      return NextResponse.json({
        success: true,
        data: workflowData
      });
    }

    if (!toolId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required parameter: toolId' 
        },
        { status: 400 }
      );
    }

    // Get review data for specific tool
    const reviewData = await manualReview.getReviewData(toolId);

    return NextResponse.json({
      success: true,
      data: reviewData
    });

  } catch (error: any) {
    console.error('Editorial review GET API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/editorial/review
 * Submit editorial review decision
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const { toolId, reviewerId, decision } = body;
    if (!toolId || !reviewerId || !decision) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: toolId, reviewerId, decision' 
        },
        { status: 400 }
      );
    }

    // Validate decision
    const validDecisions = ['approve', 'reject', 'request_changes'];
    if (!validDecisions.includes(decision)) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Invalid decision. Must be one of: ${validDecisions.join(', ')}` 
        },
        { status: 400 }
      );
    }

    // Validate editorial text format if provided
    if (body.editorialText) {
      try {
        editorialControls.validateEditorialTextFormat(body.editorialText);
      } catch (error: any) {
        return NextResponse.json(
          { 
            success: false, 
            error: `Editorial text format error: ${error.message}` 
          },
          { status: 400 }
        );
      }
    }

    console.log(`Processing editorial review for tool: ${toolId}`);
    console.log(`Decision: ${decision} by reviewer: ${reviewerId}`);

    // Submit review
    const result = await manualReview.submitReview({
      toolId,
      reviewerId,
      decision,
      reviewNotes: body.reviewNotes,
      featuredDate: body.featuredDate,
      editorialText: body.editorialText,
      contentModifications: body.contentModifications,
      qualityOverride: body.qualityOverride
    });

    console.log(`Editorial review submitted successfully for tool: ${toolId}`);

    return NextResponse.json({
      success: true,
      data: result,
      message: `Review ${decision} submitted successfully`
    });

  } catch (error: any) {
    console.error('Editorial review POST API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/editorial/review
 * Bulk operations or editorial text preview
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    if (!action) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required field: action' 
        },
        { status: 400 }
      );
    }

    switch (action) {
      case 'preview_editorial_text':
        // Preview editorial text with validation
        const { featuredDate } = body;
        if (!featuredDate) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Missing required field: featuredDate' 
            },
            { status: 400 }
          );
        }

        const preview = manualReview.previewEditorialText(featuredDate);
        
        return NextResponse.json({
          success: true,
          data: preview
        });

      case 'validate_editorial_text':
        // Validate custom editorial text
        const { editorialText } = body;
        if (!editorialText) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Missing required field: editorialText' 
            },
            { status: 400 }
          );
        }

        const validation = manualReview.validateEditorialText(editorialText);
        
        return NextResponse.json({
          success: true,
          data: validation
        });

      case 'bulk_approve':
        // Bulk approve tools
        const { toolIds, reviewerId, featuredDate: bulkFeaturedDate, notes } = body;
        if (!toolIds || !Array.isArray(toolIds) || !reviewerId || !bulkFeaturedDate) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Missing required fields: toolIds (array), reviewerId, featuredDate' 
            },
            { status: 400 }
          );
        }

        console.log(`Processing bulk approval for ${toolIds.length} tools by reviewer: ${reviewerId}`);

        const bulkResult = await manualReview.bulkApprove(
          toolIds,
          reviewerId,
          bulkFeaturedDate,
          notes
        );

        console.log(`Bulk approval completed: ${bulkResult.success.length} successful, ${bulkResult.failed.length} failed`);

        return NextResponse.json({
          success: true,
          data: bulkResult,
          message: `Bulk approval completed: ${bulkResult.success.length} successful, ${bulkResult.failed.length} failed`
        });

      default:
        return NextResponse.json(
          { 
            success: false, 
            error: `Unknown action: ${action}` 
          },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Editorial review PUT API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
