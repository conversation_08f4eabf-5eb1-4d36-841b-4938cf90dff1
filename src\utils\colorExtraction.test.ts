/**
 * Test utility for verifying dynamic border color extraction
 * This demonstrates how the CategoryCard component extracts border colors
 * from button color classes to create visual consistency.
 */

// Mock function that replicates the getBorderColor logic from CategoryCard
function extractBorderColor(colorClass: string): string {
  // Extract the base color from the background class (e.g., "bg-sky-500" -> "sky-500")
  const bgColorMatch = colorClass.match(/bg-(\w+-\d+)/);
  
  if (bgColorMatch) {
    const colorName = bgColorMatch[1]; // e.g., "sky-500", "green-500", "pink-500", "yellow-600"
    return `border-${colorName}`;
  }
  
  // Fallback to default zinc border if extraction fails
  return 'border-zinc-700';
}

// Test cases based on actual category button colors from constants.ts
const testCases = [
  {
    category: 'Writing Tools',
    buttonColorClass: 'bg-sky-500 hover:bg-sky-400',
    expectedBorderClass: 'border-sky-500',
  },
  {
    category: 'Image Generators',
    buttonColorClass: 'bg-green-500 hover:bg-green-400',
    expectedBorderClass: 'border-green-500',
  },
  {
    category: 'Chatbots',
    buttonColorClass: 'bg-pink-500 hover:bg-pink-400',
    expectedBorderClass: 'border-pink-500',
  },
  {
    category: 'Dev Tools',
    buttonColorClass: 'bg-yellow-600 hover:bg-yellow-500',
    expectedBorderClass: 'border-yellow-600',
  },
  {
    category: 'Invalid Color',
    buttonColorClass: 'invalid-class-name',
    expectedBorderClass: 'border-zinc-700', // fallback
  },
  {
    category: 'Empty String',
    buttonColorClass: '',
    expectedBorderClass: 'border-zinc-700', // fallback
  },
];

// Run tests
console.log('🧪 Testing Dynamic Border Color Extraction\n');

testCases.forEach(({ category, buttonColorClass, expectedBorderClass }) => {
  const result = extractBorderColor(buttonColorClass);
  const passed = result === expectedBorderClass;
  
  console.log(`${passed ? '✅' : '❌'} ${category}`);
  console.log(`   Input: "${buttonColorClass}"`);
  console.log(`   Expected: "${expectedBorderClass}"`);
  console.log(`   Got: "${result}"`);
  console.log('');
});

// Export for potential use in actual tests
export { extractBorderColor, testCases };

/**
 * Usage in CategoryCard component:
 * 
 * const getBorderColor = useCallback(() => {
 *   return extractBorderColor(category.seeAllButton.colorClass);
 * }, [category.seeAllButton.colorClass]);
 * 
 * const borderColorClass = getBorderColor();
 * 
 * <article className={`bg-zinc-800 border ${borderColorClass} rounded-lg ...`}>
 */
