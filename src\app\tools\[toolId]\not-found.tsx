'use client';

import Link from 'next/link';
import { ArrowLeft, Search, Home } from 'lucide-react';

export default function ToolNotFound() {
  return (
    <div className="w-full">
      <div className="mx-auto px-4 py-16" style={{ maxWidth: 'var(--container-width)' }}>
        <div className="text-center">
          
          {/* 404 Icon */}
          <div className="mb-8">
            <div className="inline-flex items-center justify-center w-24 h-24 bg-zinc-800 border border-zinc-700 rounded-full">
              <Search size={40} className="text-gray-400" />
            </div>
          </div>
          
          {/* Error Message */}
          <h1 className="text-4xl font-bold text-white mb-4">Tool Not Found</h1>
          <p className="text-gray-400 text-lg mb-8 max-w-md mx-auto">
            Sorry, we couldn't find the AI tool you're looking for. It may have been moved or doesn't exist.
          </p>
          
          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="inline-flex items-center gap-2 bg-orange-500 hover:bg-orange-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
              style={{
                backgroundColor: 'rgb(255, 150, 0)',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
              }}
            >
              <Home size={18} />
              Back to Home
            </Link>
            
            <button
              onClick={() => window.history.back()}
              className="inline-flex items-center gap-2 bg-zinc-700 hover:bg-zinc-600 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
            >
              <ArrowLeft size={18} />
              Go Back
            </button>
          </div>
          
          {/* Suggestions */}
          <div className="mt-12 p-6 bg-zinc-800 border border-zinc-700 rounded-lg max-w-lg mx-auto">
            <h3 className="text-white font-medium mb-4">What you can do:</h3>
            <ul className="text-gray-400 text-sm space-y-2 text-left">
              <li>• Check the URL for any typos</li>
              <li>• Browse our AI tools directory from the homepage</li>
              <li>• Use the search function to find specific tools</li>
              <li>• Explore different AI tool categories</li>
            </ul>
          </div>
          
        </div>
      </div>
    </div>
  );
}
