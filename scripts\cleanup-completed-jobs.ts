#!/usr/bin/env tsx

/**
 * Job Cleanup Script
 * 
 * Cleans up old completed and failed jobs to prevent memory issues
 * - <PERSON><PERSON><PERSON> completed jobs older than 24 hours
 * - <PERSON>moves failed jobs older than 7 days
 * - Keeps recent jobs for debugging
 */

import { getJobQueue } from '../src/lib/jobs/queue';
import { JobStatus } from '../src/lib/jobs/types';

interface CleanupStats {
  totalJobs: number;
  completedRemoved: number;
  failedRemoved: number;
  cancelledRemoved: number;
  kept: number;
}

class JobCleaner {
  async cleanup(): Promise<CleanupStats> {
    console.log('🧹 Starting job cleanup...\n');

    const queue = getJobQueue();
    const allJobs = await queue.getJobs();
    
    const stats: CleanupStats = {
      totalJobs: allJobs.length,
      completedRemoved: 0,
      failedRemoved: 0,
      cancelledRemoved: 0,
      kept: 0,
    };

    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    console.log(`📊 Found ${allJobs.length} total jobs`);
    console.log(`🕐 Current time: ${now.toISOString()}`);
    console.log(`📅 Cleanup thresholds:`);
    console.log(`   - Completed jobs older than: ${oneDayAgo.toISOString()}`);
    console.log(`   - Failed jobs older than: ${oneWeekAgo.toISOString()}\n`);

    for (const job of allJobs) {
      let shouldRemove = false;
      let reason = '';

      // Remove completed jobs older than 24 hours
      if (job.status === JobStatus.COMPLETED && job.completedAt && job.completedAt < oneDayAgo) {
        shouldRemove = true;
        reason = 'completed > 24h';
        stats.completedRemoved++;
      }
      // Remove failed jobs older than 7 days
      else if (job.status === JobStatus.FAILED && job.updatedAt < oneWeekAgo) {
        shouldRemove = true;
        reason = 'failed > 7d';
        stats.failedRemoved++;
      }
      // Remove cancelled jobs older than 24 hours
      else if (job.status === JobStatus.CANCELLED && job.updatedAt < oneDayAgo) {
        shouldRemove = true;
        reason = 'cancelled > 24h';
        stats.cancelledRemoved++;
      }

      if (shouldRemove) {
        try {
          await queue.removeJob(job.id);
          console.log(`🗑️  Removed job ${job.id} (${job.type}) - ${reason}`);
        } catch (error) {
          console.error(`❌ Failed to remove job ${job.id}:`, error);
        }
      } else {
        stats.kept++;
      }
    }

    return stats;
  }

  printStats(stats: CleanupStats): void {
    console.log('\n📈 Cleanup Summary:');
    console.log(`   Total jobs processed: ${stats.totalJobs}`);
    console.log(`   Completed jobs removed: ${stats.completedRemoved}`);
    console.log(`   Failed jobs removed: ${stats.failedRemoved}`);
    console.log(`   Cancelled jobs removed: ${stats.cancelledRemoved}`);
    console.log(`   Jobs kept: ${stats.kept}`);
    
    const totalRemoved = stats.completedRemoved + stats.failedRemoved + stats.cancelledRemoved;
    console.log(`\n✅ Cleanup complete! Removed ${totalRemoved} jobs, kept ${stats.kept} jobs.`);
  }

  async getJobStatistics(): Promise<void> {
    console.log('\n📊 Current Job Statistics:');
    
    const queue = getJobQueue();
    const jobs = await queue.getJobs();
    
    const statusCounts: Record<JobStatus, number> = {
      [JobStatus.PENDING]: 0,
      [JobStatus.PROCESSING]: 0,
      [JobStatus.COMPLETED]: 0,
      [JobStatus.FAILED]: 0,
      [JobStatus.RETRYING]: 0,
      [JobStatus.CANCELLED]: 0,
      [JobStatus.PAUSED]: 0,
      [JobStatus.STOPPING]: 0,
      [JobStatus.STOPPED]: 0,
    };

    const typeCounts: Record<string, number> = {};
    
    jobs.forEach(job => {
      statusCounts[job.status]++;
      typeCounts[job.type] = (typeCounts[job.type] || 0) + 1;
    });

    console.log('\nBy Status:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      if (count > 0) {
        const icon = status === JobStatus.COMPLETED ? '✅' :
                     status === JobStatus.FAILED ? '❌' :
                     status === JobStatus.PROCESSING ? '🔄' :
                     status === JobStatus.PENDING ? '⏳' :
                     status === JobStatus.RETRYING ? '🔁' : '⏹️';
        console.log(`   ${icon} ${status}: ${count}`);
      }
    });

    console.log('\nBy Type:');
    Object.entries(typeCounts).forEach(([type, count]) => {
      console.log(`   📋 ${type}: ${count}`);
    });

    // Age analysis
    const now = new Date();
    const ageRanges = {
      'last hour': 0,
      'last 24 hours': 0,
      'last week': 0,
      'older': 0,
    };

    jobs.forEach(job => {
      const age = now.getTime() - job.createdAt.getTime();
      const hours = age / (1000 * 60 * 60);
      
      if (hours < 1) {
        ageRanges['last hour']++;
      } else if (hours < 24) {
        ageRanges['last 24 hours']++;
      } else if (hours < 168) { // 7 days
        ageRanges['last week']++;
      } else {
        ageRanges['older']++;
      }
    });

    console.log('\nBy Age:');
    Object.entries(ageRanges).forEach(([range, count]) => {
      if (count > 0) {
        console.log(`   🕐 ${range}: ${count}`);
      }
    });
  }
}

// Command line interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'cleanup';

  const cleaner = new JobCleaner();

  try {
    switch (command) {
      case 'cleanup':
        const stats = await cleaner.cleanup();
        cleaner.printStats(stats);
        break;
        
      case 'stats':
        await cleaner.getJobStatistics();
        break;
        
      case 'help':
        console.log(`
Job Cleanup Utility

Usage:
  npm run cleanup:jobs [command]

Commands:
  cleanup    Clean up old completed and failed jobs (default)
  stats      Show current job statistics
  help       Show this help message

Examples:
  npm run cleanup:jobs           # Clean up old jobs
  npm run cleanup:jobs stats     # Show job statistics
  npm run cleanup:jobs help      # Show help
        `);
        break;
        
      default:
        console.error(`Unknown command: ${command}`);
        console.log('Use "npm run cleanup:jobs help" for usage information');
        process.exit(1);
    }
  } catch (error) {
    console.error('Cleanup failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { JobCleaner };
