/**
 * Rollback Manager
 * 
 * Comprehensive rollback system for data migration with
 * backup restoration, integrity verification, and recovery procedures.
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync, existsSync, readdirSync } from 'fs';
import { join } from 'path';
import { BackupData } from './data-migration-executor';

export interface RollbackConfig {
  backupDirectory: string;
  verifyIntegrity: boolean;
  preserveNewData: boolean;
  batchSize: number;
}

export interface RollbackResult {
  success: boolean;
  backupUsed: string;
  restoredTables: string[];
  restoredRecords: number;
  errors: string[];
  warnings: string[];
  duration: number;
}

export class RollbackManager {
  private supabase;
  private config: RollbackConfig;
  private startTime: number = 0;

  constructor(config: Partial<RollbackConfig> = {}) {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    
    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing required Supabase environment variables');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
    
    this.config = {
      backupDirectory: join(process.cwd(), 'backups'),
      verifyIntegrity: true,
      preserveNewData: false,
      batchSize: 50,
      ...config
    };
  }

  /**
   * Execute rollback using the most recent backup
   */
  async executeRollback(backupPath?: string): Promise<RollbackResult> {
    this.startTime = Date.now();
    
    const result: RollbackResult = {
      success: false,
      backupUsed: '',
      restoredTables: [],
      restoredRecords: 0,
      errors: [],
      warnings: [],
      duration: 0
    };

    try {
      console.log('🔄 Starting rollback procedure...');

      // Determine backup file to use
      const backupFile = backupPath || await this.findLatestBackup();
      if (!backupFile) {
        throw new Error('No backup file found for rollback');
      }

      result.backupUsed = backupFile;
      console.log(`📦 Using backup: ${backupFile}`);

      // Load and validate backup data
      const backupData = await this.loadBackupData(backupFile);
      await this.validateBackupData(backupData);

      // Create pre-rollback snapshot if preserving new data
      if (this.config.preserveNewData) {
        console.log('📸 Creating pre-rollback snapshot...');
        await this.createPreRollbackSnapshot();
      }

      // Execute rollback by table
      await this.rollbackSystemConfiguration(backupData, result);
      await this.rollbackToolsData(backupData, result);
      await this.rollbackJobsData(backupData, result);
      await this.rollbackMediaAssets(backupData, result);
      await this.rollbackEditorialReviews(backupData, result);
      await this.rollbackBulkJobs(backupData, result);

      // Verify rollback integrity
      if (this.config.verifyIntegrity) {
        console.log('🔍 Verifying rollback integrity...');
        await this.verifyRollbackIntegrity(backupData);
      }

      // Update rollback status
      await this.updateRollbackStatus('completed', backupFile);

      result.success = true;
      result.duration = Date.now() - this.startTime;
      
      console.log(`✅ Rollback completed successfully in ${result.duration}ms`);
      return result;

    } catch (error: any) {
      console.error('❌ Rollback failed:', error);
      result.errors.push(error.message);
      result.duration = Date.now() - this.startTime;
      
      await this.updateRollbackStatus('failed', result.backupUsed, error.message);
      return result;
    }
  }

  /**
   * Find the most recent backup file
   */
  private async findLatestBackup(): Promise<string | null> {
    if (!existsSync(this.config.backupDirectory)) {
      return null;
    }

    const files = readdirSync(this.config.backupDirectory)
      .filter(file => file.startsWith('backup_') && file.endsWith('.json'))
      .sort()
      .reverse();

    return files.length > 0 ? join(this.config.backupDirectory, files[0]) : null;
  }

  /**
   * Load and parse backup data
   */
  private async loadBackupData(backupPath: string): Promise<BackupData> {
    if (!existsSync(backupPath)) {
      throw new Error(`Backup file not found: ${backupPath}`);
    }

    try {
      const backupContent = readFileSync(backupPath, 'utf-8');
      const backupData: BackupData = JSON.parse(backupContent);
      
      if (!backupData.metadata || !backupData.timestamp) {
        throw new Error('Invalid backup file format');
      }

      return backupData;
    } catch (error: any) {
      throw new Error(`Failed to load backup data: ${error.message}`);
    }
  }

  /**
   * Validate backup data integrity
   */
  private async validateBackupData(backupData: BackupData): Promise<void> {
    const requiredFields = ['timestamp', 'version', 'tools', 'metadata'];
    
    for (const field of requiredFields) {
      if (!(field in backupData)) {
        throw new Error(`Backup data missing required field: ${field}`);
      }
    }

    if (!Array.isArray(backupData.tools)) {
      throw new Error('Backup data tools field is not an array');
    }

    console.log(`✅ Backup validation passed: ${backupData.tools.length} tools, ${backupData.metadata.totalRecords} total records`);
  }

  /**
   * Create snapshot before rollback
   */
  private async createPreRollbackSnapshot(): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const snapshotPath = join(this.config.backupDirectory, `pre_rollback_${timestamp}.json`);

    // This would create a snapshot of current state
    // Implementation similar to backup creation in DataMigrationExecutor
    console.log(`📸 Pre-rollback snapshot would be created at: ${snapshotPath}`);
  }

  /**
   * Rollback system configuration
   */
  private async rollbackSystemConfiguration(backupData: BackupData, result: RollbackResult): Promise<void> {
    console.log('⚙️ Rolling back system configuration...');

    if (!backupData.systemConfig || backupData.systemConfig.length === 0) {
      result.warnings.push('No system configuration data in backup');
      return;
    }

    let restoredCount = 0;
    const batchSize = this.config.batchSize;

    for (let i = 0; i < backupData.systemConfig.length; i += batchSize) {
      const batch = backupData.systemConfig.slice(i, i + batchSize);
      
      for (const config of batch) {
        try {
          const { error } = await this.supabase
            .from('system_configuration')
            .upsert({
              config_key: config.config_key,
              config_value: config.config_value,
              config_type: config.config_type,
              is_sensitive: config.is_sensitive,
              is_active: config.is_active,
              description: config.description,
              validation_schema: config.validation_schema,
              updated_by: 'rollback_system',
              version: (config.version || 1) + 1
            });

          if (error) {
            result.warnings.push(`Failed to restore config ${config.config_key}: ${error.message}`);
          } else {
            restoredCount++;
          }
        } catch (error: any) {
          result.warnings.push(`Error restoring config ${config.config_key}: ${error.message}`);
        }
      }
    }

    result.restoredTables.push('system_configuration');
    result.restoredRecords += restoredCount;
    console.log(`✅ Restored ${restoredCount} configuration entries`);
  }

  /**
   * Rollback tools data
   */
  private async rollbackToolsData(backupData: BackupData, result: RollbackResult): Promise<void> {
    console.log('🔧 Rolling back tools data...');

    if (!backupData.tools || backupData.tools.length === 0) {
      result.warnings.push('No tools data in backup');
      return;
    }

    let restoredCount = 0;
    const batchSize = this.config.batchSize;

    for (let i = 0; i < backupData.tools.length; i += batchSize) {
      const batch = backupData.tools.slice(i, i + batchSize);
      
      for (const tool of batch) {
        try {
          const { error } = await this.supabase
            .from('tools')
            .upsert(tool);

          if (error) {
            result.warnings.push(`Failed to restore tool ${tool.id}: ${error.message}`);
          } else {
            restoredCount++;
          }
        } catch (error: any) {
          result.warnings.push(`Error restoring tool ${tool.id}: ${error.message}`);
        }
      }
    }

    result.restoredTables.push('tools');
    result.restoredRecords += restoredCount;
    console.log(`✅ Restored ${restoredCount} tools`);
  }

  /**
   * Rollback jobs data
   */
  private async rollbackJobsData(backupData: BackupData, result: RollbackResult): Promise<void> {
    console.log('📋 Rolling back jobs data...');

    if (!backupData.jobs || backupData.jobs.length === 0) {
      console.log('ℹ️ No jobs data to restore');
      return;
    }

    // Clear existing jobs first
    await this.supabase.from('ai_generation_jobs').delete().neq('id', '00000000-0000-0000-0000-000000000000');

    let restoredCount = 0;
    for (const job of backupData.jobs) {
      try {
        const { error } = await this.supabase
          .from('ai_generation_jobs')
          .insert(job);

        if (error) {
          result.warnings.push(`Failed to restore job ${job.id}: ${error.message}`);
        } else {
          restoredCount++;
        }
      } catch (error: any) {
        result.warnings.push(`Error restoring job ${job.id}: ${error.message}`);
      }
    }

    result.restoredTables.push('ai_generation_jobs');
    result.restoredRecords += restoredCount;
    console.log(`✅ Restored ${restoredCount} jobs`);
  }

  /**
   * Rollback media assets
   */
  private async rollbackMediaAssets(backupData: BackupData, result: RollbackResult): Promise<void> {
    console.log('🖼️ Rolling back media assets...');

    if (!backupData.mediaAssets || backupData.mediaAssets.length === 0) {
      console.log('ℹ️ No media assets to restore');
      return;
    }

    // Clear existing media assets first
    await this.supabase.from('media_assets').delete().neq('id', '00000000-0000-0000-0000-000000000000');

    let restoredCount = 0;
    for (const asset of backupData.mediaAssets) {
      try {
        const { error } = await this.supabase
          .from('media_assets')
          .insert(asset);

        if (error) {
          result.warnings.push(`Failed to restore media asset ${asset.id}: ${error.message}`);
        } else {
          restoredCount++;
        }
      } catch (error: any) {
        result.warnings.push(`Error restoring media asset ${asset.id}: ${error.message}`);
      }
    }

    result.restoredTables.push('media_assets');
    result.restoredRecords += restoredCount;
    console.log(`✅ Restored ${restoredCount} media assets`);
  }

  /**
   * Rollback editorial reviews
   */
  private async rollbackEditorialReviews(backupData: BackupData, result: RollbackResult): Promise<void> {
    console.log('📝 Rolling back editorial reviews...');

    if (!backupData.editorialReviews || backupData.editorialReviews.length === 0) {
      console.log('ℹ️ No editorial reviews to restore');
      return;
    }

    // Clear existing reviews first
    await this.supabase.from('editorial_reviews').delete().neq('id', '00000000-0000-0000-0000-000000000000');

    let restoredCount = 0;
    for (const review of backupData.editorialReviews) {
      try {
        const { error } = await this.supabase
          .from('editorial_reviews')
          .insert(review);

        if (error) {
          result.warnings.push(`Failed to restore editorial review ${review.id}: ${error.message}`);
        } else {
          restoredCount++;
        }
      } catch (error: any) {
        result.warnings.push(`Error restoring editorial review ${review.id}: ${error.message}`);
      }
    }

    result.restoredTables.push('editorial_reviews');
    result.restoredRecords += restoredCount;
    console.log(`✅ Restored ${restoredCount} editorial reviews`);
  }

  /**
   * Rollback bulk jobs
   */
  private async rollbackBulkJobs(backupData: BackupData, result: RollbackResult): Promise<void> {
    console.log('📦 Rolling back bulk jobs...');

    if (!backupData.bulkJobs || backupData.bulkJobs.length === 0) {
      console.log('ℹ️ No bulk jobs to restore');
      return;
    }

    // Clear existing bulk jobs first
    await this.supabase.from('bulk_processing_jobs').delete().neq('id', '00000000-0000-0000-0000-000000000000');

    let restoredCount = 0;
    for (const job of backupData.bulkJobs) {
      try {
        const { error } = await this.supabase
          .from('bulk_processing_jobs')
          .insert(job);

        if (error) {
          result.warnings.push(`Failed to restore bulk job ${job.id}: ${error.message}`);
        } else {
          restoredCount++;
        }
      } catch (error: any) {
        result.warnings.push(`Error restoring bulk job ${job.id}: ${error.message}`);
      }
    }

    result.restoredTables.push('bulk_processing_jobs');
    result.restoredRecords += restoredCount;
    console.log(`✅ Restored ${restoredCount} bulk jobs`);
  }

  /**
   * Verify rollback integrity
   */
  private async verifyRollbackIntegrity(backupData: BackupData): Promise<void> {
    // Verify tools count
    const { count: toolsCount, error: toolsError } = await this.supabase
      .from('tools')
      .select('*', { count: 'exact', head: true });

    if (toolsError) {
      throw new Error(`Failed to verify tools count: ${toolsError.message}`);
    }

    if (toolsCount !== backupData.tools.length) {
      throw new Error(`Tools count mismatch: expected ${backupData.tools.length}, got ${toolsCount}`);
    }

    console.log('✅ Rollback integrity verification passed');
  }

  /**
   * Update rollback status in system configuration
   */
  private async updateRollbackStatus(status: 'completed' | 'failed', backupUsed: string, errorMessage?: string): Promise<void> {
    const statusData = {
      status,
      backupUsed,
      completedAt: new Date().toISOString(),
      duration: Date.now() - this.startTime,
      ...(errorMessage && { error: errorMessage })
    };

    await this.supabase
      .from('system_configuration')
      .upsert({
        config_key: 'rollback_status',
        config_value: statusData,
        config_type: 'system',
        description: 'Latest rollback operation status'
      });
  }

  /**
   * List available backups
   */
  async listAvailableBackups(): Promise<string[]> {
    if (!existsSync(this.config.backupDirectory)) {
      return [];
    }

    return readdirSync(this.config.backupDirectory)
      .filter(file => file.startsWith('backup_') && file.endsWith('.json'))
      .sort()
      .reverse();
  }

  /**
   * Validate rollback capability and backup integrity
   */
  async validateRollback(backupPath?: string): Promise<{
    valid: boolean;
    backupAvailable: boolean;
    backupPath?: string;
    backupAge?: number;
    errors: string[];
    warnings: string[];
  }> {
    const result: {
      valid: boolean;
      backupAvailable: boolean;
      backupPath?: string;
      backupAge?: number;
      errors: string[];
      warnings: string[];
    } = {
      valid: false,
      backupAvailable: false,
      errors: [],
      warnings: []
    };

    try {
      // Find backup file to validate
      const backupFile = backupPath || await this.findLatestBackup();

      if (!backupFile) {
        result.errors.push('No backup file available for rollback');
        return result;
      }

      result.backupAvailable = true;
      result.backupPath = backupFile;

      // Calculate backup age
      const stats = require('fs').statSync(backupFile);
      result.backupAge = Date.now() - stats.mtime.getTime();

      // Validate backup file integrity
      try {
        const backupData = await this.loadBackupData(backupFile);
        await this.validateBackupData(backupData);

        // Check if backup is recent (within 24 hours)
        if (result.backupAge > 24 * 60 * 60 * 1000) {
          result.warnings.push('Backup is older than 24 hours');
        }

        // Validate database connectivity
        const { error: dbError } = await this.supabase
          .from('tools')
          .select('id')
          .limit(1);

        if (dbError) {
          result.errors.push(`Database connectivity issue: ${dbError.message}`);
          return result;
        }

        result.valid = true;
        return result;

      } catch (error: any) {
        result.errors.push(`Backup validation failed: ${error.message}`);
        return result;
      }

    } catch (error: any) {
      result.errors.push(`Rollback validation failed: ${error.message}`);
      return result;
    }
  }
}
