'use client';

import React from 'react';
import Link from 'next/link';
import { ExternalLink, Star, CheckCircle } from 'lucide-react';
import { AITool } from '@/lib/types';
import { ResponsiveImage } from '@/components/ui/ResponsiveImage';
import { Tag } from '@/components/ui/Tag';

interface ToolCardProps {
  tool: AITool;
  showCategory?: boolean;
  showSubcategory?: boolean;
}

export function ToolCard({ tool, showCategory = false, showSubcategory = false }: ToolCardProps) {
  const getPricingDisplay = () => {
    if (!tool.pricing) return null;

    switch (tool.pricing.type) {
      case 'free':
        return <span className="text-xs font-medium" style={{ color: '#22C55E' }}>Free</span>;
      case 'freemium':
        return <span className="text-xs font-medium" style={{ color: '#3B82F6' }}>Freemium</span>;
      case 'paid':
        return <span className="text-xs font-medium" style={{ color: '#F59E0B' }}>Paid</span>;
      case 'open source':
        return <span className="text-xs font-medium" style={{ color: '#8B5CF6' }}>Open Source</span>;
      default:
        return null;
    }
  };

  const getRatingDisplay = () => {
    if (!tool.reviews?.rating) return null;
    
    return (
      <div className="flex items-center gap-1">
        <Star size={12} className="text-yellow-400 fill-current" />
        <span className="text-yellow-400 text-xs font-medium">
          {tool.reviews.rating.toFixed(1)}
        </span>
        {tool.reviews.totalReviews && (
          <span className="text-gray-500 text-xs">
            ({tool.reviews.totalReviews})
          </span>
        )}
      </div>
    );
  };

  return (
    <Link href={`/tools/${tool.id}`} className="group block">
      <div className="bg-zinc-800 border border-black rounded-lg p-4 shadow-lg hover:shadow-xl transition-all duration-200 h-full flex flex-col">
        
        {/* Tool Header */}
        <div className="flex items-start gap-3 mb-3">
          <ResponsiveImage
            src={tool.logoUrl}
            alt={`${tool.name} logo`}
            width={48}
            height={48}
            className="rounded-lg border border-zinc-600 flex-shrink-0"
          />
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-white font-medium text-sm group-hover:text-orange-400 transition-colors duration-200 truncate">
                {tool.name}
              </h3>
              {tool.isVerified && (
                <CheckCircle size={14} className="text-blue-400 flex-shrink-0" />
              )}
            </div>
            
            {/* Category/Subcategory */}
            {(showCategory || showSubcategory) && (
              <div className="flex items-center gap-2 mb-2">
                {showCategory && (
                  <span className="text-gray-500 text-xs">
                    {tool.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                )}
                {showSubcategory && tool.subcategory && (
                  <>
                    {showCategory && <span className="text-gray-600 text-xs">•</span>}
                    <span className="text-gray-400 text-xs">{tool.subcategory}</span>
                  </>
                )}
              </div>
            )}
            
            {/* Tags */}
            {tool.tags && tool.tags.length > 0 && (
              <div className="flex gap-1 mb-2 flex-wrap">
                {tool.tags.slice(0, 3).map((tag, index) => (
                  <Tag
                    key={index}
                    type={tag.type}
                    label={tag.label}
                  />
                ))}
                {tool.tags.length > 3 && (
                  <span className="text-gray-500 text-xs">+{tool.tags.length - 3}</span>
                )}
              </div>
            )}
          </div>
          
          <ExternalLink 
            size={14} 
            className="text-gray-500 group-hover:text-orange-400 transition-colors duration-200 flex-shrink-0" 
          />
        </div>
        
        {/* Tool Description */}
        <p className="text-white text-xs leading-relaxed mb-4 flex-1 line-clamp-3">
          {tool.description}
        </p>

        {/* Tool Footer */}
        <div className="flex items-center gap-3 pt-2 border-t border-zinc-700">
          {getPricingDisplay()}
          {getRatingDisplay()}
        </div>
      </div>
    </Link>
  );
}
