'use client';

import React from 'react';
import { Search } from 'lucide-react';
import { TOP_SEARCHES } from '@/lib/constants';

interface SearchBarHeaderProps {
  searchTerm: string;
  isDropdownVisible: boolean;
  onSearchChange: (value: string) => void;
  onSearchFocus: () => void;
  onSearchBlur: () => void;
  onTopSearchClick: (term: string) => void;
}

export function SearchBarHeader({
  searchTerm,
  isDropdownVisible,
  onSearchChange,
  onSearchFocus,
  onSearchBlur,
  onTopSearchClick,
}: SearchBarHeaderProps) {
  return (
    <div className="relative flex-1 max-w-2xl mx-4">
      {/* Search Input */}
      <div className="relative">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          onFocus={onSearchFocus}
          onBlur={onSearchBlur}
          placeholder="Search best 10000+ AI tools..."
          className="w-full bg-zinc-700 text-white placeholder-gray-400 px-4 py-3 pr-12 rounded-lg border-none outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-200 text-sm font-medium"
        />
        <Search
          size={20}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200 cursor-pointer"
        />
      </div>

      {/* Top Searches Dropdown */}
      {isDropdownVisible && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-zinc-800 rounded-lg shadow-xl border border-zinc-700 z-50 animate-in slide-in-from-top-2 duration-200">
          <div className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <span className="text-white font-medium">See the current top searches</span>
              <span className="text-xl">🔥</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {TOP_SEARCHES.map((search) => (
                <button
                  key={search.term}
                  onClick={() => onTopSearchClick(search.term)}
                  className="bg-zinc-700 hover:bg-zinc-600 text-gray-300 hover:text-white px-3 py-1 rounded-full text-sm transition-all duration-200 cursor-pointer font-medium"
                >
                  {search.term}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
