'use client';

import React from 'react';
import Link from 'next/link';
import { ExternalLink, TrendingUp } from 'lucide-react';
import { AITool } from '@/lib/types';
import { AI_CATEGORIES } from '@/lib/constants';
import { ResponsiveImage } from '@/components/ui/ResponsiveImage';
import { Tag } from '@/components/ui/Tag';

interface FeaturedToolsProps {
  currentTool?: AITool;
}

export function FeaturedTools({ currentTool }: FeaturedToolsProps) {
  // Get featured tools (tools with high ratings, verified status, or specific tags)
  const getFeaturedTools = (): AITool[] => {
    const allTools: AITool[] = [];
    
    // Collect all tools from all categories
    AI_CATEGORIES.forEach(category => {
      allTools.push(...category.tools);
    });
    
    // Filter out current tool if provided
    const filteredTools = currentTool 
      ? allTools.filter(tool => tool.id !== currentTool.id)
      : allTools;
    
    // Prioritize tools with certain characteristics
    const featuredTools = filteredTools
      .filter(tool => {
        // Include tools that are verified, have high ratings, or have HOT/NEW tags
        return tool.isVerified || 
               (tool.reviews && tool.reviews.rating >= 4.5) ||
               tool.tags?.some(tag => tag.type === 'HOT' || tag.type === 'NEW' || tag.type === 'PREMIUM');
      })
      .slice(0, 8); // Show up to 8 featured tools
    
    // If we don't have enough featured tools, add some random ones
    if (featuredTools.length < 6) {
      const remainingTools = filteredTools
        .filter(tool => !featuredTools.includes(tool))
        .slice(0, 6 - featuredTools.length);
      featuredTools.push(...remainingTools);
    }
    
    return featuredTools.slice(0, 6); // Limit to 6 tools
  };

  const featuredTools = getFeaturedTools();

  return (
    <div>
      <div className="flex items-center gap-2 mb-6">
        <TrendingUp size={20} className="text-orange-400" />
        <h2 className="text-3xl font-bold text-white">Featured</h2>
      </div>

      <div className="space-y-4">
        {featuredTools.map((tool) => (
          <Link
            key={tool.id}
            href={`/tools/${tool.id}`}
            className="group block"
          >
            <div className="p-px rounded-lg shadow-lg border-2 border-black relative transition-all duration-200" style={{
              background: 'rgba(39, 39, 42, 0.7)',
              backdropFilter: 'blur(20px)',
              WebkitBackdropFilter: 'blur(20px)',
              boxShadow: `
                0 8px 32px 0 rgba(0, 0, 0, 0.37),
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2)
              `
            }}>
              <div className="p-4 rounded-lg" style={{
                background: 'rgba(39, 39, 42, 0.3)',
                boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.1)'
              }}>
              
              {/* Tool Header */}
              <div className="flex items-start gap-3 mb-3">
                <ResponsiveImage
                  src={tool.logoUrl}
                  alt={`${tool.name} logo`}
                  width={46}
                  height={46}
                  className="rounded-lg border border-zinc-500 flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-white font-medium text-sm group-hover:text-orange-400 transition-colors duration-200 truncate">
                      {tool.name}
                    </h3>
                    {tool.isVerified && (
                      <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                  
                  {/* Tags */}
                  {tool.tags && tool.tags.length > 0 && (
                    <div className="flex gap-1 mb-2">
                      {tool.tags.slice(0, 2).map((tag, index) => (
                        <Tag
                          key={index}
                          type={tag.type}
                          label={tag.label}
                        />
                      ))}
                    </div>
                  )}
                </div>
                
                <ExternalLink 
                  size={14} 
                  className="text-gray-500 group-hover:text-orange-400 transition-colors duration-200 flex-shrink-0" 
                />
              </div>
              
              {/* Tool Description */}
              <p className="text-gray-400 text-xs leading-relaxed line-clamp-2">
                {tool.description}
              </p>

              </div>
            </div>
          </Link>
        ))}
      </div>
      
      {/* View All Link */}
      <div className="mt-6 pt-4 border-t border-zinc-600 text-center">
        <Link
          href="/"
          className="text-orange-400 hover:text-orange-300 transition-colors duration-200 text-sm font-medium"
        >
          View All Tools →
        </Link>
      </div>
    </div>
  );
}
