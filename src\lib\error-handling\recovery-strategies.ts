import {
  SystemError,
  Error<PERSON>ontext,
  RecoveryResult,
  RecoveryStrategy,
  ErrorCategory,
  ERROR_TYPES
} from './types';

/**
 * Recovery Strategy Manager
 * Implements various recovery strategies for different error types
 */
export class RecoveryStrategyManager {
  private strategies = new Map<string, RecoveryStrategy>();

  constructor() {
    this.initializeRecoveryStrategies();
  }

  /**
   * Execute recovery strategy for a given error
   */
  async executeRecovery(
    systemError: SystemError,
    context: ErrorContext
  ): Promise<RecoveryResult> {
    const strategy = this.selectRecoveryStrategy(systemError, context);
    
    if (!strategy) {
      return {
        success: false,
        strategy: 'none',
        error: 'No applicable recovery strategy found',
        timestamp: new Date().toISOString(),
        requiresManualIntervention: true
      };
    }

    try {
      console.log(`Executing recovery strategy: ${strategy.name} for error: ${systemError.type}`);
      return await strategy.execute(systemError, context);
    } catch (recoveryError) {
      console.error(`Recovery strategy ${strategy.name} failed:`, recoveryError);
      const errorMessage = recoveryError instanceof Error ? recoveryError.message : String(recoveryError);
      return {
        success: false,
        strategy: strategy.name,
        error: `Recovery strategy failed: ${errorMessage}`,
        timestamp: new Date().toISOString(),
        requiresManualIntervention: true
      };
    }
  }

  /**
   * Select the most appropriate recovery strategy for an error
   */
  private selectRecoveryStrategy(
    systemError: SystemError,
    context: ErrorContext
  ): RecoveryStrategy | null {
    // Find strategies applicable to this error type
    const applicableStrategies = Array.from(this.strategies.values())
      .filter(strategy => strategy.applicableErrors.includes(systemError.type))
      .sort((a, b) => b.priority - a.priority); // Sort by priority (highest first)

    return applicableStrategies[0] || null;
  }

  /**
   * Initialize all recovery strategies
   */
  private initializeRecoveryStrategies(): void {
    // Exponential Backoff Retry Strategy
    this.strategies.set('exponential_backoff_retry', {
      name: 'Exponential Backoff Retry',
      execute: this.executeExponentialBackoffRetry.bind(this),
      applicableErrors: [
        ERROR_TYPES.NETWORK_TIMEOUT,
        ERROR_TYPES.SCRAPE_DO_API_ERROR,
        ERROR_TYPES.DATABASE_CONNECTION_LOST,
        ERROR_TYPES.OPENAI_RATE_LIMIT,
        ERROR_TYPES.RATE_LIMIT_EXCEEDED
      ],
      priority: 8,
      maxRetries: 3,
      backoffStrategy: 'exponential'
    });

    // AI Provider Fallback Strategy
    this.strategies.set('ai_provider_fallback', {
      name: 'AI Provider Fallback',
      execute: this.executeAIProviderFallback.bind(this),
      applicableErrors: [
        ERROR_TYPES.OPENAI_RATE_LIMIT,
        ERROR_TYPES.OPENROUTER_INSUFFICIENT_CREDITS,
        ERROR_TYPES.MODEL_UNAVAILABLE,
        ERROR_TYPES.INVALID_API_KEY
      ],
      priority: 9,
      maxRetries: 2
    });

    // Content Splitting Strategy
    this.strategies.set('content_splitting', {
      name: 'Content Splitting Strategy',
      execute: this.executeContentSplitting.bind(this),
      applicableErrors: [
        ERROR_TYPES.CONTEXT_LENGTH_EXCEEDED
      ],
      priority: 7,
      maxRetries: 1
    });

    // Simplified Generation Strategy
    this.strategies.set('simplified_generation', {
      name: 'Simplified Generation Strategy',
      execute: this.executeSimplifiedGeneration.bind(this),
      applicableErrors: [
        ERROR_TYPES.SCHEMA_VALIDATION_FAILED,
        ERROR_TYPES.CONTENT_QUALITY_BELOW_THRESHOLD
      ],
      priority: 6,
      maxRetries: 2
    });

    // Database Reconnection Strategy
    this.strategies.set('database_reconnection', {
      name: 'Database Reconnection Strategy',
      execute: this.executeDatabaseReconnection.bind(this),
      applicableErrors: [
        ERROR_TYPES.DATABASE_CONNECTION_LOST
      ],
      priority: 9,
      maxRetries: 5
    });

    // Job Restart Strategy
    this.strategies.set('job_restart', {
      name: 'Job Restart Strategy',
      execute: this.executeJobRestart.bind(this),
      applicableErrors: [
        ERROR_TYPES.JOB_TIMEOUT,
        ERROR_TYPES.WORKER_UNAVAILABLE
      ],
      priority: 7,
      maxRetries: 3
    });

    // Alternative Scraper Strategy
    this.strategies.set('alternative_scraper', {
      name: 'Alternative Scraper Strategy',
      execute: this.executeAlternativeScraper.bind(this),
      applicableErrors: [
        ERROR_TYPES.SCRAPE_DO_API_ERROR,
        ERROR_TYPES.CONTENT_EXTRACTION_FAILED
      ],
      priority: 6,
      maxRetries: 2
    });
  }

  /**
   * Execute exponential backoff retry strategy
   */
  private async executeExponentialBackoffRetry(
    systemError: SystemError,
    context: ErrorContext
  ): Promise<RecoveryResult> {
    const maxRetries = context.maxRetries || 3;
    const baseDelay = 1000; // 1 second
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Calculate exponential backoff delay
        const delay = baseDelay * Math.pow(2, attempt - 1);
        
        if (attempt > 1) {
          console.log(`Retry attempt ${attempt}/${maxRetries} after ${delay}ms delay`);
          await this.delay(delay);
        }

        // Retry the original operation
        if (context.retryOperation) {
          const result = await context.retryOperation();
          return {
            success: true,
            strategy: 'exponential_backoff_retry',
            attemptsUsed: attempt,
            result,
            timestamp: new Date().toISOString()
          };
        } else {
          throw new Error('No retry operation provided in context');
        }

      } catch (error: any) {
        lastError = error;
        console.warn(`Retry attempt ${attempt}/${maxRetries} failed:`, error.message);
        
        if (attempt === maxRetries) {
          break;
        }
      }
    }

    return {
      success: false,
      strategy: 'exponential_backoff_retry',
      attemptsUsed: maxRetries,
      finalError: lastError!.message,
      timestamp: new Date().toISOString(),
      requiresManualIntervention: true
    };
  }

  /**
   * Execute AI provider fallback strategy
   */
  private async executeAIProviderFallback(
    systemError: SystemError,
    context: ErrorContext
  ): Promise<RecoveryResult> {
    try {
      // Determine alternative provider
      const currentProvider = context.currentProvider || context.provider;
      const alternativeProvider = currentProvider === 'openai' ? 'openrouter' : 'openai';
      
      console.log(`Switching from ${currentProvider} to ${alternativeProvider}`);

      // Switch to alternative provider
      if (context.switchProvider) {
        const result = await context.switchProvider(alternativeProvider);
        
        return {
          success: true,
          strategy: 'ai_provider_fallback',
          alternativeUsed: alternativeProvider,
          result,
          timestamp: new Date().toISOString()
        };
      } else {
        throw new Error('No provider switching function provided in context');
      }

    } catch (fallbackError: any) {
      return {
        success: false,
        strategy: 'ai_provider_fallback',
        fallbackError: fallbackError.message,
        timestamp: new Date().toISOString(),
        requiresManualIntervention: true
      };
    }
  }

  /**
   * Execute content splitting strategy
   */
  private async executeContentSplitting(
    systemError: SystemError,
    context: ErrorContext
  ): Promise<RecoveryResult> {
    try {
      if (!context.content || !context.maxTokens) {
        throw new Error('Content or maxTokens not provided for splitting');
      }

      // Split content into smaller chunks
      const chunks = this.splitContent(context.content, context.maxTokens);
      console.log(`Split content into ${chunks.length} chunks`);

      // Process chunks sequentially
      const results = [];
      for (let i = 0; i < chunks.length; i++) {
        console.log(`Processing chunk ${i + 1}/${chunks.length}`);
        
        if (context.processChunk) {
          const chunkResult = await context.processChunk(chunks[i]);
          results.push(chunkResult);
        } else {
          throw new Error('No chunk processing function provided in context');
        }
      }

      // Combine results
      const combinedResult = this.combineChunkResults(results);

      return {
        success: true,
        strategy: 'content_splitting',
        chunksProcessed: chunks.length,
        result: combinedResult,
        timestamp: new Date().toISOString()
      };

    } catch (splittingError: any) {
      return {
        success: false,
        strategy: 'content_splitting',
        splittingError: splittingError.message,
        timestamp: new Date().toISOString(),
        requiresManualIntervention: true
      };
    }
  }

  /**
   * Execute simplified generation strategy
   */
  private async executeSimplifiedGeneration(
    systemError: SystemError,
    context: ErrorContext
  ): Promise<RecoveryResult> {
    try {
      console.log('Attempting simplified content generation');
      
      // Create simplified context with reduced requirements
      const simplifiedContext = {
        ...context,
        simplified: true,
        qualityThreshold: 0.6, // Lower quality threshold
        maxComplexity: 'basic'
      };

      if (context.retryOperation) {
        const result = await context.retryOperation();
        
        return {
          success: true,
          strategy: 'simplified_generation',
          result,
          timestamp: new Date().toISOString()
        };
      } else {
        throw new Error('No retry operation provided for simplified generation');
      }

    } catch (simplificationError: any) {
      return {
        success: false,
        strategy: 'simplified_generation',
        error: simplificationError.message,
        timestamp: new Date().toISOString(),
        requiresManualIntervention: true
      };
    }
  }

  /**
   * Execute database reconnection strategy
   */
  private async executeDatabaseReconnection(
    systemError: SystemError,
    context: ErrorContext
  ): Promise<RecoveryResult> {
    const maxRetries = 5;
    const baseDelay = 2000; // 2 seconds

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Database reconnection attempt ${attempt}/${maxRetries}`);
        
        // Wait before retry (exponential backoff)
        if (attempt > 1) {
          const delay = baseDelay * Math.pow(2, attempt - 1);
          await this.delay(delay);
        }

        // Attempt to reconnect and retry operation
        if (context.retryOperation) {
          const result = await context.retryOperation();
          
          return {
            success: true,
            strategy: 'database_reconnection',
            attemptsUsed: attempt,
            result,
            timestamp: new Date().toISOString()
          };
        }

      } catch (error: any) {
        console.warn(`Database reconnection attempt ${attempt} failed:`, error.message);
        
        if (attempt === maxRetries) {
          return {
            success: false,
            strategy: 'database_reconnection',
            attemptsUsed: maxRetries,
            finalError: error.message,
            timestamp: new Date().toISOString(),
            requiresManualIntervention: true
          };
        }
      }
    }

    return {
      success: false,
      strategy: 'database_reconnection',
      error: 'Max retries exceeded',
      timestamp: new Date().toISOString(),
      requiresManualIntervention: true
    };
  }

  /**
   * Execute job restart strategy
   */
  private async executeJobRestart(
    systemError: SystemError,
    context: ErrorContext
  ): Promise<RecoveryResult> {
    try {
      console.log(`Restarting job: ${context.jobId}`);
      
      // Implementation would restart the job with fresh context
      if (context.retryOperation) {
        const result = await context.retryOperation();
        
        return {
          success: true,
          strategy: 'job_restart',
          result,
          timestamp: new Date().toISOString()
        };
      } else {
        throw new Error('No job restart operation provided');
      }

    } catch (restartError: any) {
      return {
        success: false,
        strategy: 'job_restart',
        error: restartError.message,
        timestamp: new Date().toISOString(),
        requiresManualIntervention: true
      };
    }
  }

  /**
   * Execute alternative scraper strategy
   */
  private async executeAlternativeScraper(
    systemError: SystemError,
    context: ErrorContext
  ): Promise<RecoveryResult> {
    try {
      console.log('Attempting alternative scraping method');
      
      // Implementation would use alternative scraping approach
      // For now, we'll simulate a fallback to basic scraping
      if (context.retryOperation) {
        const result = await context.retryOperation();
        
        return {
          success: true,
          strategy: 'alternative_scraper',
          result,
          timestamp: new Date().toISOString()
        };
      } else {
        throw new Error('No alternative scraping operation provided');
      }

    } catch (scraperError: any) {
      return {
        success: false,
        strategy: 'alternative_scraper',
        error: scraperError.message,
        timestamp: new Date().toISOString(),
        requiresManualIntervention: true
      };
    }
  }

  /**
   * Split content into smaller chunks based on token limit
   */
  private splitContent(content: string, maxTokens: number): string[] {
    // Simple implementation - split by paragraphs and estimate tokens
    const paragraphs = content.split('\n\n');
    const chunks: string[] = [];
    let currentChunk = '';
    const estimatedTokensPerChar = 0.25; // Rough estimate

    for (const paragraph of paragraphs) {
      const estimatedTokens = (currentChunk + paragraph).length * estimatedTokensPerChar;
      
      if (estimatedTokens > maxTokens && currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = paragraph;
      } else {
        currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }

    return chunks.length > 0 ? chunks : [content];
  }

  /**
   * Combine results from multiple chunks
   */
  private combineChunkResults(results: any[]): any {
    // Simple implementation - concatenate text results
    if (results.length === 0) return null;
    if (results.length === 1) return results[0];

    // If results are strings, concatenate them
    if (typeof results[0] === 'string') {
      return results.join('\n\n');
    }

    // If results are objects with content, combine content
    if (results[0] && typeof results[0] === 'object' && 'content' in results[0]) {
      const combinedContent = results.map(r => r.content).join('\n\n');
      return {
        ...results[0],
        content: combinedContent,
        chunked: true,
        chunkCount: results.length
      };
    }

    // Default: return array of results
    return results;
  }

  /**
   * Utility function to create a delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get all available recovery strategies
   */
  getAvailableStrategies(): RecoveryStrategy[] {
    return Array.from(this.strategies.values());
  }

  /**
   * Get strategies applicable to a specific error type
   */
  getStrategiesForError(errorType: string): RecoveryStrategy[] {
    return Array.from(this.strategies.values())
      .filter(strategy => strategy.applicableErrors.includes(errorType))
      .sort((a, b) => b.priority - a.priority);
  }

  /**
   * Static retry method for backward compatibility with tests
   */
  static async retry<T>(
    operation: () => Promise<T>,
    options: { maxRetries?: number; delay?: number } = {}
  ): Promise<{ success: boolean; attempts: number; result?: T; error?: string }> {
    const maxRetries = options.maxRetries || 3;
    const delay = options.delay || 1000;
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await operation();
        return {
          success: true,
          attempts: attempt,
          result
        };
      } catch (error: any) {
        lastError = error;

        if (attempt === maxRetries) {
          break;
        }

        // Wait before retry
        if (delay > 0) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    return {
      success: false,
      attempts: maxRetries,
      error: lastError!.message
    };
  }
}

// Export for backward compatibility with tests
export const RecoveryStrategies = RecoveryStrategyManager;

// Create singleton instance
export const recoveryStrategyManager = new RecoveryStrategyManager();
