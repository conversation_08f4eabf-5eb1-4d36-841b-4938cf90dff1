'use client';

import { useState, useEffect, useCallback } from 'react';

export interface SubmissionItem {
  id: string;
  name: string;
  url: string;
  description: string;
  category: string;
  subcategory?: string;
  submitterName: string;
  submitterEmail: string;
  status: 'pending' | 'under_review' | 'approved' | 'rejected' | 'published';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  reviewNotes?: string;
  priority: 'high' | 'normal' | 'low';
  pricingType?: string;
}

export interface EditorialStats {
  totalSubmissions: number;
  pendingReview: number;
  underReview: number;
  approvedToday: number;
  rejectedToday: number;
  averageReviewTime: number;
  totalEditorialReviews?: number;
  featuredTools?: number;
}

export interface WorkflowHealth {
  queueHealth: 'healthy' | 'warning' | 'critical';
  reviewEfficiency: 'excellent' | 'good' | 'needs_improvement';
  approvalRate: number;
}

export interface ReviewFormData {
  decision: 'approve' | 'reject' | 'needs_revision';
  reviewNotes: string;
  priority?: 'high' | 'normal' | 'low';
  featuredDate?: string;
  editorialText?: string;
  qualityScore?: number;
}

interface UseEditorialWorkflowOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseEditorialWorkflowReturn {
  // Data
  submissions: SubmissionItem[];
  stats: EditorialStats;
  workflowHealth?: WorkflowHealth;
  categoryBreakdown?: Record<string, number>;
  recentActivity?: any[];
  
  // Loading states
  loading: boolean;
  submitting: boolean;
  error: string | null;
  
  // Actions
  loadData: () => Promise<void>;
  submitReview: (submissionId: string, reviewData: ReviewFormData) => Promise<void>;
  updateSubmissionStatus: (submissionId: string, status: string, notes?: string) => Promise<void>;
  deleteSubmission: (submissionId: string) => Promise<void>;
  refreshStats: () => Promise<void>;
  
  // Filters
  filteredSubmissions: SubmissionItem[];
  setStatusFilter: (status: string) => void;
  setSearchFilter: (search: string) => void;
  statusFilter: string;
  searchFilter: string;
}

export function useEditorialWorkflow(options: UseEditorialWorkflowOptions = {}): UseEditorialWorkflowReturn {
  const { autoRefresh = false, refreshInterval = 30000 } = options;

  // State
  const [submissions, setSubmissions] = useState<SubmissionItem[]>([]);
  const [stats, setStats] = useState<EditorialStats>({
    totalSubmissions: 0,
    pendingReview: 0,
    underReview: 0,
    approvedToday: 0,
    rejectedToday: 0,
    averageReviewTime: 0
  });
  const [workflowHealth, setWorkflowHealth] = useState<WorkflowHealth>();
  const [categoryBreakdown, setCategoryBreakdown] = useState<Record<string, number>>();
  const [recentActivity, setRecentActivity] = useState<any[]>();
  
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Filters
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchFilter, setSearchFilter] = useState('');

  // API helper
  const apiCall = async (endpoint: string, options: RequestInit = {}) => {
    const response = await fetch(endpoint, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'x-admin-api-key': 'admin-dashboard-access',
        ...options.headers
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  };

  // Load submissions and stats
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const [submissionsData, statsData] = await Promise.all([
        apiCall('/api/admin/editorial/submissions'),
        apiCall('/api/admin/editorial/stats')
      ]);

      setSubmissions(submissionsData.submissions || []);
      setStats(statsData.stats || stats);
      setWorkflowHealth(statsData.workflowHealth);
      setCategoryBreakdown(statsData.categoryBreakdown);
      setRecentActivity(statsData.recentActivity);

    } catch (err) {
      console.error('Failed to load editorial data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setLoading(false);
    }
  }, []);

  // Submit review
  const submitReview = useCallback(async (submissionId: string, reviewData: ReviewFormData) => {
    try {
      setSubmitting(true);
      setError(null);

      const result = await apiCall('/api/admin/editorial/review', {
        method: 'POST',
        body: JSON.stringify({
          submissionId,
          decision: reviewData.decision,
          reviewNotes: reviewData.reviewNotes,
          priority: reviewData.priority,
          featuredDate: reviewData.featuredDate,
          editorialText: reviewData.editorialText,
          qualityScore: reviewData.qualityScore,
          reviewerId: 'admin'
        })
      });

      // Update local state
      setSubmissions(prev => 
        prev.map(sub => 
          sub.id === submissionId 
            ? { ...sub, ...result.submission }
            : sub
        )
      );

      // Refresh stats
      await refreshStats();

      return result;
    } catch (err) {
      console.error('Failed to submit review:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to submit review';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  }, []);

  // Update submission status
  const updateSubmissionStatus = useCallback(async (
    submissionId: string, 
    status: string, 
    notes?: string
  ) => {
    try {
      setSubmitting(true);
      setError(null);

      const result = await apiCall('/api/admin/editorial/submissions', {
        method: 'PUT',
        body: JSON.stringify({
          submissionId,
          status,
          reviewNotes: notes,
          reviewedBy: 'admin'
        })
      });

      // Update local state
      setSubmissions(prev => 
        prev.map(sub => 
          sub.id === submissionId 
            ? { ...sub, ...result.submission }
            : sub
        )
      );

      await refreshStats();
      return result;
    } catch (err) {
      console.error('Failed to update submission:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update submission';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  }, []);

  // Delete submission
  const deleteSubmission = useCallback(async (submissionId: string) => {
    try {
      setSubmitting(true);
      setError(null);

      await apiCall(`/api/admin/editorial/submissions?id=${submissionId}`, {
        method: 'DELETE'
      });

      // Update local state
      setSubmissions(prev => prev.filter(sub => sub.id !== submissionId));
      
      await refreshStats();
    } catch (err) {
      console.error('Failed to delete submission:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete submission';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  }, []);

  // Refresh stats only
  const refreshStats = useCallback(async () => {
    try {
      const statsData = await apiCall('/api/admin/editorial/stats');
      setStats(statsData.stats || stats);
      setWorkflowHealth(statsData.workflowHealth);
      setCategoryBreakdown(statsData.categoryBreakdown);
      setRecentActivity(statsData.recentActivity);
    } catch (err) {
      console.error('Failed to refresh stats:', err);
    }
  }, []);

  // Filtered submissions
  const filteredSubmissions = submissions.filter(submission => {
    const matchesStatus = statusFilter === 'all' || submission.status === statusFilter;
    const matchesSearch = searchFilter === '' || 
      submission.name.toLowerCase().includes(searchFilter.toLowerCase()) ||
      submission.submitterName.toLowerCase().includes(searchFilter.toLowerCase()) ||
      submission.category.toLowerCase().includes(searchFilter.toLowerCase());
    
    return matchesStatus && matchesSearch;
  });

  // Auto-refresh effect
  useEffect(() => {
    loadData();

    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(loadData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [loadData, autoRefresh, refreshInterval]);

  return {
    // Data
    submissions,
    stats,
    workflowHealth,
    categoryBreakdown,
    recentActivity,
    
    // Loading states
    loading,
    submitting,
    error,
    
    // Actions
    loadData,
    submitReview,
    updateSubmissionStatus,
    deleteSubmission,
    refreshStats,
    
    // Filters
    filteredSubmissions,
    setStatusFilter,
    setSearchFilter,
    statusFilter,
    searchFilter
  };
}
