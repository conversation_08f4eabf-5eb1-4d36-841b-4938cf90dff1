'use client';

import { useState, useEffect } from 'react';

interface ResultsViewerProps {
  data?: any;
  jobId?: string;
  mode: 'preview' | 'results';
}

interface JobResult {
  id: string;
  status: string;
  totalItems: number;
  successfulItems: number;
  failedItems: number;
  results: Array<{
    url: string;
    status: 'success' | 'failed';
    toolId?: string;
    error?: string;
    generatedContent?: any;
  }>;
  summary: {
    processingTime: number;
    totalCost: number;
    successRate: number;
  };
}

/**
 * Results Viewer Component
 * 
 * Displays processing results with filtering, export capabilities,
 * and detailed item inspection.
 */
export function ResultsViewer({
  data,
  jobId,
  mode,
}: ResultsViewerProps) {
  const [results, setResults] = useState<JobResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState<'all' | 'success' | 'failed'>('all');
  const [selectedItem, setSelectedItem] = useState<any>(null);

  useEffect(() => {
    if (mode === 'results' && jobId) {
      loadJobResults();
    }
  }, [mode, jobId]);

  const loadJobResults = async () => {
    if (!jobId) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/admin/bulk-processing/${jobId}`, {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || '',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load job results');
      }

      const data = await response.json();
      if (data.success && data.job) {
        setResults(data.job);
      }
    } catch (err) {
      console.error('Error loading job results:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (format: 'json' | 'csv') => {
    const exportData = mode === 'preview' ? data : results;
    if (!exportData) return;

    try {
      let content: string;
      let filename: string;
      let mimeType: string;

      if (format === 'json') {
        content = JSON.stringify(exportData, null, 2);
        filename = `bulk-processing-${mode}-${Date.now()}.json`;
        mimeType = 'application/json';
      } else {
        // CSV format
        const items = mode === 'preview' ? exportData.validItems : exportData.results;
        const headers = mode === 'preview' 
          ? ['URL', 'Status']
          : ['URL', 'Status', 'Tool ID', 'Error'];
        
        const csvRows = [
          headers.join(','),
          ...items.map((item: any) => {
            if (mode === 'preview') {
              return `"${item.url}","Ready for processing"`;
            } else {
              return `"${item.url}","${item.status}","${item.toolId || ''}","${item.error || ''}"`;
            }
          })
        ];
        
        content = csvRows.join('\n');
        filename = `bulk-processing-${mode}-${Date.now()}.csv`;
        mimeType = 'text/csv';
      }

      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Export failed:', err);
    }
  };

  if (loading) {
    return (
      <div className="bg-zinc-800 border border-black rounded-lg p-6">
        <div className="flex items-center justify-center space-x-3">
          <div className="animate-spin w-6 h-6 border-2 border-orange-500 border-t-transparent rounded-full"></div>
          <span className="text-white">Loading results...</span>
        </div>
      </div>
    );
  }

  const displayData = mode === 'preview' ? data : results;
  if (!displayData) {
    return (
      <div className="bg-zinc-800 border border-black rounded-lg p-6">
        <div className="text-center text-gray-400">
          No data to display
        </div>
      </div>
    );
  }

  const items = mode === 'preview' 
    ? displayData.validItems || []
    : displayData.results || [];

  const filteredItems = items.filter((item: any) => {
    if (filter === 'all') return true;
    if (mode === 'preview') return true; // No filtering for preview
    return item.status === filter;
  });

  return (
    <div className="bg-zinc-800 border border-black rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-white">
          {mode === 'preview' ? 'Data Preview' : 'Processing Results'}
        </h2>
        <div className="flex space-x-2">
          <button
            onClick={() => handleExport('json')}
            className="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm font-medium transition-colors"
          >
            Export JSON
          </button>
          <button
            onClick={() => handleExport('csv')}
            className="bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-sm font-medium transition-colors"
          >
            Export CSV
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-zinc-700 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-blue-400">
            {mode === 'preview' ? displayData.totalItems : displayData.totalItems}
          </div>
          <div className="text-sm text-gray-400">Total Items</div>
        </div>
        {mode === 'results' && (
          <>
            <div className="bg-zinc-700 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-400">{displayData.successfulItems}</div>
              <div className="text-sm text-gray-400">Successful</div>
            </div>
            <div className="bg-zinc-700 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-red-400">{displayData.failedItems}</div>
              <div className="text-sm text-gray-400">Failed</div>
            </div>
            <div className="bg-zinc-700 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-yellow-400">
                {Math.round(displayData.summary?.successRate || 0)}%
              </div>
              <div className="text-sm text-gray-400">Success Rate</div>
            </div>
          </>
        )}
        {mode === 'preview' && (
          <>
            <div className="bg-zinc-700 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-green-400">
                {displayData.validItems?.length || 0}
              </div>
              <div className="text-sm text-gray-400">Valid</div>
            </div>
            <div className="bg-zinc-700 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-red-400">
                {displayData.invalidItems?.length || 0}
              </div>
              <div className="text-sm text-gray-400">Invalid</div>
            </div>
            <div className="bg-zinc-700 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-yellow-400">
                {displayData.duplicatesRemoved || 0}
              </div>
              <div className="text-sm text-gray-400">Duplicates</div>
            </div>
          </>
        )}
      </div>

      {/* Filter Tabs (Results mode only) */}
      {mode === 'results' && (
        <div className="flex space-x-1 mb-4">
          {(['all', 'success', 'failed'] as const).map((filterOption) => (
            <button
              key={filterOption}
              onClick={() => setFilter(filterOption)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                filter === filterOption
                  ? 'bg-orange-500 text-white'
                  : 'bg-zinc-700 text-gray-400 hover:bg-zinc-600'
              }`}
            >
              {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
              {filterOption !== 'all' && (
                <span className="ml-1 text-xs">
                  ({items.filter((item: any) => item.status === filterOption).length})
                </span>
              )}
            </button>
          ))}
        </div>
      )}

      {/* Items List */}
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {filteredItems.length > 0 ? (
          filteredItems.map((item: any, index: number) => (
            <div
              key={index}
              className="bg-zinc-700 rounded-lg p-4 hover:bg-zinc-600 transition-colors cursor-pointer"
              onClick={() => setSelectedItem(item)}
            >
              <div className="flex justify-between items-start">
                <div className="flex-1 min-w-0">
                  <div className="text-white font-mono text-sm truncate">
                    {item.url}
                  </div>
                  {mode === 'results' && item.error && (
                    <div className="text-red-400 text-xs mt-1 truncate">
                      Error: {item.error}
                    </div>
                  )}
                  {mode === 'results' && item.toolId && (
                    <div className="text-green-400 text-xs mt-1">
                      Tool ID: {item.toolId}
                    </div>
                  )}
                </div>
                <div className="ml-4 flex-shrink-0">
                  {mode === 'preview' ? (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400">
                      Ready
                    </span>
                  ) : (
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      item.status === 'success'
                        ? 'bg-green-500/20 text-green-400'
                        : 'bg-red-500/20 text-red-400'
                    }`}>
                      {item.status === 'success' ? '✓' : '✗'} {item.status}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center text-gray-400 py-8">
            No items to display
          </div>
        )}
      </div>

      {/* Item Detail Modal */}
      {selectedItem && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-zinc-800 border border-zinc-700 rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-white">Item Details</h3>
                <button
                  onClick={() => setSelectedItem(null)}
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </button>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">URL</label>
                  <div className="bg-zinc-700 rounded p-3 text-white font-mono text-sm break-all">
                    {selectedItem.url}
                  </div>
                </div>
                {mode === 'results' && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Status</label>
                      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                        selectedItem.status === 'success'
                          ? 'bg-green-500/20 text-green-400'
                          : 'bg-red-500/20 text-red-400'
                      }`}>
                        {selectedItem.status}
                      </div>
                    </div>
                    {selectedItem.toolId && (
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Tool ID</label>
                        <div className="bg-zinc-700 rounded p-3 text-white font-mono text-sm">
                          {selectedItem.toolId}
                        </div>
                      </div>
                    )}
                    {selectedItem.error && (
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Error</label>
                        <div className="bg-red-900/20 border border-red-500/50 rounded p-3 text-red-300 text-sm">
                          {selectedItem.error}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
