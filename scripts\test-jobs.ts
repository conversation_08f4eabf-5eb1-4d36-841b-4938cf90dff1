#!/usr/bin/env tsx

/**
 * Test script for the background job system
 * Run with: npx tsx scripts/test-jobs.ts
 */

import { config } from 'dotenv';
import { getJobQueue, JobType, JobPriority } from '../src/lib/jobs';

// Load environment variables
config({ path: '.env.local' });

async function testJobSystem() {
  console.log('🧪 Testing Background Job System\n');

  if (process.env.JOB_QUEUE_ENABLED !== 'true') {
    console.log('❌ Job queue is disabled. Set JOB_QUEUE_ENABLED=true in .env.local');
    process.exit(1);
  }

  try {
    const queue = getJobQueue();
    console.log('✅ Job queue initialized');

    // Test 1: Web Scraping Job
    console.log('\n📡 Testing web scraping job...');
    const scrapingJob = await queue.add(
      JobType.WEB_SCRAPING,
      {
        url: 'https://example.com',
        options: {
          timeout: 10000,
          extractImages: false,
          extractLinks: false,
        },
      },
      {
        priority: JobPriority.HIGH,
      }
    );
    console.log(`✅ Scraping job created: ${scrapingJob.id}`);

    // Test 2: Content Generation Job (will fail without scraped data, but tests the queue)
    console.log('\n🤖 Testing content generation job...');
    const contentJob = await queue.add(
      JobType.CONTENT_GENERATION,
      {
        url: 'https://example.com',
        scrapedData: {
          title: 'Example Tool',
          text: 'This is a test tool for demonstration purposes.',
          meta: {
            description: 'A test tool',
          },
        },
      },
      {
        priority: JobPriority.NORMAL,
      }
    );
    console.log(`✅ Content generation job created: ${contentJob.id}`);

    // Test 3: Email Notification Job (will fail without SMTP config, but tests the queue)
    if (process.env.SMTP_USER) {
      console.log('\n📧 Testing email notification job...');
      const emailJob = await queue.add(
        JobType.EMAIL_NOTIFICATION,
        {
          to: process.env.ADMIN_EMAIL || '<EMAIL>',
          subject: 'Test Email from AI Dude Directory',
          template: 'admin-tool-created',
          data: {
            toolName: 'Test Tool',
            toolUrl: 'https://example.com',
            toolId: 'test-123',
          },
        },
        {
          priority: JobPriority.LOW,
        }
      );
      console.log(`✅ Email job created: ${emailJob.id}`);
    } else {
      console.log('\n📧 Skipping email test (no SMTP configuration)');
    }

    // Monitor jobs for 30 seconds
    console.log('\n⏱️  Monitoring jobs for 30 seconds...');
    const startTime = Date.now();
    const monitorInterval = setInterval(async () => {
      const jobs = await queue.getJobs();
      const pending = jobs.filter(j => j.status === 'pending').length;
      const processing = jobs.filter(j => j.status === 'processing').length;
      const completed = jobs.filter(j => j.status === 'completed').length;
      const failed = jobs.filter(j => j.status === 'failed').length;

      console.log(`📊 Jobs - Pending: ${pending}, Processing: ${processing}, Completed: ${completed}, Failed: ${failed}`);

      if (Date.now() - startTime > 30000) {
        clearInterval(monitorInterval);
        
        console.log('\n📋 Final job status:');
        for (const job of jobs) {
          console.log(`  ${job.id}: ${job.status}${job.error ? ` (${job.error})` : ''}`);
        }

        console.log('\n🎉 Job system test completed!');
        process.exit(0);
      }
    }, 2000);

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Test interrupted');
  process.exit(0);
});

testJobSystem();
