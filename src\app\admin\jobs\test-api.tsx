'use client';

import React, { useState } from 'react';

/**
 * API Test Component for Job Monitoring Dashboard
 * 
 * This component tests the API endpoints to verify they work correctly
 * after the fixes have been applied.
 */
export default function JobMonitoringAPITest(): React.JSX.Element {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const runAPITests = async () => {
    setIsLoading(true);
    const results: any[] = [];

    // Test 1: Health Check
    try {
      const response = await fetch('/api/health/jobs');
      const data = await response.json();
      results.push({
        test: 'Health Check',
        status: response.ok ? 'PASS' : 'FAIL',
        response: data
      });
    } catch (error) {
      results.push({
        test: 'Health Check',
        status: 'ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 2: Jobs List API
    try {
      const response = await fetch('/api/automation/jobs', {
        headers: {
          'x-admin-api-key': 'admin-dashboard-access'
        }
      });
      const data = await response.json();
      results.push({
        test: 'Jobs List API',
        status: response.ok ? 'PASS' : 'FAIL',
        response: data
      });
    } catch (error) {
      results.push({
        test: 'Jobs List API',
        status: 'ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 3: WebSocket API (HTTP fallback)
    try {
      const response = await fetch('/api/jobs/websocket', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': 'admin-dashboard-access'
        },
        body: JSON.stringify({
          action: 'register_connection',
          connectionId: 'test-connection'
        })
      });
      const data = await response.json();
      results.push({
        test: 'WebSocket API (HTTP)',
        status: response.ok ? 'PASS' : 'FAIL',
        response: data
      });
    } catch (error) {
      results.push({
        test: 'WebSocket API (HTTP)',
        status: 'ERROR',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    setTestResults(results);
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-zinc-900 text-white font-roboto p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Job Monitoring API Tests</h1>
        
        <div className="mb-8">
          <button
            onClick={runAPITests}
            disabled={isLoading}
            className="bg-orange-500 hover:bg-orange-600 px-6 py-3 rounded-lg font-medium disabled:opacity-50"
          >
            {isLoading ? 'Running Tests...' : 'Run API Tests'}
          </button>
        </div>

        {testResults.length > 0 && (
          <div className="space-y-4">
            <h2 className="text-2xl font-semibold mb-4">Test Results</h2>
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border ${
                  result.status === 'PASS'
                    ? 'bg-green-900/20 border-green-500'
                    : result.status === 'FAIL'
                    ? 'bg-red-900/20 border-red-500'
                    : 'bg-yellow-900/20 border-yellow-500'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-medium">{result.test}</h3>
                  <span
                    className={`px-3 py-1 rounded text-sm font-medium ${
                      result.status === 'PASS'
                        ? 'bg-green-500 text-white'
                        : result.status === 'FAIL'
                        ? 'bg-red-500 text-white'
                        : 'bg-yellow-500 text-black'
                    }`}
                  >
                    {result.status}
                  </span>
                </div>
                
                {result.response && (
                  <div className="mt-2">
                    <h4 className="text-sm font-medium text-gray-300 mb-1">Response:</h4>
                    <pre className="bg-zinc-800 p-3 rounded text-xs overflow-auto">
                      {JSON.stringify(result.response, null, 2)}
                    </pre>
                  </div>
                )}
                
                {result.error && (
                  <div className="mt-2">
                    <h4 className="text-sm font-medium text-red-300 mb-1">Error:</h4>
                    <p className="text-red-200 text-sm">{result.error}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        <div className="mt-8 p-4 bg-blue-900/20 border border-blue-500 rounded-lg">
          <h3 className="text-lg font-medium text-blue-300 mb-2">Fixed Issues Summary</h3>
          <ul className="text-sm text-blue-200 space-y-1">
            <li>✅ API response structure mismatch (data.jobs → data.data.jobs)</li>
            <li>✅ Missing job properties in API responses</li>
            <li>✅ WebSocket implementation converted to HTTP polling</li>
            <li>✅ Environment variable access replaced with temp key</li>
            <li>✅ API route method mismatch (PUT → POST)</li>
            <li>✅ Missing job actions (pause, resume, stop) added</li>
            <li>✅ Temporary admin key validation implemented</li>
            <li>✅ Import path issues resolved</li>
            <li>✅ Complete job properties in individual API endpoint</li>
          </ul>
        </div>

        <div className="mt-4 p-4 bg-zinc-800 border border-zinc-700 rounded-lg">
          <h3 className="text-lg font-medium text-white mb-2">Next Steps</h3>
          <ul className="text-sm text-gray-300 space-y-1">
            <li>• Configure TypeScript JSX settings in tsconfig.json</li>
            <li>• Set up proper admin authentication system</li>
            <li>• Implement real WebSocket server for production</li>
            <li>• Add comprehensive error boundaries</li>
            <li>• Test with actual job data</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
