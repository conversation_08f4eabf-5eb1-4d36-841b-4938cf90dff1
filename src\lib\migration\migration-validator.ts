/**
 * Migration Validator
 * 
 * Comprehensive validation system for data migration integrity,
 * schema compliance, and rollback procedures.
 */

import { createClient } from '@supabase/supabase-js';
import { DbTool } from '../types';

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  summary: ValidationSummary;
}

export interface ValidationError {
  type: 'schema' | 'data' | 'integrity' | 'constraint';
  table: string;
  field?: string;
  message: string;
  recordId?: string;
  severity: 'critical' | 'high' | 'medium';
}

export interface ValidationWarning {
  type: 'performance' | 'compatibility' | 'data_quality' | 'integrity';
  table: string;
  message: string;
  recordCount?: number;
}

export interface ValidationSummary {
  totalRecords: number;
  validRecords: number;
  invalidRecords: number;
  tablesChecked: string[];
  validationDuration: number;
}

export class MigrationValidator {
  private supabase;
  private startTime: number = 0;

  constructor() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    
    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing required Supabase environment variables');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  /**
   * Perform comprehensive validation of migrated data
   */
  async validateMigration(): Promise<ValidationResult> {
    this.startTime = Date.now();
    
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      summary: {
        totalRecords: 0,
        validRecords: 0,
        invalidRecords: 0,
        tablesChecked: [],
        validationDuration: 0
      }
    };

    try {
      console.log('🔍 Starting comprehensive migration validation...');

      // Validate schema integrity
      await this.validateSchemaIntegrity(result);
      
      // Validate data integrity
      await this.validateDataIntegrity(result);
      
      // Validate foreign key relationships
      await this.validateForeignKeyIntegrity(result);
      
      // Validate enhanced AI system fields
      await this.validateEnhancedAIFields(result);
      
      // Validate system configuration
      await this.validateSystemConfiguration(result);

      // Calculate final results
      result.summary.validationDuration = Date.now() - this.startTime;
      result.isValid = result.errors.filter(e => e.severity === 'critical').length === 0;

      if (result.isValid) {
        console.log('✅ Migration validation passed');
      } else {
        console.log('❌ Migration validation failed');
      }

      return result;

    } catch (error: any) {
      result.errors.push({
        type: 'schema',
        table: 'validation_system',
        message: `Validation failed: ${error.message}`,
        severity: 'critical'
      });
      
      result.isValid = false;
      result.summary.validationDuration = Date.now() - this.startTime;
      
      return result;
    }
  }

  /**
   * Validate database schema integrity
   */
  private async validateSchemaIntegrity(result: ValidationResult): Promise<void> {
    console.log('  📋 Validating schema integrity...');

    const expectedTables = [
      'tools',
      'ai_generation_jobs',
      'media_assets',
      'editorial_reviews',
      'bulk_processing_jobs',
      'system_configuration'
    ];

    for (const tableName of expectedTables) {
      try {
        const { data, error } = await this.supabase
          .from(tableName)
          .select('*')
          .limit(1);

        if (error) {
          result.errors.push({
            type: 'schema',
            table: tableName,
            message: `Table access failed: ${error.message}`,
            severity: 'critical'
          });
        } else {
          result.summary.tablesChecked.push(tableName);
        }
      } catch (error: any) {
        result.errors.push({
          type: 'schema',
          table: tableName,
          message: `Schema validation failed: ${error.message}`,
          severity: 'critical'
        });
      }
    }
  }

  /**
   * Validate data integrity and constraints
   */
  private async validateDataIntegrity(result: ValidationResult): Promise<void> {
    console.log('  🔍 Validating data integrity...');

    // Validate tools table
    await this.validateToolsTable(result);
    
    // Validate AI generation jobs
    await this.validateAIGenerationJobs(result);
    
    // Validate media assets
    await this.validateMediaAssets(result);
  }

  /**
   * Validate tools table data
   */
  private async validateToolsTable(result: ValidationResult): Promise<void> {
    const { data: tools, error } = await this.supabase
      .from('tools')
      .select('id, name, link, ai_generation_status, submission_type');

    if (error) {
      result.errors.push({
        type: 'data',
        table: 'tools',
        message: `Failed to fetch tools: ${error.message}`,
        severity: 'critical'
      });
      return;
    }

    if (!tools) return;

    result.summary.totalRecords += tools.length;

    for (const tool of tools) {
      let isValid = true;

      // Check required fields
      if (!tool.name || !tool.link) {
        result.errors.push({
          type: 'data',
          table: 'tools',
          field: !tool.name ? 'name' : 'link',
          message: 'Required field is missing',
          recordId: tool.id,
          severity: 'high'
        });
        isValid = false;
      }

      // Check enhanced AI system fields
      if (!tool.ai_generation_status) {
        result.errors.push({
          type: 'data',
          table: 'tools',
          field: 'ai_generation_status',
          message: 'AI generation status is missing',
          recordId: tool.id,
          severity: 'medium'
        });
        isValid = false;
      }

      if (!tool.submission_type) {
        result.errors.push({
          type: 'data',
          table: 'tools',
          field: 'submission_type',
          message: 'Submission type is missing',
          recordId: tool.id,
          severity: 'medium'
        });
        isValid = false;
      }

      if (isValid) {
        result.summary.validRecords++;
      } else {
        result.summary.invalidRecords++;
      }
    }
  }

  /**
   * Validate AI generation jobs
   */
  private async validateAIGenerationJobs(result: ValidationResult): Promise<void> {
    const { data: jobs, error } = await this.supabase
      .from('ai_generation_jobs')
      .select('id, tool_id, job_type, status');

    if (error) {
      result.errors.push({
        type: 'data',
        table: 'ai_generation_jobs',
        message: `Failed to fetch AI jobs: ${error.message}`,
        severity: 'critical'
      });
      return;
    }

    if (!jobs) return;

    result.summary.totalRecords += jobs.length;

    for (const job of jobs) {
      let isValid = true;

      // Check required fields
      if (!job.tool_id || !job.job_type || !job.status) {
        result.errors.push({
          type: 'data',
          table: 'ai_generation_jobs',
          message: 'Required fields are missing',
          recordId: job.id,
          severity: 'high'
        });
        isValid = false;
      }

      if (isValid) {
        result.summary.validRecords++;
      } else {
        result.summary.invalidRecords++;
      }
    }
  }

  /**
   * Validate media assets
   */
  private async validateMediaAssets(result: ValidationResult): Promise<void> {
    const { data: assets, error } = await this.supabase
      .from('media_assets')
      .select('id, tool_id, asset_type');

    if (error) {
      result.errors.push({
        type: 'data',
        table: 'media_assets',
        message: `Failed to fetch media assets: ${error.message}`,
        severity: 'critical'
      });
      return;
    }

    if (!assets) return;

    result.summary.totalRecords += assets.length;

    for (const asset of assets) {
      let isValid = true;

      // Check required fields
      if (!asset.tool_id || !asset.asset_type) {
        result.errors.push({
          type: 'data',
          table: 'media_assets',
          message: 'Required fields are missing',
          recordId: asset.id,
          severity: 'high'
        });
        isValid = false;
      }

      if (isValid) {
        result.summary.validRecords++;
      } else {
        result.summary.invalidRecords++;
      }
    }
  }

  /**
   * Validate foreign key relationships
   */
  private async validateForeignKeyIntegrity(result: ValidationResult): Promise<void> {
    console.log('  🔗 Validating foreign key relationships...');

    // Check tools -> editorial_reviews relationship
    const { data: orphanedReviews, error: reviewError } = await this.supabase
      .rpc('check_orphaned_editorial_reviews');

    if (reviewError && !reviewError.message.includes('function')) {
      result.warnings.push({
        type: 'integrity',
        table: 'editorial_reviews',
        message: 'Could not verify foreign key integrity'
      });
    }

    // Check tools -> ai_generation_jobs relationship
    const { data: orphanedJobs, error: jobError } = await this.supabase
      .rpc('check_orphaned_ai_jobs');

    if (jobError && !jobError.message.includes('function')) {
      result.warnings.push({
        type: 'integrity',
        table: 'ai_generation_jobs',
        message: 'Could not verify foreign key integrity'
      });
    }
  }

  /**
   * Validate enhanced AI system fields
   */
  private async validateEnhancedAIFields(result: ValidationResult): Promise<void> {
    console.log('  🤖 Validating enhanced AI system fields...');

    const { data: tools, error } = await this.supabase
      .from('tools')
      .select('id, ai_generation_status, submission_type, submission_source')
      .or('ai_generation_status.is.null,submission_type.is.null');

    if (error) {
      result.errors.push({
        type: 'data',
        table: 'tools',
        message: `Failed to validate AI fields: ${error.message}`,
        severity: 'high'
      });
      return;
    }

    if (tools && tools.length > 0) {
      result.errors.push({
        type: 'data',
        table: 'tools',
        message: `${tools.length} tools missing enhanced AI system fields`,
        severity: 'high'
      });
    }
  }

  /**
   * Validate system configuration
   */
  private async validateSystemConfiguration(result: ValidationResult): Promise<void> {
    console.log('  ⚙️ Validating system configuration...');

    const requiredConfigs = [
      'ai_provider_openai_enabled',
      'ai_provider_openrouter_enabled',
      'scraping_default_timeout',
      'job_processing_max_concurrent'
    ];

    for (const configKey of requiredConfigs) {
      const { data, error } = await this.supabase
        .from('system_configuration')
        .select('config_value')
        .eq('config_key', configKey)
        .single();

      if (error || !data) {
        result.warnings.push({
          type: 'data_quality',
          table: 'system_configuration',
          message: `Missing required configuration: ${configKey}`
        });
      }
    }
  }

  /**
   * Generate validation report
   */
  generateReport(result: ValidationResult): string {
    const report = [
      '📊 MIGRATION VALIDATION REPORT',
      '=' .repeat(50),
      '',
      `🎯 Overall Status: ${result.isValid ? '✅ PASSED' : '❌ FAILED'}`,
      `⏱️ Validation Duration: ${result.summary.validationDuration}ms`,
      `📋 Tables Checked: ${result.summary.tablesChecked.length}`,
      `📊 Total Records: ${result.summary.totalRecords}`,
      `✅ Valid Records: ${result.summary.validRecords}`,
      `❌ Invalid Records: ${result.summary.invalidRecords}`,
      '',
      '🚨 ERRORS:',
      ...result.errors.map(e => `   ${e.severity.toUpperCase()}: ${e.table}.${e.field || 'general'} - ${e.message}`),
      '',
      '⚠️ WARNINGS:',
      ...result.warnings.map(w => `   ${w.type.toUpperCase()}: ${w.table} - ${w.message}`),
      '',
      '=' .repeat(50)
    ];

    return report.join('\n');
  }
}
