/**
 * Basic functionality test for the Enhanced Error Handling System
 * Tests core error handling without complex dependencies
 */

import { 
  ErrorManager, 
  ErrorHandlingUtils, 
  ErrorCategory,
  ErrorSeverity,
  ERROR_TYPES
} from './index';

/**
 * Test basic error handling functionality
 */
async function testBasicErrorHandling(): Promise<boolean> {
  console.log('🧪 Testing Basic Error Handling Functionality...\n');

  try {
    // Test 1: Error Manager Initialization
    console.log('1️⃣ Testing Error Manager Initialization...');
    const errorManager = ErrorManager.getInstance();
    console.log('   ✅ Error Manager initialized successfully\n');

    // Test 2: Error Context Creation
    console.log('2️⃣ Testing Error Context Creation...');
    const context = ErrorHandlingUtils.createErrorContext({
      operation: 'test_operation',
      provider: 'openai',
      metadata: { test: true }
    });
    console.log('   ✅ Error Context created successfully');
    console.log(`   Context: ${JSON.stringify(context, null, 2)}\n`);

    // Test 3: Basic Error Handling
    console.log('3️⃣ Testing Basic Error Handling...');
    const testError = new Error('Test error for basic functionality');
    (testError as any).code = 'TEST_ERROR';

    const result = await errorManager.handleError(testError, context);
    console.log('   ✅ Error handled successfully');
    console.log(`   Result: ${JSON.stringify(result, null, 2)}\n`);

    // Test 4: Error Classification
    console.log('4️⃣ Testing Error Classification...');
    const networkError = new Error('Network timeout');
    (networkError as any).code = 'ETIMEDOUT';

    const networkResult = await errorManager.handleError(networkError, {
      operation: 'network_request'
    });
    console.log('   ✅ Network error classified and handled');
    console.log(`   Strategy: ${networkResult.strategy}\n`);

    // Test 5: Error Metrics
    console.log('5️⃣ Testing Error Metrics...');
    const metrics = errorManager.getErrorMetrics();
    console.log('   ✅ Error metrics retrieved');
    console.log(`   Tracked error types: ${metrics.size}\n`);

    // Test 6: Error Utilities
    console.log('6️⃣ Testing Error Utilities...');
    const isRetryable = ErrorHandlingUtils.isRetryableError(networkError);
    const errorType = ErrorHandlingUtils.extractErrorType(testError);
    const logFormat = ErrorHandlingUtils.formatErrorForLogging(testError, context);
    
    console.log('   ✅ Error utilities working');
    console.log(`   Is retryable: ${isRetryable}`);
    console.log(`   Error type: ${errorType}`);
    console.log(`   Log format: ${JSON.stringify(logFormat, null, 2)}\n`);

    console.log('✅ All basic functionality tests passed!\n');
    return true;

  } catch (error) {
    console.error('❌ Basic functionality test failed:', error);
    return false;
  }
}

/**
 * Test error handling with retry mechanism
 */
async function testRetryMechanism(): Promise<boolean> {
  console.log('🔄 Testing Retry Mechanism...\n');

  try {
    let attemptCount = 0;
    const maxAttempts = 3;

    const retryableOperation = async () => {
      attemptCount++;
      console.log(`   Attempt ${attemptCount}/${maxAttempts}`);
      
      if (attemptCount < maxAttempts) {
        const error = new Error(`Simulated failure on attempt ${attemptCount}`);
        (error as any).code = 'TEMPORARY_FAILURE';
        throw error;
      }
      
      return { success: true, attempts: attemptCount };
    };

    const result = await ErrorHandlingUtils.withErrorHandling(
      retryableOperation,
      ErrorHandlingUtils.createErrorContext({
        operation: 'retry_test',
        metadata: { maxRetries: maxAttempts }
      })
    );

    console.log('   ✅ Retry mechanism test passed');
    console.log(`   Final result: ${JSON.stringify(result, null, 2)}\n`);
    return true;

  } catch (error) {
    console.error('   ❌ Retry mechanism test failed:', error);
    return false;
  }
}

/**
 * Test error constants and types
 */
async function testErrorConstants(): Promise<boolean> {
  console.log('📋 Testing Error Constants and Types...\n');

  try {
    // Test error categories
    console.log('1️⃣ Testing Error Categories...');
    const categories = Object.values(ErrorCategory);
    console.log(`   Available categories: ${categories.join(', ')}`);

    // Test error severities
    console.log('2️⃣ Testing Error Severities...');
    const severities = Object.values(ErrorSeverity);
    console.log(`   Available severities: ${severities.join(', ')}`);

    // Test error types
    console.log('3️⃣ Testing Error Types...');
    const errorTypes = Object.values(ERROR_TYPES);
    console.log(`   Available error types: ${errorTypes.length} types`);
    console.log(`   Sample types: ${errorTypes.slice(0, 5).join(', ')}`);

    console.log('   ✅ All constants and types are accessible\n');
    return true;

  } catch (error) {
    console.error('   ❌ Constants test failed:', error);
    return false;
  }
}

/**
 * Run all basic tests
 */
export async function runBasicTests(): Promise<void> {
  console.log('🚀 Running Basic Error Handling Tests...\n');
  
  const results = {
    basicFunctionality: false,
    retryMechanism: false,
    errorConstants: false
  };

  try {
    results.basicFunctionality = await testBasicErrorHandling();
    results.retryMechanism = await testRetryMechanism();
    results.errorConstants = await testErrorConstants();

    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;

    console.log('📊 Test Results Summary:');
    console.log(`   Basic Functionality: ${results.basicFunctionality ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Retry Mechanism: ${results.retryMechanism ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Error Constants: ${results.errorConstants ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`\n   Overall: ${passedTests}/${totalTests} tests passed`);

    if (passedTests === totalTests) {
      console.log('\n🎉 All basic tests passed! Error handling system is functional.');
    } else {
      console.log('\n⚠️ Some tests failed. Please check the error handling implementation.');
    }

  } catch (error) {
    console.error('\n💥 Test suite failed:', error);
  }
}

// Export for use in other test files
export {
  testBasicErrorHandling,
  testRetryMechanism,
  testErrorConstants
};
