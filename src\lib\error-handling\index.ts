/**
 * Enhanced AI System Error Handling Module
 * 
 * This module provides comprehensive error handling and recovery capabilities
 * for the Enhanced AI System, including:
 * 
 * - Error classification and categorization
 * - Automatic recovery strategies
 * - Error monitoring and alerting
 * - Health checking and system monitoring
 * - Manual intervention procedures
 * - Error reporting and analytics
 */

// Core error handling
export { ErrorManager, errorManager } from './error-manager';
export { RecoveryStrategyManager } from './recovery-strategies';
export { ErrorMonitoringSystem } from './monitoring';

// Health monitoring
export { HealthChecker } from '../monitoring/health-checker';

// Types and interfaces
export {
  // Enums
  ErrorCategory,
  ErrorSeverity,

  // Constants
  ERROR_TYPES
} from './types';

export type {
  // Core interfaces
  SystemError,
  ErrorContext,
  ErrorClassification,
  RecoveryResult,
  RecoveryStrategy,

  // Monitoring interfaces
  ErrorMetrics,
  AlertThreshold,
  Alert,
  ErrorReport,
  TimeRange,

  // Health checking interfaces
  HealthCheck,
  HealthCheckResult,
  SystemHealthStatus,

  // Utility interfaces
  ImpactAssessment,
  ManualInterventionProcedure,
  EscalationPath,
  ManualAction,
  ErrorDefinition,
  ChaosTestType,
  ChaosTestResult,
  ErrorHandlingConfig,
  RecoveryContext,
  ErrorType
} from './types';

// Import types for proper typing
import type { ErrorContext } from './types';

// Utility functions for error handling
export const ErrorHandlingUtils = {
  /**
   * Create a standardized error context
   */
  createErrorContext(options: {
    operation?: string;
    provider?: 'openai' | 'openrouter' | 'scrape.do';
    toolId?: string;
    jobId?: string;
    userId?: string;
    metadata?: Record<string, any>;
  }): ErrorContext {
    return {
      operation: options.operation,
      provider: options.provider,
      toolId: options.toolId,
      jobId: options.jobId,
      userId: options.userId,
      metadata: options.metadata || {}
    };
  },

  /**
   * Wrap an async operation with error handling
   */
  async withErrorHandling<T>(
    operation: () => Promise<T>,
    context: ErrorContext = {}
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      const { errorManager } = await import('./error-manager');
      const recoveryResult = await errorManager.handleError(error, context);
      
      if (recoveryResult.success && recoveryResult.result) {
        return recoveryResult.result;
      }
      
      // Re-throw the error if recovery failed
      throw error;
    }
  },

  /**
   * Create a retry operation function for error context
   */
  createRetryOperation<T>(operation: () => Promise<T>): () => Promise<T> {
    return operation;
  },

  /**
   * Create a provider switching function for AI operations
   */
  createProviderSwitcher(
    openaiOperation: () => Promise<any>,
    openrouterOperation: () => Promise<any>
  ): (provider: string) => Promise<any> {
    return async (provider: string) => {
      switch (provider) {
        case 'openai':
          return await openaiOperation();
        case 'openrouter':
          return await openrouterOperation();
        default:
          throw new Error(`Unknown provider: ${provider}`);
      }
    };
  },

  /**
   * Create a content processing function for chunked operations
   */
  createChunkProcessor<T>(
    processor: (chunk: string) => Promise<T>
  ): (chunk: string) => Promise<T> {
    return processor;
  },

  /**
   * Check if an error is retryable
   */
  isRetryableError(error: any): boolean {
    const retryablePatterns = [
      /timeout/i,
      /network/i,
      /rate.?limit/i,
      /temporary/i,
      /unavailable/i,
      /503/,
      /502/,
      /429/
    ];

    const errorMessage = error.message || error.toString();
    return retryablePatterns.some(pattern => pattern.test(errorMessage));
  },

  /**
   * Extract error type from error object
   */
  extractErrorType(error: any): string {
    if (error.code) return error.code;
    if (error.type) return error.type;
    if (error.name) return error.name;
    
    // Try to infer from message
    const message = error.message || '';
    if (message.includes('timeout')) return 'TIMEOUT';
    if (message.includes('network')) return 'NETWORK_ERROR';
    if (message.includes('rate limit')) return 'RATE_LIMIT_EXCEEDED';
    if (message.includes('quota')) return 'QUOTA_EXCEEDED';
    if (message.includes('unauthorized')) return 'UNAUTHORIZED';
    if (message.includes('forbidden')) return 'FORBIDDEN';
    
    return 'UNKNOWN_ERROR';
  },

  /**
   * Format error for logging
   */
  formatErrorForLogging(error: any, context: ErrorContext = {}): object {
    return {
      type: this.extractErrorType(error),
      message: error.message || 'Unknown error',
      stack: error.stack,
      context: {
        operation: context.operation,
        provider: context.provider,
        toolId: context.toolId,
        jobId: context.jobId,
        userId: context.userId,
        attempt: context.attempt,
        maxRetries: context.maxRetries
      },
      timestamp: new Date().toISOString(),
      retryable: this.isRetryableError(error)
    };
  }
};

// Error handling decorators and higher-order functions
export const ErrorHandlingDecorators = {
  /**
   * Decorator for automatic error handling in class methods
   */
  withErrorHandling(context: ErrorContext = {}) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      const originalMethod = descriptor.value;

      descriptor.value = async function (...args: any[]) {
        try {
          return await originalMethod.apply(this, args);
        } catch (error) {
          const { errorManager } = await import('./error-manager');
          const recoveryResult = await errorManager.handleError(error, {
            ...context,
            operation: `${target.constructor.name}.${propertyKey}`,
            metadata: { args: args.map(arg => typeof arg === 'object' ? '[Object]' : arg) }
          });

          if (recoveryResult.success && recoveryResult.result) {
            return recoveryResult.result;
          }

          throw error;
        }
      };

      return descriptor;
    };
  },

  /**
   * Decorator for retry logic
   */
  withRetry(maxRetries: number = 3, backoffMs: number = 1000) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      const originalMethod = descriptor.value;

      descriptor.value = async function (...args: any[]) {
        let lastError: any;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            return await originalMethod.apply(this, args);
          } catch (error) {
            lastError = error;

            if (attempt < maxRetries && ErrorHandlingUtils.isRetryableError(error)) {
              const errorMessage = error instanceof Error ? error.message : String(error);
              console.warn(`Attempt ${attempt}/${maxRetries} failed, retrying in ${backoffMs}ms:`, errorMessage);
              await new Promise(resolve => setTimeout(resolve, backoffMs * attempt));
            } else {
              break;
            }
          }
        }

        throw lastError;
      };

      return descriptor;
    };
  }
};

// Pre-configured error handling instances for common scenarios
export const CommonErrorHandlers = {
  /**
   * Error handler for AI provider operations
   */
  aiProvider: {
    async handleWithFallback<T>(
      primaryOperation: () => Promise<T>,
      fallbackOperation: () => Promise<T>,
      context: ErrorContext = {}
    ): Promise<T> {
      const enhancedContext = {
        ...context,
        operation: context.operation || 'ai_generation',
        retryOperation: primaryOperation,
        switchProvider: async (provider: string) => {
          if (provider === 'fallback') {
            return await fallbackOperation();
          }
          return await primaryOperation();
        }
      };

      return await ErrorHandlingUtils.withErrorHandling(primaryOperation, enhancedContext);
    }
  },

  /**
   * Error handler for scraping operations
   */
  scraping: {
    async handleWithRetry<T>(
      operation: () => Promise<T>,
      context: ErrorContext = {}
    ): Promise<T> {
      const enhancedContext = {
        ...context,
        operation: context.operation || 'web_scraping',
        retryOperation: operation,
        maxRetries: 3
      };

      return await ErrorHandlingUtils.withErrorHandling(operation, enhancedContext);
    }
  },

  /**
   * Error handler for database operations
   */
  database: {
    async handleWithReconnect<T>(
      operation: () => Promise<T>,
      context: ErrorContext = {}
    ): Promise<T> {
      const enhancedContext = {
        ...context,
        operation: context.operation || 'database_operation',
        retryOperation: operation,
        maxRetries: 5
      };

      return await ErrorHandlingUtils.withErrorHandling(operation, enhancedContext);
    }
  },

  /**
   * Error handler for job processing
   */
  jobProcessing: {
    async handleWithRestart<T>(
      operation: () => Promise<T>,
      jobId: string,
      context: ErrorContext = {}
    ): Promise<T> {
      const enhancedContext = {
        ...context,
        operation: context.operation || 'job_processing',
        jobId,
        retryOperation: operation,
        maxRetries: 3
      };

      return await ErrorHandlingUtils.withErrorHandling(operation, enhancedContext);
    }
  }
};

// Initialize error handling system
export const initializeErrorHandling = async () => {
  console.log('Initializing Enhanced AI System Error Handling...');

  // Import modules dynamically to avoid circular dependencies
  const { errorManager } = await import('./error-manager');
  const { HealthChecker } = await import('../monitoring/health-checker');

  // Start health monitoring
  const healthChecker = new HealthChecker();
  healthChecker.startMonitoring();

  console.log('Error handling system initialized successfully');

  return {
    errorManager,
    healthChecker
  };
};

// Export singleton instances
export const errorHandlingSystem = {
  get errorManager() {
    // Lazy load to avoid circular dependencies
    return require('./error-manager').errorManager;
  },
  get healthChecker() {
    const { HealthChecker } = require('../monitoring/health-checker');
    return new HealthChecker();
  },
  utils: ErrorHandlingUtils,
  decorators: ErrorHandlingDecorators,
  handlers: CommonErrorHandlers
};
