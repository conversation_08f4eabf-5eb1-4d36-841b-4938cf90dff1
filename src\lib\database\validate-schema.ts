/**
 * Database Schema Validation Script
 * 
 * This script validates the database schema after migration
 * to ensure all tables, columns, and constraints are properly created.
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing required Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

interface TableInfo {
  table_name: string;
  column_name: string;
  data_type: string;
  is_nullable: string;
  column_default: string | null;
}

interface ConstraintInfo {
  constraint_name: string;
  table_name: string;
  constraint_type: string;
}

/**
 * Get table schema information
 */
async function getTableSchema(tableName: string): Promise<TableInfo[]> {
  const { data, error } = await supabase.rpc('exec_sql', {
    sql: `
      SELECT 
        table_name,
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_name = '${tableName}' 
      AND table_schema = 'public'
      ORDER BY ordinal_position;
    `
  });

  if (error) {
    throw new Error(`Failed to get schema for table ${tableName}: ${error.message}`);
  }

  return data || [];
}

/**
 * Get table constraints
 */
async function getTableConstraints(tableName: string): Promise<ConstraintInfo[]> {
  const { data, error } = await supabase.rpc('exec_sql', {
    sql: `
      SELECT 
        constraint_name,
        table_name,
        constraint_type
      FROM information_schema.table_constraints 
      WHERE table_name = '${tableName}' 
      AND table_schema = 'public';
    `
  });

  if (error) {
    throw new Error(`Failed to get constraints for table ${tableName}: ${error.message}`);
  }

  return data || [];
}

/**
 * Check if table exists
 */
async function tableExists(tableName: string): Promise<boolean> {
  const { data, error } = await supabase.rpc('exec_sql', {
    sql: `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = '${tableName}'
      );
    `
  });

  if (error) {
    throw new Error(`Failed to check if table ${tableName} exists: ${error.message}`);
  }

  return data?.[0]?.exists || false;
}

/**
 * Validate new tables exist
 */
async function validateNewTables(): Promise<void> {
  console.log('🔍 Validating new tables...');
  
  const expectedTables = [
    'ai_generation_jobs',
    'media_assets',
    'editorial_reviews',
    'bulk_processing_jobs',
    'system_configuration'
  ];

  for (const tableName of expectedTables) {
    const exists = await tableExists(tableName);
    if (exists) {
      console.log(`✅ Table ${tableName} exists`);
    } else {
      console.error(`❌ Table ${tableName} is missing`);
      throw new Error(`Required table ${tableName} not found`);
    }
  }
}

/**
 * Validate tools table enhancements
 */
async function validateToolsTableEnhancements(): Promise<void> {
  console.log('🔍 Validating tools table enhancements...');
  
  const schema = await getTableSchema('tools');
  const columnNames = schema.map(col => col.column_name);
  
  const expectedNewColumns = [
    'scraped_data',
    'ai_generation_status',
    'last_scraped_at',
    'editorial_review_id',
    'ai_generation_job_id',
    'submission_type',
    'submission_source',
    'content_quality_score',
    'last_ai_update'
  ];

  for (const columnName of expectedNewColumns) {
    if (columnNames.includes(columnName)) {
      console.log(`✅ Column tools.${columnName} exists`);
    } else {
      console.error(`❌ Column tools.${columnName} is missing`);
      throw new Error(`Required column tools.${columnName} not found`);
    }
  }
}

/**
 * Validate foreign key relationships
 */
async function validateForeignKeys(): Promise<void> {
  console.log('🔍 Validating foreign key relationships...');
  
  const foreignKeyChecks = [
    {
      table: 'ai_generation_jobs',
      column: 'tool_id',
      references: 'tools(id)'
    },
    {
      table: 'media_assets',
      column: 'tool_id',
      references: 'tools(id)'
    },
    {
      table: 'editorial_reviews',
      column: 'tool_id',
      references: 'tools(id)'
    },
    {
      table: 'tools',
      column: 'editorial_review_id',
      references: 'editorial_reviews(id)'
    },
    {
      table: 'tools',
      column: 'ai_generation_job_id',
      references: 'ai_generation_jobs(id)'
    }
  ];

  for (const fk of foreignKeyChecks) {
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT EXISTS (
          SELECT 1 FROM information_schema.referential_constraints rc
          JOIN information_schema.key_column_usage kcu ON rc.constraint_name = kcu.constraint_name
          WHERE kcu.table_name = '${fk.table}' 
          AND kcu.column_name = '${fk.column}'
        );
      `
    });

    if (error) {
      console.warn(`⚠️  Could not verify foreign key ${fk.table}.${fk.column}: ${error.message}`);
    } else if (data?.[0]?.exists) {
      console.log(`✅ Foreign key ${fk.table}.${fk.column} → ${fk.references} exists`);
    } else {
      console.warn(`⚠️  Foreign key ${fk.table}.${fk.column} → ${fk.references} may be missing`);
    }
  }
}

/**
 * Validate indexes
 */
async function validateIndexes(): Promise<void> {
  console.log('🔍 Validating database indexes...');
  
  const { data, error } = await supabase.rpc('exec_sql', {
    sql: `
      SELECT 
        schemaname,
        tablename,
        indexname,
        indexdef
      FROM pg_indexes 
      WHERE schemaname = 'public' 
      AND (
        tablename IN ('ai_generation_jobs', 'media_assets', 'editorial_reviews', 'bulk_processing_jobs', 'system_configuration')
        OR indexname LIKE 'idx_tools_%'
      )
      ORDER BY tablename, indexname;
    `
  });

  if (error) {
    console.warn(`⚠️  Could not verify indexes: ${error.message}`);
    return;
  }

  const indexes = data || [];
  console.log(`✅ Found ${indexes.length} indexes for enhanced AI system tables`);
  
  // Log some key indexes
  const keyIndexes = indexes.filter((idx: any) =>
    idx.indexname.includes('status') ||
    idx.indexname.includes('tool_id') ||
    idx.indexname.includes('created_at')
  );

  keyIndexes.forEach((idx: any) => {
    console.log(`  📊 ${idx.tablename}.${idx.indexname}`);
  });
}

/**
 * Validate system configuration defaults
 */
async function validateSystemConfiguration(): Promise<void> {
  console.log('🔍 Validating system configuration...');
  
  const { data, error } = await supabase
    .from('system_configuration')
    .select('config_key, config_type')
    .eq('is_active', true);

  if (error) {
    throw new Error(`Failed to validate system configuration: ${error.message}`);
  }

  const configs = data || [];
  console.log(`✅ Found ${configs.length} system configuration entries`);
  
  const expectedConfigs = [
    'ai_provider_openai_enabled',
    'ai_provider_openrouter_enabled',
    'scraping_default_timeout',
    'job_processing_max_concurrent'
  ];

  const existingKeys = configs.map(c => c.config_key);
  
  for (const key of expectedConfigs) {
    if (existingKeys.includes(key)) {
      console.log(`✅ Configuration ${key} exists`);
    } else {
      console.warn(`⚠️  Configuration ${key} is missing`);
    }
  }
}

/**
 * Run complete schema validation
 */
export async function validateSchema(): Promise<void> {
  try {
    console.log('🚀 Starting database schema validation...\n');
    
    await validateNewTables();
    console.log('');
    
    await validateToolsTableEnhancements();
    console.log('');
    
    await validateForeignKeys();
    console.log('');
    
    await validateIndexes();
    console.log('');
    
    await validateSystemConfiguration();
    console.log('');
    
    console.log('🎉 Database schema validation completed successfully!');
    
  } catch (error) {
    console.error('❌ Schema validation failed:', error);
    throw error;
  }
}

// CLI interface
if (require.main === module) {
  validateSchema().catch(process.exit);
}
