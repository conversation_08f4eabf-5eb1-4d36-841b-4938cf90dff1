# Scrape.do API Integration Guide

## Overview

The enhanced AI system now uses scrape.do API instead of Puppeteer for web scraping, providing significant cost optimization and improved reliability. This integration implements intelligent cost reduction strategies targeting 50-70% savings while maintaining 85-90% success rates.

## Key Features

### 🎯 Cost Optimization
- **Pattern-based optimization**: Never-enhance and always-enhance URL patterns
- **Intelligent content analysis**: Three common markdown scenarios handling
- **Credit management**: Configurable thresholds and limits
- **Target savings**: 50-70% cost reduction

### 🖼️ Media Collection
- **Favicon priority**: Extract favicons with fallback strategies
- **OG image extraction**: Parse meta tags for social media images
- **Standard viewport screenshots**: Not fullpage for cost efficiency
- **Image validation**: Check accessibility and metadata

### 📄 Multi-Page Scraping
- **Configurable page types**: Pricing, FAQ, features, about pages
- **Smart discovery**: Navigation links, URL patterns, content analysis
- **Credit-aware decisions**: Queue for later or skip based on available credits
- **Fallback strategies**: Search in main page first

### 🔍 Content Analysis
- **Three scenarios**: Meta+Loading+Content, Meta+Loading+NoContent, Meta-only
- **Quality scoring**: Structure, length, content ratio analysis
- **AI-ready output**: Optimized markdown for LLM consumption
- **Validation**: Error detection and content quality checks

## API Endpoints

### Enhanced Scrape API
```typescript
POST /api/scrape
{
  "url": "https://example.com",
  "options": {
    "captureScreenshot": true,
    "extractImages": true,
    "extractFavicon": true,
    "scrapePricingPage": true,
    "scrapeFAQPage": true
  }
}
```

### Automation Scrape API
```typescript
POST /api/automation/scrape
{
  "url": "https://example.com",
  "priority": "normal",
  "async": false,
  "options": {
    "timeout": 30000,
    "extractImages": true,
    "extractLinks": true
  }
}
```

## Configuration

### Environment Variables
```bash
# Scrape.do API Configuration
SCRAPE_DO_API_KEY=8e7e405ff81145c4afe447610ddb9a7f785f494dddc
SCRAPE_DO_BASE_URL=https://api.scrape.do
SCRAPE_DO_TIMEOUT=30000
SCRAPE_DO_RETRY_ATTEMPTS=3
```

### Cost Optimization Settings
```typescript
// Never enhance patterns (1 credit each - 80% savings)
const NEVER_ENHANCE_PATTERNS = [
  /wikipedia\.org/i,
  /github\.com/i,
  /stackoverflow\.com/i,
  /docs\./i,
  /blog\./i
];

// Always enhance patterns (5 credits each - skip basic attempt)
const ALWAYS_ENHANCE_PATTERNS = [
  /claude\.ai/i,
  /chat\.openai\.com/i,
  /notion\.so/i,
  /app\./i,
  /dashboard\./i
];
```

## Usage Examples

### Basic Enhanced Scraping
```typescript
import { contentProcessor } from '@/lib/scraping/content-processor';

const result = await contentProcessor.processEnhancedScrape({
  url: 'https://example.com',
  options: {
    outputFormat: 'markdown',
    timeout: 30000
  },
  costOptimization: true,
  mediaCollection: true
});
```

### Cost-Optimized Scraping
```typescript
import { costOptimizer } from '@/lib/scraping/cost-optimizer';

const result = await costOptimizer.scrapeWithMaxCostOptimization(url);
```

### Multi-Page Scraping
```typescript
import { multiPageScraper } from '@/lib/scraping/multi-page-scraper';

const decision = await multiPageScraper.discoverAndPlanScraping(url, content);
const results = await multiPageScraper.executeMultiPageScraping(decision);
```

### Media Collection
```typescript
import { mediaExtractor } from '@/lib/scraping/media-extractor';

const images = await mediaExtractor.collectImagesWithPriority(url, content);
```

## Cost Analysis

### Credit Usage
- **Basic scraping**: 1 credit (datacenter proxy)
- **Enhanced scraping**: 5 credits (datacenter + browser)
- **Premium scraping**: 50 credits (residential + browser)

### Optimization Strategies
1. **Pattern matching**: Immediate classification (80% savings)
2. **Content analysis**: Intelligent enhancement decisions
3. **Multi-page control**: Credit-aware page discovery
4. **Quality thresholds**: Accept "good enough" content

### Expected Savings
- **Never-enhance sites**: 4 credits saved per URL (80% reduction)
- **Always-enhance sites**: 1 credit saved per URL (skip basic attempt)
- **Overall target**: 50-70% cost reduction

## Content Quality Analysis

### Three Common Scenarios
1. **Meta + Loading + Content**: Keep basic scraping result
2. **Meta + Loading + No Content**: Enhance with browser rendering
3. **Meta Only**: Enhance with browser rendering

### Quality Metrics
- **Content length**: Minimum 300 characters
- **Word count**: Minimum 50 meaningful words
- **Structure score**: Headings, paragraphs, lists
- **Content ratio**: Clean content vs. total content

## Backward Compatibility

The integration maintains full backward compatibility with existing API endpoints:
- `/api/scrape` - Enhanced with scrape.do but same response format
- `/api/automation/scrape` - Job queue integration updated
- Legacy options supported with automatic conversion

## Testing

### Test Script
```bash
# Run the test script
npx tsx src/lib/scraping/test-scrape-do.ts
```

### Test Coverage
- API connectivity and authentication
- Cost optimization pattern matching
- Basic and enhanced scraping workflows
- Content analysis scenarios
- Media collection functionality

## Monitoring

### Usage Statistics
```typescript
import { scrapeDoClient } from '@/lib/scraping/scrape-do-client';

const stats = await scrapeDoClient.getUsageStatistics();
console.log(`Remaining requests: ${stats.remainingMonthlyRequests}`);
```

### Cost Tracking
- Credits used per request
- Optimization strategy applied
- Estimated savings achieved
- Quality scores and content analysis

## Migration Notes

### From Puppeteer
- No code changes required for existing API calls
- Automatic cost optimization applied
- Enhanced media collection capabilities
- Improved reliability and performance

### Performance Improvements
- Faster scraping (no browser startup time)
- Better error handling and retry logic
- Intelligent content analysis
- Cost-aware decision making

## Support

For issues or questions about the scrape.do integration:
1. Check the test script output for connectivity issues
2. Review cost optimization patterns for unexpected behavior
3. Monitor usage statistics for rate limiting
4. Verify environment variables are set correctly
