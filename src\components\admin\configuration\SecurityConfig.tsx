'use client';

import { useState, useEffect } from 'react';

interface SecurityConfigProps {
  onSave: () => void;
}

interface ProviderTestResult {
  success: boolean;
  error?: string;
}

interface ProviderTestResults {
  openai: ProviderTestResult;
  openrouter: ProviderTestResult;
  scrapeDoAPI: ProviderTestResult;
}

export function SecurityConfig({ }: SecurityConfigProps) {
  const [providerStatus, setProviderStatus] = useState<ProviderTestResults | null>(null);
  const [testing, setTesting] = useState(false);
  const [lastTested, setLastTested] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProviderStatus();
  }, []);

  const loadProviderStatus = async () => {
    try {
      setError(null);

      const response = await fetch('/api/admin/config/test-providers', {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load provider status');
      }

      const data = await response.json();
      setProviderStatus(data.results);
      setLastTested(new Date().toISOString());
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const testAllProviders = async () => {
    try {
      setTesting(true);
      setError(null);

      const response = await fetch('/api/admin/config/test-providers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({ detailed: true })
      });

      if (!response.ok) {
        throw new Error('Provider testing failed');
      }

      const data = await response.json();
      setProviderStatus(data.results);
      setLastTested(new Date().toISOString());
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setTesting(false);
    }
  };

  const testSingleProvider = async (provider: string) => {
    try {
      setTesting(true);
      setError(null);

      const response = await fetch(`/api/admin/config/test-providers?provider=${provider}&detailed=true`, {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (!response.ok) {
        throw new Error(`${provider} testing failed`);
      }

      const data = await response.json();
      
      // Update only the tested provider
      setProviderStatus(prev => prev ? {
        ...prev,
        [provider]: data.result
      } : null);
      
      setLastTested(new Date().toISOString());
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setTesting(false);
    }
  };

  const getStatusColor = (success: boolean) => {
    return success ? 'text-green-400' : 'text-red-400';
  };

  const getStatusIcon = (success: boolean) => {
    return success ? '✓' : '✗';
  };

  const getStatusText = (success: boolean) => {
    return success ? 'Connected' : 'Failed';
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-2">Security & Provider Status</h3>
        <p className="text-gray-400">
          Monitor API provider connections and security status.
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-700 rounded-lg p-4">
          <div className="text-red-400 font-semibold">Error</div>
          <div className="text-red-300 mt-1">{error}</div>
        </div>
      )}

      {/* Provider Status */}
      <div className="bg-zinc-700 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="font-semibold">API Provider Status</h4>
          <div className="flex space-x-2">
            <button
              onClick={testAllProviders}
              disabled={testing}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 px-4 py-2 rounded-lg font-medium transition-colors text-sm"
            >
              {testing ? 'Testing...' : 'Test All'}
            </button>
            <button
              onClick={loadProviderStatus}
              disabled={testing}
              className="bg-zinc-600 hover:bg-zinc-500 disabled:bg-zinc-700 px-4 py-2 rounded-lg font-medium transition-colors text-sm"
            >
              Refresh
            </button>
          </div>
        </div>

        {providerStatus ? (
          <div className="space-y-4">
            {/* OpenAI Status */}
            <div className="flex items-center justify-between p-4 bg-zinc-600 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 rounded-full bg-current" 
                     style={{ color: providerStatus.openai.success ? '#10b981' : '#ef4444' }}></div>
                <div>
                  <div className="font-medium">OpenAI</div>
                  <div className="text-sm text-gray-400">GPT-4o Content Generation</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <span className={`font-medium ${getStatusColor(providerStatus.openai.success)}`}>
                  {getStatusIcon(providerStatus.openai.success)} {getStatusText(providerStatus.openai.success)}
                </span>
                <button
                  onClick={() => testSingleProvider('openai')}
                  disabled={testing}
                  className="bg-zinc-500 hover:bg-zinc-400 disabled:bg-zinc-600 px-3 py-1 rounded text-sm"
                >
                  Test
                </button>
              </div>
            </div>

            {/* OpenRouter Status */}
            <div className="flex items-center justify-between p-4 bg-zinc-600 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 rounded-full bg-current" 
                     style={{ color: providerStatus.openrouter.success ? '#10b981' : '#ef4444' }}></div>
                <div>
                  <div className="font-medium">OpenRouter</div>
                  <div className="text-sm text-gray-400">Gemini 2.5 Pro & Claude Access</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <span className={`font-medium ${getStatusColor(providerStatus.openrouter.success)}`}>
                  {getStatusIcon(providerStatus.openrouter.success)} {getStatusText(providerStatus.openrouter.success)}
                </span>
                <button
                  onClick={() => testSingleProvider('openrouter')}
                  disabled={testing}
                  className="bg-zinc-500 hover:bg-zinc-400 disabled:bg-zinc-600 px-3 py-1 rounded text-sm"
                >
                  Test
                </button>
              </div>
            </div>

            {/* Scrape.do Status */}
            <div className="flex items-center justify-between p-4 bg-zinc-600 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 rounded-full bg-current" 
                     style={{ color: providerStatus.scrapeDoAPI.success ? '#10b981' : '#ef4444' }}></div>
                <div>
                  <div className="font-medium">Scrape.do API</div>
                  <div className="text-sm text-gray-400">Web Scraping & Media Extraction</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <span className={`font-medium ${getStatusColor(providerStatus.scrapeDoAPI.success)}`}>
                  {getStatusIcon(providerStatus.scrapeDoAPI.success)} {getStatusText(providerStatus.scrapeDoAPI.success)}
                </span>
                <button
                  onClick={() => testSingleProvider('scrape-do')}
                  disabled={testing}
                  className="bg-zinc-500 hover:bg-zinc-400 disabled:bg-zinc-600 px-3 py-1 rounded text-sm"
                >
                  Test
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-400">
            Loading provider status...
          </div>
        )}

        {lastTested && (
          <div className="text-xs text-gray-400 mt-4 text-center">
            Last tested: {new Date(lastTested).toLocaleString()}
          </div>
        )}
      </div>

      {/* Security Recommendations */}
      <div className="bg-zinc-700 rounded-lg p-6">
        <h4 className="font-semibold mb-4">Security Recommendations</h4>
        
        <div className="space-y-3">
          <div className="flex items-start space-x-3">
            <span className="text-green-400 mt-1">✓</span>
            <div>
              <div className="font-medium">API Keys Encrypted</div>
              <div className="text-sm text-gray-400">All sensitive configuration data is encrypted at rest</div>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <span className="text-green-400 mt-1">✓</span>
            <div>
              <div className="font-medium">Admin Authentication</div>
              <div className="text-sm text-gray-400">Admin API requires valid authentication key</div>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <span className="text-green-400 mt-1">✓</span>
            <div>
              <div className="font-medium">Configuration Validation</div>
              <div className="text-sm text-gray-400">All configuration changes are validated before saving</div>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <span className="text-yellow-400 mt-1">⚠</span>
            <div>
              <div className="font-medium">Regular Key Rotation</div>
              <div className="text-sm text-gray-400">Consider rotating API keys regularly for enhanced security</div>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <span className="text-yellow-400 mt-1">⚠</span>
            <div>
              <div className="font-medium">Monitor Provider Usage</div>
              <div className="text-sm text-gray-400">Regularly monitor API usage and costs across providers</div>
            </div>
          </div>
        </div>
      </div>

      {/* Environment Information */}
      <div className="bg-zinc-700 rounded-lg p-6">
        <h4 className="font-semibold mb-4">Environment Information</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="text-sm font-medium text-gray-300">Environment</div>
            <div className="text-lg font-mono">{process.env.NODE_ENV || 'development'}</div>
          </div>

          <div>
            <div className="text-sm font-medium text-gray-300">Configuration Source</div>
            <div className="text-lg">Database + Environment</div>
          </div>

          <div>
            <div className="text-sm font-medium text-gray-300">Encryption Status</div>
            <div className="text-lg text-green-400">✓ Enabled</div>
          </div>

          <div>
            <div className="text-sm font-medium text-gray-300">Audit Logging</div>
            <div className="text-lg text-green-400">✓ Active</div>
          </div>
        </div>
      </div>

      {/* Provider Error Details */}
      {providerStatus && Object.values(providerStatus).some(provider => !provider.success) && (
        <div className="bg-red-900/20 border border-red-700 rounded-lg p-6">
          <h4 className="font-semibold text-red-400 mb-4">Provider Connection Issues</h4>
          
          <div className="space-y-3">
            {Object.entries(providerStatus).map(([provider, status]) => {
              if (status.success) return null;
              
              return (
                <div key={provider} className="flex items-start space-x-3">
                  <span className="text-red-400 mt-1">✗</span>
                  <div>
                    <div className="font-medium text-red-300 capitalize">{provider}</div>
                    <div className="text-sm text-red-200">{status.error || 'Connection failed'}</div>
                  </div>
                </div>
              );
            })}
          </div>

          <div className="mt-4 p-3 bg-red-800/20 rounded">
            <div className="text-sm text-red-200">
              <strong>Troubleshooting:</strong>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>Verify API keys are correct and have not expired</li>
                <li>Check network connectivity and firewall settings</li>
                <li>Ensure provider services are not experiencing outages</li>
                <li>Review rate limits and usage quotas</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
