-- Enhanced AI System Database Schema Migration
-- Migration: 001_enhanced_ai_system_schema
-- Description: Add new tables and enhance existing schema for Enhanced AI System
-- Reference: docs/enhanced-ai-system/01-system-architecture.md

-- =====================================================
-- NEW TABLES
-- =====================================================

-- AI Generation Jobs Table
-- Tracks all AI content generation jobs with detailed progress and data storage
CREATE TABLE IF NOT EXISTS ai_generation_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tool_id VARCHAR(255) REFERENCES tools(id) ON DELETE CASCADE,
    job_type VARCHAR(50) NOT NULL CHECK (job_type IN ('scrape', 'generate', 'bulk', 'media_extraction')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    scraped_data JSONB, -- Raw scraped .md content from scrape.do
    ai_prompts JSONB, -- Prompts sent to AI providers
    ai_responses JSONB, -- AI responses and generated content
    error_logs JSONB, -- Detailed error information and stack traces
    processing_options JSONB, -- Job configuration and options
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for ai_generation_jobs
CREATE INDEX IF NOT EXISTS idx_ai_generation_jobs_tool_id ON ai_generation_jobs(tool_id);
CREATE INDEX IF NOT EXISTS idx_ai_generation_jobs_status ON ai_generation_jobs(status);
CREATE INDEX IF NOT EXISTS idx_ai_generation_jobs_job_type ON ai_generation_jobs(job_type);
CREATE INDEX IF NOT EXISTS idx_ai_generation_jobs_created_at ON ai_generation_jobs(created_at DESC);

-- Media Assets Table
-- Stores all media assets (logos, favicons, screenshots) with metadata
CREATE TABLE IF NOT EXISTS media_assets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tool_id VARCHAR(255) REFERENCES tools(id) ON DELETE CASCADE,
    asset_type VARCHAR(20) NOT NULL CHECK (asset_type IN ('logo', 'favicon', 'screenshot', 'og_image')),
    source_url TEXT, -- Original URL where asset was found
    local_path TEXT, -- Local storage path
    cdn_url TEXT, -- CDN URL for serving
    file_size INTEGER CHECK (file_size > 0),
    mime_type VARCHAR(100),
    width INTEGER,
    height INTEGER,
    alt_text TEXT,
    is_primary BOOLEAN DEFAULT FALSE, -- Primary asset for the type
    extraction_method VARCHAR(50), -- 'og_meta', 'favicon_link', 'screenshot', 'manual'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for media_assets
CREATE INDEX IF NOT EXISTS idx_media_assets_tool_id ON media_assets(tool_id);
CREATE INDEX IF NOT EXISTS idx_media_assets_asset_type ON media_assets(asset_type);
CREATE INDEX IF NOT EXISTS idx_media_assets_is_primary ON media_assets(is_primary) WHERE is_primary = TRUE;

-- Editorial Reviews Table
-- Manages manual editorial review process and featured content
CREATE TABLE IF NOT EXISTS editorial_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tool_id VARCHAR(255) REFERENCES tools(id) ON DELETE CASCADE,
    reviewed_by VARCHAR(255) NOT NULL, -- Admin user identifier
    review_status VARCHAR(20) DEFAULT 'pending' CHECK (review_status IN ('pending', 'approved', 'rejected', 'needs_revision')),
    review_date DATE NOT NULL,
    featured_date DATE, -- Date when tool was first featured
    review_notes TEXT, -- Internal review notes
    editorial_text TEXT, -- "was manually vetted by our editorial team and was first featured on [date]"
    quality_score INTEGER CHECK (quality_score >= 1 AND quality_score <= 10),
    content_flags JSONB, -- Array of content issues or flags
    approval_workflow JSONB, -- Workflow state and history
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for editorial_reviews
CREATE INDEX IF NOT EXISTS idx_editorial_reviews_tool_id ON editorial_reviews(tool_id);
CREATE INDEX IF NOT EXISTS idx_editorial_reviews_status ON editorial_reviews(review_status);
CREATE INDEX IF NOT EXISTS idx_editorial_reviews_reviewed_by ON editorial_reviews(reviewed_by);
CREATE INDEX IF NOT EXISTS idx_editorial_reviews_featured_date ON editorial_reviews(featured_date DESC);

-- Bulk Processing Jobs Table
-- Manages bulk operations for processing multiple tools
CREATE TABLE IF NOT EXISTS bulk_processing_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_type VARCHAR(50) NOT NULL CHECK (job_type IN ('text_file', 'json_file', 'manual_entry', 'csv_import')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'paused')),
    total_items INTEGER DEFAULT 0 CHECK (total_items >= 0),
    processed_items INTEGER DEFAULT 0 CHECK (processed_items >= 0),
    successful_items INTEGER DEFAULT 0 CHECK (successful_items >= 0),
    failed_items INTEGER DEFAULT 0 CHECK (failed_items >= 0),
    source_data JSONB NOT NULL, -- Original input data (URLs, tool data, etc.)
    processing_options JSONB, -- Batch size, delays, retry settings, etc.
    results JSONB, -- Processing results, errors, and generated tool IDs
    progress_log JSONB, -- Detailed progress tracking
    created_by VARCHAR(255) NOT NULL, -- Admin user who created the job
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for bulk_processing_jobs
CREATE INDEX IF NOT EXISTS idx_bulk_processing_jobs_status ON bulk_processing_jobs(status);
CREATE INDEX IF NOT EXISTS idx_bulk_processing_jobs_job_type ON bulk_processing_jobs(job_type);
CREATE INDEX IF NOT EXISTS idx_bulk_processing_jobs_created_by ON bulk_processing_jobs(created_by);
CREATE INDEX IF NOT EXISTS idx_bulk_processing_jobs_created_at ON bulk_processing_jobs(created_at DESC);

-- System Configuration Table
-- Stores system-wide configuration with secure handling of sensitive data
CREATE TABLE IF NOT EXISTS system_configuration (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_key VARCHAR(255) UNIQUE NOT NULL,
    config_value JSONB NOT NULL,
    config_type VARCHAR(50) NOT NULL CHECK (config_type IN ('ai_provider', 'scraping', 'job_processing', 'system', 'security')),
    is_sensitive BOOLEAN DEFAULT FALSE, -- Indicates if value should be encrypted
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT,
    validation_schema JSONB, -- JSON schema for validating config_value
    updated_by VARCHAR(255),
    version INTEGER DEFAULT 1, -- Configuration versioning
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for system_configuration
CREATE INDEX IF NOT EXISTS idx_system_configuration_config_type ON system_configuration(config_type);
CREATE INDEX IF NOT EXISTS idx_system_configuration_is_active ON system_configuration(is_active) WHERE is_active = TRUE;
CREATE UNIQUE INDEX IF NOT EXISTS idx_system_configuration_key_active ON system_configuration(config_key) WHERE is_active = TRUE;

-- =====================================================
-- ENHANCE EXISTING TABLES
-- =====================================================

-- Add new columns to existing tools table
-- These columns support the enhanced AI system functionality
ALTER TABLE tools ADD COLUMN IF NOT EXISTS scraped_data JSONB;
ALTER TABLE tools ADD COLUMN IF NOT EXISTS ai_generation_status VARCHAR(20) DEFAULT 'pending' CHECK (ai_generation_status IN ('pending', 'processing', 'completed', 'failed', 'skipped'));
ALTER TABLE tools ADD COLUMN IF NOT EXISTS last_scraped_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE tools ADD COLUMN IF NOT EXISTS editorial_review_id UUID REFERENCES editorial_reviews(id);
ALTER TABLE tools ADD COLUMN IF NOT EXISTS ai_generation_job_id UUID REFERENCES ai_generation_jobs(id);
ALTER TABLE tools ADD COLUMN IF NOT EXISTS submission_type VARCHAR(20) DEFAULT 'admin' CHECK (submission_type IN ('admin', 'user_url', 'user_full'));
ALTER TABLE tools ADD COLUMN IF NOT EXISTS submission_source VARCHAR(50); -- 'admin_panel', 'bulk_import', 'user_submission', 'api'
ALTER TABLE tools ADD COLUMN IF NOT EXISTS content_quality_score INTEGER CHECK (content_quality_score >= 1 AND content_quality_score <= 100);
ALTER TABLE tools ADD COLUMN IF NOT EXISTS last_ai_update TIMESTAMP WITH TIME ZONE;

-- Create indexes for new tools table columns
CREATE INDEX IF NOT EXISTS idx_tools_ai_generation_status ON tools(ai_generation_status);
CREATE INDEX IF NOT EXISTS idx_tools_last_scraped_at ON tools(last_scraped_at DESC);
CREATE INDEX IF NOT EXISTS idx_tools_editorial_review_id ON tools(editorial_review_id);
CREATE INDEX IF NOT EXISTS idx_tools_submission_type ON tools(submission_type);
CREATE INDEX IF NOT EXISTS idx_tools_content_quality_score ON tools(content_quality_score DESC);

-- =====================================================
-- TRIGGERS AND FUNCTIONS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_ai_generation_jobs_updated_at BEFORE UPDATE ON ai_generation_jobs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_media_assets_updated_at BEFORE UPDATE ON media_assets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_editorial_reviews_updated_at BEFORE UPDATE ON editorial_reviews FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bulk_processing_jobs_updated_at BEFORE UPDATE ON bulk_processing_jobs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_configuration_updated_at BEFORE UPDATE ON system_configuration FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- INITIAL DATA SEEDING
-- =====================================================

-- Insert default system configuration
INSERT INTO system_configuration (config_key, config_value, config_type, description) VALUES
('ai_provider_openai_enabled', 'true'::jsonb, 'ai_provider', 'Enable OpenAI GPT-4o integration'),
('ai_provider_openrouter_enabled', 'true'::jsonb, 'ai_provider', 'Enable OpenRouter Gemini 2.5 Pro Preview integration'),
('scraping_default_timeout', '30000'::jsonb, 'scraping', 'Default timeout for scraping operations in milliseconds'),
('scraping_cost_optimization_enabled', 'true'::jsonb, 'scraping', 'Enable cost optimization patterns for scraping'),
('job_processing_max_concurrent', '5'::jsonb, 'job_processing', 'Maximum concurrent job processing'),
('job_processing_retry_attempts', '3'::jsonb, 'job_processing', 'Default retry attempts for failed jobs'),
('bulk_processing_batch_size', '10'::jsonb, 'job_processing', 'Default batch size for bulk operations'),
('content_generation_quality_threshold', '70'::jsonb, 'system', 'Minimum quality score for auto-approval')
ON CONFLICT (config_key) DO NOTHING;

-- =====================================================
-- MIGRATION COMPLETION
-- =====================================================

-- Log migration completion
INSERT INTO system_configuration (config_key, config_value, config_type, description) VALUES
('migration_001_completed', json_build_object('completed_at', CURRENT_TIMESTAMP, 'version', '1.0.0')::jsonb, 'system', 'Enhanced AI System schema migration completion marker')
ON CONFLICT (config_key) DO UPDATE SET
    config_value = EXCLUDED.config_value,
    updated_at = CURRENT_TIMESTAMP;
