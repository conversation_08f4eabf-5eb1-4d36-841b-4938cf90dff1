/**
 * Content Analyzer for Scrape.do Integration
 * Handles the three common markdown scraping scenarios and content quality analysis
 */

import { ContentAnalysis, ValidationResult } from './types';

export class ContentAnalyzer {
  
  /**
   * Analyze content quality and determine if enhanced scraping is needed
   * Handles three common scenarios:
   * 1. Meta + Loading + Full Content = DON'T re-scrape
   * 2. Meta + Loading + No Content = DO re-scrape  
   * 3. Meta Only (minimal content) = DO re-scrape
   */
  analyzeContentQuality(content: string, url?: string): ContentAnalysis {
    const analysis: ContentAnalysis = {
      hasMetaTags: false,
      hasLoadingIndicators: false,
      hasSubstantialContent: false,
      hasStructure: false,
      contentRatio: 0,
      needsEnhancedScraping: false,
      confidence: 0
    };

    // 1. Check for meta tags presence
    analysis.hasMetaTags = this.hasMetaTags(content);

    // 2. Check for loading indicators
    analysis.hasLoadingIndicators = this.hasLoadingIndicators(content);

    // 3. Analyze substantial content (excluding meta tags and loading indicators)
    const cleanContent = this.extractMainContent(content);
    analysis.hasSubstantialContent = this.hasSubstantialContent(cleanContent);

    // 4. Check for content structure
    analysis.hasStructure = this.hasContentStructure(cleanContent);

    // 5. Calculate content-to-noise ratio
    analysis.contentRatio = this.calculateContentRatio(content, cleanContent);

    // 6. Make intelligent decision based on analysis
    analysis.needsEnhancedScraping = this.shouldEnhanceScraping(analysis);
    analysis.confidence = this.calculateConfidence(analysis);

    // 7. Additional check: Known sites that require browser rendering
    if (url && this.checkKnownBrowserRequiredSites(url)) {
      analysis.needsEnhancedScraping = true;
      analysis.scenario = 'known_browser_required';
    }

    // 8. Log the analysis decision
    this.logAnalysisDecision(analysis);

    return analysis;
  }

  /**
   * Check for substantial meta tag presence
   */
  private hasMetaTags(content: string): boolean {
    const metaTagCount = (content.match(/<meta\s+/gi) || []).length;
    return metaTagCount > 5; // Substantial meta tag presence
  }

  /**
   * Check for loading indicators
   */
  private hasLoadingIndicators(content: string): boolean {
    const loadingPatterns = [
      /loading\.{3}/gi,
      /please\s+wait/gi,
      /loading-spinner/gi,
      /loader/gi,
      /<div[^>]*class="[^"]*loading[^"]*"/gi,
      /<div[^>]*id="[^"]*loading[^"]*"/gi
    ];

    return loadingPatterns.some(pattern => pattern.test(content));
  }

  /**
   * Extract main content by removing meta tags, scripts, styles, and loading indicators
   */
  private extractMainContent(content: string): string {
    // Remove meta tags, scripts, styles, and loading indicators
    let cleanContent = content
      .replace(/<meta[^>]*>/gi, '')
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .replace(/<link[^>]*>/gi, '')
      .replace(/loading\.{3}|please\s+wait|loading-spinner/gi, '')
      .replace(/<div[^>]*class="[^"]*loading[^"]*"[^>]*>[\s\S]*?<\/div>/gi, '')
      .replace(/<nav[^>]*>[\s\S]*?<\/nav>/gi, '')
      .replace(/<footer[^>]*>[\s\S]*?<\/footer>/gi, '');

    // Remove excessive whitespace
    cleanContent = cleanContent.replace(/\s+/g, ' ').trim();

    return cleanContent;
  }

  /**
   * Check if content has substantial meaningful text
   */
  private hasSubstantialContent(cleanContent: string): boolean {
    // Check if content is JSON/API response
    if (this.isJsonContent(cleanContent)) {
      console.log(`🔍 JSON content detected (${cleanContent.length} chars) - considering substantial`);
      return cleanContent.length > 200; // JSON responses are substantial if they have reasonable length
    }

    const wordCount = cleanContent.split(/\s+/).filter(word => word.length > 2).length;
    const meaningfulTextRegex = /[a-zA-Z]{10,}/;
    const sentenceCount = cleanContent.split(/[.!?]+/).length;

    console.log(`🔍 Text analysis: ${wordCount} words, ${sentenceCount} sentences, length: ${cleanContent.length}`);

    return (
      wordCount > 100 &&                    // At least 100 meaningful words
      meaningfulTextRegex.test(cleanContent) && // Has substantial text blocks
      sentenceCount > 5                     // Has multiple sentences
    );
  }

  /**
   * Check if content is JSON format
   */
  private isJsonContent(content: string): boolean {
    const trimmed = content.trim();

    // Check for JSON object or array
    const isJsonStructure = (trimmed.startsWith('{') && trimmed.endsWith('}')) ||
                           (trimmed.startsWith('[') && trimmed.endsWith(']'));

    // Also check for JSON-like content in markdown format
    const hasJsonBlocks = /```json\s*\{[\s\S]*?\}\s*```/i.test(content) ||
                         /```\s*\{[\s\S]*?\}\s*```/i.test(content);

    // Check for key-value patterns typical in API responses
    const hasApiPatterns = /"[^"]+"\s*:\s*[^,}]+/g.test(content) && content.includes('{');

    return isJsonStructure || hasJsonBlocks || hasApiPatterns;
  }

  /**
   * Check if content has proper structure
   */
  private hasContentStructure(cleanContent: string): boolean {
    // JSON content has inherent structure
    if (this.isJsonContent(cleanContent)) {
      return true;
    }

    const hasHeadings = /<h[1-6]|^#{1,6}\s/m.test(cleanContent);
    const hasParagraphs = cleanContent.split(/\n\s*\n/).length > 3;
    const hasLists = /^[\*\-\+]\s|^\d+\.\s/m.test(cleanContent);
    const hasLinks = /<a\s+href|^\[.*\]\(/m.test(cleanContent);

    // Content has structure if it has headings plus at least one other structural element
    return hasHeadings && (hasParagraphs || hasLists || hasLinks);
  }

  /**
   * Calculate content-to-noise ratio
   */
  private calculateContentRatio(fullContent: string, cleanContent: string): number {
    if (fullContent.length === 0) return 0;
    return cleanContent.length / fullContent.length;
  }

  /**
   * Determine if enhanced scraping is needed based on the three common scenarios
   */
  private shouldEnhanceScraping(analysis: ContentAnalysis): boolean {
    // Scenario 1: Meta + Loading + Full Content = DON'T re-scrape
    if (analysis.hasMetaTags && analysis.hasLoadingIndicators && analysis.hasSubstantialContent) {
      analysis.scenario = 'meta_loading_content_sufficient';
      return false;
    }

    // Scenario 2: Meta + Loading + No Content = DO re-scrape
    if (analysis.hasMetaTags && analysis.hasLoadingIndicators && !analysis.hasSubstantialContent) {
      analysis.scenario = 'meta_loading_no_content';
      return true;
    }

    // Scenario 3: Meta Only (minimal content) = DO re-scrape
    if (analysis.hasMetaTags && !analysis.hasSubstantialContent && !analysis.hasStructure) {
      analysis.scenario = 'meta_only_minimal';
      return true;
    }

    // Scenario 4: Loading indicators with insufficient content = DO re-scrape
    if (analysis.hasLoadingIndicators && !analysis.hasSubstantialContent) {
      analysis.scenario = 'loading_insufficient_content';
      return true;
    }

    // Scenario 5: No substantial content regardless of other factors = DO re-scrape
    if (!analysis.hasSubstantialContent) {
      analysis.scenario = 'no_substantial_content';
      return true;
    }

    // Scenario 6: Low content ratio (mostly noise/meta) = DO re-scrape
    if (analysis.contentRatio < 0.15) {
      analysis.scenario = 'low_content_ratio';
      return true;
    }

    // Scenario 7: Has content but no structure (might be incomplete) = CONDITIONAL re-scrape
    if (analysis.hasSubstantialContent && !analysis.hasStructure && analysis.contentRatio < 0.3) {
      analysis.scenario = 'content_no_structure';
      return true;
    }

    // Content appears sufficient
    analysis.scenario = 'content_sufficient';
    return false;
  }

  /**
   * Log content analysis decision with detailed reasoning
   */
  private logAnalysisDecision(analysis: ContentAnalysis): void {
    const decision = analysis.needsEnhancedScraping ? 'ENHANCING scraping' : 'KEEPING content';
    const ratio = analysis.contentRatio.toFixed(2);

    switch (analysis.scenario) {
      case 'meta_loading_content_sufficient':
        console.log('Content analysis: Meta tags + Loading indicators + Substantial content detected - KEEPING content');
        break;
      case 'meta_loading_no_content':
        console.log('Content analysis: Meta tags + Loading indicators but no substantial content - ENHANCING scraping');
        break;
      case 'meta_only_minimal':
        console.log('Content analysis: Only meta tags detected, minimal content - ENHANCING scraping');
        break;
      case 'loading_insufficient_content':
        console.log('Content analysis: Loading indicators with insufficient content - ENHANCING scraping');
        break;
      case 'no_substantial_content':
        console.log('Content analysis: No substantial content detected - ENHANCING scraping');
        break;
      case 'low_content_ratio':
        console.log(`Content analysis: Low content ratio (${ratio}) - ENHANCING scraping`);
        break;
      case 'content_no_structure':
        console.log('Content analysis: Content present but lacks structure - ENHANCING scraping');
        break;
      case 'content_sufficient':
        console.log(`Content analysis: Content sufficient (ratio: ${ratio}, confidence: ${analysis.confidence}) - KEEPING content`);
        break;
      case 'known_browser_required':
        console.log('Content analysis: Known browser-required site - ENHANCING scraping');
        break;
      default:
        console.log(`Content analysis: ${analysis.scenario} - ${decision}`);
    }
  }

  /**
   * Calculate confidence score for the analysis
   */
  private calculateConfidence(analysis: ContentAnalysis): number {
    let confidence = 0;

    if (analysis.hasSubstantialContent) confidence += 40;
    if (analysis.hasStructure) confidence += 30;
    if (analysis.contentRatio > 0.2) confidence += 20;
    if (!analysis.hasLoadingIndicators) confidence += 10;

    return Math.min(100, confidence);
  }

  /**
   * Check for known sites that require browser rendering
   */
  private checkKnownBrowserRequiredSites(url: string): boolean {
    const browserRequiredPatterns = [
      // AI/ML Platforms (like claude.ai)
      /claude\.ai/i,
      /openai\.com/i,
      /chat\.openai\.com/i,
      /bard\.google\.com/i,
      /character\.ai/i,

      // Modern SaaS platforms
      /notion\.so/i,
      /figma\.com/i,
      /canva\.com/i,
      /miro\.com/i,
      /airtable\.com/i,

      // React/Vue/Angular heavy sites
      /vercel\.app/i,
      /netlify\.app/i,
      /\.vercel\.com/i,

      // Common SPA frameworks indicators
      /app\./i,  // app.example.com subdomains often use SPAs
      /dashboard\./i, // dashboard.example.com

      // Sites with known client-side rendering
      /discord\.com/i,
      /slack\.com/i,
      /teams\.microsoft\.com/i
    ];

    const requiresBrowser = browserRequiredPatterns.some(pattern => pattern.test(url));

    if (requiresBrowser) {
      console.log(`URL ${url} matches known browser-required pattern - will use enhanced scraping`);
    }

    return requiresBrowser;
  }

  /**
   * Validate content quality for AI processing
   */
  validateContentQuality(content: string, url: string): ValidationResult {
    const issues: string[] = [];
    const minimumLength = 200;

    // Length validation
    if (content.length < minimumLength) {
      issues.push(`Content too short (${content.length} chars, minimum ${minimumLength})`);
    }

    // Structure validation
    if (!this.hasValidStructure(content)) {
      issues.push('Content lacks proper structure (headings, paragraphs)');
    }

    // Error page detection
    if (this.containsErrorIndicators(content)) {
      issues.push('Content appears to be an error page');
    }

    // Meaningful content check
    if (!this.hasMeaningfulContent(content)) {
      issues.push('Content lacks meaningful text or appears to be placeholder');
    }

    // AI readiness check
    if (!this.isAIReady(content)) {
      issues.push('Content not properly formatted for AI processing');
    }

    return {
      isValid: issues.length === 0,
      issues,
      contentLength: content.length,
      url,
      qualityScore: this.calculateQualityScore(content)
    };
  }

  /**
   * Check if content has valid structure
   */
  private hasValidStructure(content: string): boolean {
    const hasHeadings = /<h[1-6]|^#{1,6}\s/.test(content);
    const hasParagraphs = content.split('\n\n').length > 3;
    const hasLists = /^[\*\-\+]\s|^\d+\.\s/m.test(content);

    return hasHeadings && (hasParagraphs || hasLists);
  }

  /**
   * Check for error indicators
   */
  private containsErrorIndicators(content: string): boolean {
    const errorIndicators = ['404', 'access denied', 'forbidden', 'not found'];
    const lowerContent = content.toLowerCase();

    return errorIndicators.some(indicator => lowerContent.includes(indicator));
  }

  /**
   * Check for meaningful content
   */
  private hasMeaningfulContent(content: string): boolean {
    // Check for meaningful text patterns
    const meaningfulTextRegex = /[a-zA-Z]{10,}/;
    const wordCount = content.split(/\s+/).filter(word => word.length > 3).length;

    return meaningfulTextRegex.test(content) && wordCount > 50;
  }

  /**
   * Check if content is AI-ready
   */
  private isAIReady(content: string): boolean {
    // Check if content is properly formatted for AI consumption
    const hasStructuredData = /^#{1,3}\s/.test(content); // Has markdown headers
    const hasCleanText = !/<script|<style|<nav|<footer/i.test(content); // No unwanted HTML
    const hasReasonableLength = content.length > 100 && content.length < 100000;

    return hasStructuredData && hasCleanText && hasReasonableLength;
  }

  /**
   * Calculate overall quality score
   */
  private calculateQualityScore(content: string): number {
    let score = 0;

    // Length score (0-30 points)
    const lengthScore = Math.min(30, (content.length / 2000) * 30);
    score += lengthScore;

    // Structure score (0-25 points)
    const headingCount = (content.match(/<h[1-6]|^#{1,6}\s/g) || []).length;
    const structureScore = Math.min(25, headingCount * 5);
    score += structureScore;

    // Content diversity score (0-25 points)
    const uniqueWords = new Set(content.toLowerCase().split(/\s+/)).size;
    const diversityScore = Math.min(25, (uniqueWords / 100) * 25);
    score += diversityScore;

    // Readability score (0-20 points)
    const sentences = content.split(/[.!?]+/).length;
    const avgWordsPerSentence = content.split(/\s+/).length / sentences;
    const readabilityScore = avgWordsPerSentence > 10 && avgWordsPerSentence < 25 ? 20 : 10;
    score += readabilityScore;

    return Math.round(score);
  }
}
