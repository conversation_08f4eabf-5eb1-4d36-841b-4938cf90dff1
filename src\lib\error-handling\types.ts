/**
 * Comprehensive type definitions for the Enhanced AI System Error Handling
 */

export enum ErrorCategory {
  NETWORK = 'network',
  SCRAPING = 'scraping',
  AI_GENERATION = 'ai_generation',
  VALIDATION = 'validation',
  STORAGE = 'storage',
  AUTHENTICATION = 'authentication',
  RATE_LIMITING = 'rate_limiting',
  SYSTEM = 'system',
  JOB_PROCESSING = 'job_processing',
  CONTENT_GENERATION = 'content_generation'
}

export enum ErrorSeverity {
  CRITICAL = 'critical',    // System-wide failure, immediate attention required
  HIGH = 'high',           // Feature failure, affects user experience
  MEDIUM = 'medium',       // Partial failure, degraded functionality
  LOW = 'low',            // Minor issues, logging only
  INFO = 'info'           // Informational, no action required
}

export interface ImpactAssessment {
  userImpact: 'low' | 'medium' | 'high' | 'critical';
  systemImpact: 'low' | 'medium' | 'high' | 'critical';
  businessImpact: 'low' | 'medium' | 'high' | 'critical';
}

export interface ErrorClassification {
  category: ErrorCategory;
  severity: ErrorSeverity;
  retryable: boolean;
  autoRecoverable: boolean;
  requiresManualIntervention: boolean;
  affectedSystems: string[];
  estimatedImpact: ImpactAssessment;
}

export interface ErrorContext {
  operation?: string;
  provider?: 'openai' | 'openrouter' | 'scrape.do';
  toolId?: string;
  jobId?: string;
  userId?: string;
  attempt?: number;
  maxRetries?: number;
  currentProvider?: string;
  retryOperation?: () => Promise<any>;
  switchProvider?: (provider: string) => Promise<any>;
  processChunk?: (chunk: any) => Promise<any>;
  content?: string;
  maxTokens?: number;
  metadata?: Record<string, any>;
}

export interface SystemError {
  id: string;
  type: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  message: string;
  stack?: string;
  context: ErrorContext;
  classification: ErrorClassification;
  timestamp: string;
  originalError: any;
}

export interface RecoveryStrategy {
  name: string;
  execute: (error: SystemError, context: ErrorContext) => Promise<RecoveryResult>;
  applicableErrors: string[];
  priority: number;
  maxRetries?: number;
  backoffStrategy?: 'linear' | 'exponential' | 'fixed';
}

export interface RecoveryResult {
  success: boolean;
  strategy: string;
  attemptsUsed?: number;
  alternativeUsed?: string;
  chunksProcessed?: number;
  result?: any;
  error?: string;
  finalError?: string;
  fallbackError?: string;
  splittingError?: string;
  timestamp: string;
  requiresManualIntervention?: boolean;
  nextAction?: string;
  estimatedRecoveryTime?: number;
}

export interface ErrorMetrics {
  count: number;
  lastOccurrence: Date | null;
  frequency: number; // errors per hour
  trend: 'increasing' | 'decreasing' | 'stable';
  consecutiveFailures: number;
  recoverySuccessRate: number;
  averageRecoveryTime?: number;
  impactScore?: number;
}

export interface AlertThreshold {
  maxFrequency: number; // per hour
  maxConsecutiveFailures: number;
  escalationTime: number; // milliseconds
  notificationChannels?: string[];
  severity?: ErrorSeverity;
}

export interface Alert {
  id: string;
  type: 'frequency_exceeded' | 'consecutive_failures' | 'system_degraded' | 'manual_intervention_required';
  errorType: string;
  severity: ErrorSeverity;
  message: string;
  currentFrequency?: number;
  consecutiveFailures?: number;
  threshold?: number;
  timestamp: string;
  acknowledged: boolean;
  resolvedAt?: string;
  escalated: boolean;
  notificationsSent: string[];
}

export interface TimeRange {
  start: Date;
  end: Date;
}

export interface ErrorReport {
  timeRange: TimeRange;
  summary: {
    totalErrors: number;
    errorsByCategory: Record<ErrorCategory, number>;
    errorsBySeverity: Record<ErrorSeverity, number>;
    topErrors: Array<{ type: string; count: number; severity: ErrorSeverity }>;
    recoverySuccessRate: number;
    averageRecoveryTime: number;
  };
  trends: {
    errorFrequencyTrend: 'increasing' | 'decreasing' | 'stable';
    recoverySuccessRate: number;
    meanTimeToRecovery: number;
    systemHealthScore: number;
  };
  recommendations: string[];
  criticalIssues: SystemError[];
  systemImpact: ImpactAssessment;
}

export interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime?: number;
  error?: string;
  timestamp: Date;
  details?: Record<string, any>;
  lastSuccessful?: Date;
  consecutiveFailures?: number;
}

export interface HealthCheck {
  name: string;
  check: () => Promise<HealthCheckResult>;
  interval: number; // milliseconds
  timeout: number;  // milliseconds
  retries: number;
  critical: boolean;
  dependencies?: string[];
}

export interface SystemHealthStatus {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  checks: Record<string, HealthCheckResult>;
  timestamp: Date;
  summary: {
    total: number;
    healthy: number;
    degraded: number;
    unhealthy: number;
  };
  criticalIssues: string[];
  recommendations: string[];
}

export interface ManualInterventionProcedure {
  triggerConditions: string[];
  severity: ErrorSeverity;
  escalationPath: EscalationPath;
  requiredActions: ManualAction[];
  timeToResolve: string;
  documentationRequired: boolean;
  automationOpportunities?: string[];
}

export interface EscalationPath {
  immediate: string;
  if_unresolved_15min?: string;
  if_unresolved_30min?: string;
  if_unresolved_1h?: string;
  if_unresolved_4h?: string;
  if_unresolved_24h?: string;
}

export interface ManualAction {
  description: string;
  priority: 'immediate' | 'high' | 'medium' | 'low';
  estimatedTime: string;
  requiredRole: string;
  documentation?: string;
  automatable: boolean;
}

export interface ErrorDefinition {
  category: ErrorCategory;
  severity: ErrorSeverity;
  retryable: boolean;
  autoRecoverable: boolean;
  maxRetries?: number;
  backoffStrategy?: 'linear' | 'exponential' | 'fixed';
  fallbackStrategy?: string;
  retryAfter?: number; // milliseconds
  requiresManualIntervention?: boolean;
  description?: string;
  commonCauses?: string[];
  resolutionSteps?: string[];
}

export interface ChaosTestType {
  name: string;
  description: string;
  targetSystems: string[];
  expectedBehavior: string;
  recoveryTime: number;
  severity: ErrorSeverity;
}

export interface ChaosTestResult {
  testType: string;
  success: boolean;
  recoveryTime: number;
  errors: SystemError[];
  recommendations: string[];
  systemBehavior: 'expected' | 'unexpected' | 'degraded';
  impactAssessment: ImpactAssessment;
}

export interface ErrorHandlingConfig {
  enableAutoRecovery: boolean;
  maxRetryAttempts: number;
  retryBackoffMultiplier: number;
  alertingEnabled: boolean;
  healthCheckInterval: number;
  errorRetentionDays: number;
  criticalErrorNotification: boolean;
  manualInterventionThreshold: ErrorSeverity;
  systemHealthThreshold: number; // percentage
}

export interface RecoveryContext {
  originalOperation: () => Promise<any>;
  fallbackOperations: Array<() => Promise<any>>;
  maxRetries: number;
  currentAttempt: number;
  backoffDelay: number;
  timeoutMs: number;
  metadata: Record<string, any>;
}

// Error type definitions for specific error scenarios
export const ERROR_TYPES = {
  // Network Errors
  NETWORK_TIMEOUT: 'NETWORK_TIMEOUT',
  DNS_RESOLUTION_FAILED: 'DNS_RESOLUTION_FAILED',
  CONNECTION_REFUSED: 'CONNECTION_REFUSED',
  SSL_CERTIFICATE_ERROR: 'SSL_CERTIFICATE_ERROR',

  // Scraping Errors
  SCRAPE_DO_API_ERROR: 'SCRAPE_DO_API_ERROR',
  CONTENT_EXTRACTION_FAILED: 'CONTENT_EXTRACTION_FAILED',
  INVALID_URL: 'INVALID_URL',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',

  // AI Generation Errors
  OPENAI_RATE_LIMIT: 'OPENAI_RATE_LIMIT',
  OPENROUTER_INSUFFICIENT_CREDITS: 'OPENROUTER_INSUFFICIENT_CREDITS',
  CONTEXT_LENGTH_EXCEEDED: 'CONTEXT_LENGTH_EXCEEDED',
  INVALID_API_KEY: 'INVALID_API_KEY',
  MODEL_UNAVAILABLE: 'MODEL_UNAVAILABLE',

  // Validation Errors
  CONTENT_QUALITY_BELOW_THRESHOLD: 'CONTENT_QUALITY_BELOW_THRESHOLD',
  SCHEMA_VALIDATION_FAILED: 'SCHEMA_VALIDATION_FAILED',
  REQUIRED_FIELD_MISSING: 'REQUIRED_FIELD_MISSING',

  // Storage Errors
  DATABASE_CONNECTION_LOST: 'DATABASE_CONNECTION_LOST',
  STORAGE_QUOTA_EXCEEDED: 'STORAGE_QUOTA_EXCEEDED',
  DATA_CORRUPTION: 'DATA_CORRUPTION',

  // Job Processing Errors
  JOB_TIMEOUT: 'JOB_TIMEOUT',
  JOB_CANCELLED: 'JOB_CANCELLED',
  WORKER_UNAVAILABLE: 'WORKER_UNAVAILABLE',

  // System Errors
  MEMORY_EXHAUSTED: 'MEMORY_EXHAUSTED',
  CPU_OVERLOAD: 'CPU_OVERLOAD',
  DISK_SPACE_LOW: 'DISK_SPACE_LOW',

  // Migration-Specific Errors
  PUPPETEER_MIGRATION_ERROR: 'PUPPETEER_MIGRATION_ERROR',
  LEGACY_JOB_CONVERSION_FAILED: 'LEGACY_JOB_CONVERSION_FAILED',
  DATA_MIGRATION_FAILED: 'DATA_MIGRATION_FAILED'
} as const;

export type ErrorType = typeof ERROR_TYPES[keyof typeof ERROR_TYPES];
