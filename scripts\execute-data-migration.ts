#!/usr/bin/env tsx

/**
 * Execute Data Migration - Task 4.1
 * 
 * Main script for executing comprehensive data migration with backup,
 * validation, and rollback capabilities.
 */

import { config } from 'dotenv';
import { DataMigrationExecutor } from '../src/lib/migration/data-migration-executor';
import { MigrationValidator } from '../src/lib/migration/migration-validator';
import { RollbackManager } from '../src/lib/migration/rollback-manager';

// Load environment variables
config({ path: '.env.local' });

interface MigrationOptions {
  skipBackup?: boolean;
  skipValidation?: boolean;
  enableRollback?: boolean;
  dryRun?: boolean;
  batchSize?: number;
}

async function executeMigration(options: MigrationOptions = {}): Promise<void> {
  const startTime = Date.now();
  
  console.log('🚀 TASK 4.1: DATA MIGRATION EXECUTION');
  console.log('=' .repeat(60));
  console.log(`📅 Started: ${new Date().toISOString()}`);
  console.log(`⚙️ Options:`, options);
  console.log('');

  try {
    // Initialize migration executor
    const migrationConfig = {
      enableRollback: options.enableRollback !== false,
      validateIntegrity: !options.skipValidation,
      batchSize: options.batchSize || 50
    };

    const executor = new DataMigrationExecutor(migrationConfig);

    if (options.dryRun) {
      console.log('🔍 DRY RUN MODE - No actual changes will be made');
      console.log('');
      
      // Perform validation only
      const validator = new MigrationValidator();
      const validationResult = await validator.validateMigration();
      
      console.log(validator.generateReport(validationResult));
      
      if (!validationResult.isValid) {
        console.log('❌ Dry run validation failed - migration would fail');
        process.exit(1);
      }
      
      console.log('✅ Dry run validation passed - migration ready to execute');
      return;
    }

    // Execute the migration
    console.log('🔄 Executing data migration...');
    const migrationResult = await executor.executeMigration();

    if (!migrationResult.success) {
      console.log('❌ Migration failed:');
      migrationResult.errors.forEach(error => console.log(`   • ${error}`));
      
      if (migrationResult.warnings.length > 0) {
        console.log('⚠️ Warnings:');
        migrationResult.warnings.forEach(warning => console.log(`   • ${warning}`));
      }
      
      process.exit(1);
    }

    // Display migration results
    console.log('');
    console.log('📊 MIGRATION RESULTS:');
    console.log('=' .repeat(40));
    console.log(`✅ Status: SUCCESS`);
    console.log(`📦 Backup: ${migrationResult.backupPath}`);
    console.log(`🔧 Tools Migrated: ${migrationResult.migratedTools}`);
    console.log(`📋 Jobs Migrated: ${migrationResult.migratedJobs}`);
    console.log(`⚙️ Config Entries: ${migrationResult.migratedConfig}`);
    console.log(`⏱️ Duration: ${migrationResult.duration}ms`);
    console.log(`🔄 Rollback Available: ${migrationResult.rollbackAvailable ? 'Yes' : 'No'}`);

    if (migrationResult.warnings.length > 0) {
      console.log('');
      console.log('⚠️ WARNINGS:');
      migrationResult.warnings.forEach(warning => console.log(`   • ${warning}`));
    }

    // Post-migration validation
    if (!options.skipValidation) {
      console.log('');
      console.log('🔍 Running post-migration validation...');
      
      const validator = new MigrationValidator();
      const validationResult = await validator.validateMigration();
      
      console.log('');
      console.log(validator.generateReport(validationResult));
      
      if (!validationResult.isValid) {
        console.log('');
        console.log('❌ Post-migration validation failed!');
        
        if (migrationResult.rollbackAvailable && migrationResult.backupPath) {
          console.log('🔄 Initiating automatic rollback...');
          
          const rollbackManager = new RollbackManager();
          const rollbackResult = await rollbackManager.executeRollback(migrationResult.backupPath);
          
          if (rollbackResult.success) {
            console.log('✅ Rollback completed successfully');
          } else {
            console.log('❌ Rollback failed:');
            rollbackResult.errors.forEach(error => console.log(`   • ${error}`));
          }
        }
        
        process.exit(1);
      }
      
      console.log('✅ Post-migration validation passed');
    }

    // Final summary
    const totalDuration = Date.now() - startTime;
    console.log('');
    console.log('🎉 MIGRATION COMPLETED SUCCESSFULLY!');
    console.log('=' .repeat(60));
    console.log(`📅 Completed: ${new Date().toISOString()}`);
    console.log(`⏱️ Total Duration: ${totalDuration}ms`);
    console.log(`📊 Summary: ${migrationResult.migratedTools} tools, ${migrationResult.migratedConfig} configs migrated`);
    console.log('');
    console.log('🔄 Next Steps:');
    console.log('   • Update integration plan document');
    console.log('   • Run system tests');
    console.log('   • Monitor system performance');
    console.log('   • Proceed to Task 4.2: System Testing and Validation');

  } catch (error: any) {
    console.error('');
    console.error('❌ MIGRATION EXECUTION FAILED:');
    console.error('=' .repeat(60));
    console.error(`Error: ${error.message}`);
    console.error(`Stack: ${error.stack}`);
    
    const totalDuration = Date.now() - startTime;
    console.error(`Duration: ${totalDuration}ms`);
    
    process.exit(1);
  }
}

async function showMigrationStatus(): Promise<void> {
  console.log('📊 MIGRATION STATUS CHECK');
  console.log('=' .repeat(40));
  
  try {
    const validator = new MigrationValidator();
    const result = await validator.validateMigration();
    
    console.log(validator.generateReport(result));
    
    if (result.isValid) {
      console.log('✅ System is ready for migration');
    } else {
      console.log('❌ System has validation issues');
      process.exit(1);
    }
    
  } catch (error: any) {
    console.error('❌ Status check failed:', error.message);
    process.exit(1);
  }
}

async function listBackups(): Promise<void> {
  console.log('📦 AVAILABLE BACKUPS');
  console.log('=' .repeat(30));
  
  try {
    const rollbackManager = new RollbackManager();
    const backups = await rollbackManager.listAvailableBackups();
    
    if (backups.length === 0) {
      console.log('No backups found');
    } else {
      backups.forEach((backup, index) => {
        console.log(`${index + 1}. ${backup}`);
      });
    }
    
  } catch (error: any) {
    console.error('❌ Failed to list backups:', error.message);
    process.exit(1);
  }
}

async function executeRollback(backupFile?: string): Promise<void> {
  console.log('🔄 EXECUTING ROLLBACK');
  console.log('=' .repeat(30));
  
  try {
    const rollbackManager = new RollbackManager();
    const result = await rollbackManager.executeRollback(backupFile);
    
    if (result.success) {
      console.log('✅ Rollback completed successfully');
      console.log(`📦 Backup used: ${result.backupUsed}`);
      console.log(`📊 Restored: ${result.restoredRecords} records across ${result.restoredTables.length} tables`);
    } else {
      console.log('❌ Rollback failed:');
      result.errors.forEach(error => console.log(`   • ${error}`));
      process.exit(1);
    }
    
  } catch (error: any) {
    console.error('❌ Rollback execution failed:', error.message);
    process.exit(1);
  }
}

// CLI interface
async function main(): Promise<void> {
  const command = process.argv[2];
  const options: MigrationOptions = {};
  
  // Parse command line options
  for (let i = 3; i < process.argv.length; i++) {
    const arg = process.argv[i];
    switch (arg) {
      case '--skip-backup':
        options.skipBackup = true;
        break;
      case '--skip-validation':
        options.skipValidation = true;
        break;
      case '--no-rollback':
        options.enableRollback = false;
        break;
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--batch-size':
        options.batchSize = parseInt(process.argv[++i]);
        break;
    }
  }
  
  switch (command) {
    case 'execute':
    case 'migrate':
      await executeMigration(options);
      break;
    case 'status':
    case 'check':
      await showMigrationStatus();
      break;
    case 'backups':
    case 'list':
      await listBackups();
      break;
    case 'rollback':
      const backupFile = process.argv[3];
      await executeRollback(backupFile);
      break;
    default:
      console.log('Usage: npm run migrate:execute [command] [options]');
      console.log('');
      console.log('Commands:');
      console.log('  execute, migrate    Execute data migration');
      console.log('  status, check       Check migration status');
      console.log('  backups, list       List available backups');
      console.log('  rollback [file]     Execute rollback');
      console.log('');
      console.log('Options:');
      console.log('  --skip-backup       Skip backup creation');
      console.log('  --skip-validation   Skip validation steps');
      console.log('  --no-rollback       Disable rollback capability');
      console.log('  --dry-run           Validate only, no changes');
      console.log('  --batch-size N      Set batch size for operations');
      process.exit(1);
  }
}

// Run the CLI
main().catch(console.error);
