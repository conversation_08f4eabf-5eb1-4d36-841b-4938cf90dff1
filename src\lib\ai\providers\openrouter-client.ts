import OpenAI from 'openai';
import { 
  AIResponse, 
  AIModelConfig, 
  OPENROUTER_CONFIG,
  MODEL_OUTPUT_LIMITS 
} from '../types';

export class OpenRouterClient {
  private client: OpenAI;
  private config: AIModelConfig;

  constructor(config?: Partial<AIModelConfig>) {
    this.config = { ...OPENROUTER_CONFIG, ...config };
    
    if (!process.env.OPENROUTER_API_KEY) {
      throw new Error('OPENROUTER_API_KEY environment variable is required');
    }

    // OpenRouter uses OpenAI-compatible API
    this.client = new OpenAI({
      apiKey: process.env.OPENROUTER_API_KEY,
      baseURL: 'https://openrouter.ai/api/v1',
      timeout: this.config.timeout,
    });
  }

  async generateContent(
    systemPrompt: string,
    userPrompt: string,
    options: {
      model?: string;
      temperature?: number;
      maxTokens?: number;
      responseFormat?: 'json_object' | 'json_schema';
      jsonSchema?: any;
      enableCaching?: boolean;
    } = {}
  ): Promise<AIResponse> {
    const model = options.model || this.config.model;
    const temperature = options.temperature ?? this.config.temperature;
    const maxTokens = options.maxTokens || this.getOptimalOutputTokens(model);

    try {
      const requestConfig: any = {
        model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature,
        max_tokens: maxTokens,
      };

      // Add OpenRouter-specific headers
      const extraHeaders: Record<string, string> = {};
      
      if (this.config.extraHeaders) {
        Object.assign(extraHeaders, this.config.extraHeaders);
      }

      // Configure response format
      if (options.responseFormat === 'json_object' || options.responseFormat === 'json_schema') {
        // OpenRouter supports JSON mode for most models
        requestConfig.response_format = { type: 'json_object' };
        
        if (options.jsonSchema) {
          // Include schema in the system prompt for models that don't support strict schema
          const schemaPrompt = `\n\nPlease respond with JSON that matches this schema:\n${JSON.stringify(options.jsonSchema, null, 2)}`;
          requestConfig.messages[0].content += schemaPrompt;
        }
      }

      // Implicit caching is enabled automatically for Gemini 2.5 Pro Preview
      // No manual cache configuration needed - OpenRouter handles this automatically

      const response = await this.client.chat.completions.create(requestConfig, {
        headers: extraHeaders
      });

      const choice = response.choices[0];
      if (!choice?.message?.content) {
        throw new Error('No content generated from OpenRouter');
      }

      return {
        content: choice.message.content,
        tokenUsage: response.usage ? {
          prompt_tokens: response.usage.prompt_tokens,
          completion_tokens: response.usage.completion_tokens,
          total_tokens: response.usage.total_tokens
        } : undefined,
        model: response.model,
        finishReason: choice.finish_reason || 'unknown'
      };

    } catch (error: any) {
      // Enhanced error handling for OpenRouter-specific errors
      if (error.status === 402) {
        throw new Error(`OpenRouter insufficient credits: ${error.message}`);
      } else if (error.status === 429) {
        throw new Error(`OpenRouter rate limit exceeded: ${error.message}`);
      } else if (error.status === 400) {
        throw new Error(`OpenRouter bad request: ${error.message}`);
      } else if (error.status === 401) {
        throw new Error(`OpenRouter authentication failed: ${error.message}`);
      } else if (error.status === 503) {
        throw new Error(`OpenRouter service unavailable: ${error.message}`);
      }

      throw new Error(`OpenRouter API error: ${error.message}`);
    }
  }

  private getOptimalOutputTokens(model: string): number {
    const limit = MODEL_OUTPUT_LIMITS[model] || MODEL_OUTPUT_LIMITS['google/gemini-2.5-pro-preview'];
    
    // For Gemini 2.5 Pro Preview, use the full 65K limit for maximum quality
    if (model.includes('gemini-2.5-pro-preview')) {
      return Math.floor(limit * 0.95); // Use 95% of the 65K limit
    }
    
    // For other models, use 80% of the limit
    return Math.floor(limit * 0.8);
  }

  async validateConnection(): Promise<boolean> {
    try {
      const response = await this.client.chat.completions.create({
        model: 'openai/gpt-4o-mini', // Use cheaper model for validation
        messages: [{ role: 'user', content: 'Test connection' }],
        max_tokens: 10,
        temperature: 0
      }, {
        headers: this.config.extraHeaders || {}
      });

      return response.choices.length > 0;
    } catch (error) {
      console.error('OpenRouter connection validation failed:', error);
      return false;
    }
  }

  isConfigured(): boolean {
    return !!process.env.OPENROUTER_API_KEY;
  }

  getConfig(): AIModelConfig {
    return { ...this.config };
  }

  updateConfig(newConfig: Partial<AIModelConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  calculateTokenCount(text: string): number {
    // Approximate token calculation (1 token ≈ 4 characters for English)
    return Math.ceil(text.length / 4);
  }

  canHandleContent(contentSize: number): boolean {
    return contentSize <= this.config.maxInputTokens;
  }

  splitContentForModel(content: string): string[] {
    const tokenCount = this.calculateTokenCount(content);
    const maxTokensPerChunk = Math.floor(this.config.maxInputTokens * 0.8); // Reserve 20% for response
    
    if (tokenCount <= maxTokensPerChunk) {
      return [content];
    }
    
    // Split content by logical sections
    const sections = content.split(/\n(?=#{1,3}\s)/); // Split on headers
    const chunks = [];
    let currentChunk = '';
    
    for (const section of sections) {
      const sectionTokens = this.calculateTokenCount(section);
      const currentTokens = this.calculateTokenCount(currentChunk);
      
      if (currentTokens + sectionTokens > maxTokensPerChunk && currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = section;
      } else {
        currentChunk += (currentChunk ? '\n' : '') + section;
      }
    }
    
    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }
    
    return chunks;
  }

  getProviderInfo(): { provider: string; model: string; capabilities: string[] } {
    return {
      provider: 'OpenRouter',
      model: this.config.model,
      capabilities: [
        'Large Context Window (1M+ tokens)',
        'Implicit Prompt Caching',
        'Cost Optimization',
        'Model Routing',
        'High Output Limits (65K tokens)',
        'Multi-Modal Support'
      ]
    };
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      // This would require a separate API call to OpenRouter's models endpoint
      // For now, return the commonly used models
      return [
        'google/gemini-2.5-pro-preview',
        'google/gemini-pro-1.5',
        'openai/gpt-4o',
        'openai/gpt-4o-2024-11-20',
        'openai/gpt-4o-mini',
        'anthropic/claude-3.5-sonnet'
      ];
    } catch (error) {
      console.error('Failed to fetch available models:', error);
      return [this.config.model];
    }
  }
}
