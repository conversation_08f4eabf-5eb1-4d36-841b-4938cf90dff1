'use client';

import { useState, useEffect } from 'react';
import { BulkProcessingDashboard } from '@/components/admin/bulk-processing/BulkProcessingDashboard';
import { BulkProcessingJob } from '@/lib/types';

/**
 * Bulk Processing Admin Page
 * 
 * Main interface for bulk processing operations including:
 * - File upload (text and JSON)
 * - Manual URL entry
 * - Batch configuration
 * - Progress tracking
 * - Results management
 */
export default function BulkProcessingPage() {
  const [jobs, setJobs] = useState<BulkProcessingJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadBulkJobs();
  }, []);

  const loadBulkJobs = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/bulk-processing', {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || '',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load bulk processing jobs');
      }

      const data = await response.json();
      if (data.success) {
        setJobs(data.jobs || []);
      } else {
        throw new Error(data.error || 'Unknown error');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load jobs');
      console.error('Error loading bulk jobs:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleJobUpdate = () => {
    // Reload jobs when a new job is created or updated
    loadBulkJobs();
  };

  return (
    <div className="min-h-screen bg-zinc-900 text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold font-roboto">Bulk Processing</h1>
            <p className="text-gray-400 mt-2">
              Process multiple AI tools efficiently with batch operations
            </p>
          </div>
          <div className="flex space-x-3">
            <a
              href="/admin"
              className="bg-zinc-700 hover:bg-zinc-600 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              ← Back to Admin
            </a>
            <a
              href="/admin/jobs"
              className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              📊 Job Monitor
            </a>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-900/20 border border-red-500/50 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2">
              <span className="text-red-400">⚠️</span>
              <span className="text-red-400">{error}</span>
              <button
                onClick={() => setError(null)}
                className="ml-auto text-red-400 hover:text-red-300"
              >
                ✕
              </button>
            </div>
          </div>
        )}

        {/* Main Dashboard */}
        <BulkProcessingDashboard
          jobs={jobs}
          loading={loading}
          onJobUpdate={handleJobUpdate}
          onError={setError}
        />
      </div>
    </div>
  );
}
