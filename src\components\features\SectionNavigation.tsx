'use client';

import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  DollarSign, 
  Calendar, 
  MessageSquare, 
  Scale, 
  HelpCircle, 
  Shuffle, 
  Eye 
} from 'lucide-react';

interface SectionNavigationProps {
  activeSection?: string;
  onSectionClick?: (sectionId: string) => void;
}

const sections = [
  { id: 'overview', label: 'Overview', icon: Eye },
  { id: 'pricing', label: 'Pricing', icon: DollarSign },
  { id: 'releases', label: 'Releases', icon: Calendar },
  { id: 'reviews', label: 'Reviews', icon: MessageSquare },
  { id: 'pros-cons', label: 'Pros & Cons', icon: Scale },
  { id: 'qa', label: 'Q&A', icon: HelpCircle },
  { id: 'alternatives', label: 'Alternatives', icon: Shuffle },
];

export function SectionNavigation({ activeSection = 'overview', onSectionClick }: SectionNavigationProps) {
  const [currentSection, setCurrentSection] = useState(activeSection);

  useEffect(() => {
    setCurrentSection(activeSection);
  }, [activeSection]);

  const handleSectionClick = (sectionId: string) => {
    setCurrentSection(sectionId);
    
    // Smooth scroll to section
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
    
    // Call parent callback if provided
    if (onSectionClick) {
      onSectionClick(sectionId);
    }
  };

  return (
    <nav className="bg-zinc-800 border border-black rounded-lg p-4 shadow-lg hover:shadow-xl transition-shadow duration-200">
      <div className="flex flex-wrap gap-2">
        {sections.map((section) => {
          const Icon = section.icon;
          const isActive = currentSection === section.id;
          
          return (
            <button
              key={section.id}
              onClick={() => handleSectionClick(section.id)}
              className={`
                flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200
                ${isActive
                  ? 'text-white border-b-2'
                  : 'text-gray-300 hover:text-white hover:bg-zinc-700'
                }
              `}
              style={isActive ? {
                backgroundColor: 'rgb(255, 150, 0)',
                borderBottomColor: 'rgb(255, 170, 30)'
              } : {}}
              onMouseEnter={isActive ? (e) => {
                e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
              } : undefined}
              onMouseLeave={isActive ? (e) => {
                e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
              } : undefined}
            >
              <Icon size={16} />
              <span>{section.label}</span>
            </button>
          );
        })}
      </div>
    </nav>
  );
}
