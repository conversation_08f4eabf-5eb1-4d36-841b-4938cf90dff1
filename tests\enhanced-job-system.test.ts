/**
 * Enhanced Job Processing System Integration Tests
 * 
 * Tests the complete enhanced job processing system including:
 * - Enhanced queue functionality
 * - Progress tracking
 * - WebSocket integration
 * - Job management
 * - Database persistence
 */

// Jest globals are available in the test environment
declare const describe: any;
declare const test: any;
declare const expect: any;
declare const beforeEach: any;
declare const afterEach: any;
declare const jest: any;
import { 
  getEnhancedJobQueue,
  getJobManager,
  getProgressTracker,
  getWebSocketManager,
  JobType,
  JobStatus,
  JobPriority
} from '../src/lib/jobs';

describe('Enhanced Job Processing System', () => {
  let jobManager: any;
  let enhancedQueue: any;
  let progressTracker: any;
  let webSocketManager: any;

  beforeEach(() => {
    // Initialize components
    jobManager = getJobManager();
    enhancedQueue = getEnhancedJobQueue();
    progressTracker = getProgressTracker();
    webSocketManager = getWebSocketManager();
  });

  afterEach(() => {
    // Cleanup
    progressTracker.cleanup();
    webSocketManager.cleanup();
  });

  describe('Enhanced Job Queue', () => {
    test('should create and store job in database', async () => {
      const jobData = {
        url: 'https://example.com',
        name: 'Test Tool',
        description: 'Test description',
      };

      const job = await enhancedQueue.add(JobType.TOOL_SUBMISSION, jobData);

      expect(job).toBeDefined();
      expect(job.id).toBeDefined();
      expect(job.type).toBe(JobType.TOOL_SUBMISSION);
      expect(job.status).toBe(JobStatus.PENDING);
      expect(job.progress).toBe(0);
      expect(job.canPause).toBe(true);
      expect(job.canStop).toBe(true);
    });

    test('should retrieve job by ID', async () => {
      const jobData = { url: 'https://example.com' };
      const createdJob = await enhancedQueue.add(JobType.WEB_SCRAPING, jobData);

      const retrievedJob = await enhancedQueue.getJob(createdJob.id);

      expect(retrievedJob).toBeDefined();
      expect(retrievedJob?.id).toBe(createdJob.id);
      expect(retrievedJob?.type).toBe(JobType.WEB_SCRAPING);
    });

    test('should get jobs by status', async () => {
      await enhancedQueue.add(JobType.TOOL_SUBMISSION, { url: 'https://example1.com' });
      await enhancedQueue.add(JobType.TOOL_SUBMISSION, { url: 'https://example2.com' });

      const pendingJobs = await enhancedQueue.getJobs(JobStatus.PENDING);

      expect(pendingJobs.length).toBeGreaterThanOrEqual(2);
      expect(pendingJobs.every((job: any) => job.status === JobStatus.PENDING)).toBe(true);
    });

    test('should get queue statistics', async () => {
      const stats = await enhancedQueue.getQueueStats();

      expect(stats).toBeDefined();
      expect(typeof stats.totalJobs).toBe('number');
      expect(typeof stats.pendingJobs).toBe('number');
      expect(typeof stats.processingJobs).toBe('number');
      expect(typeof stats.completedJobs).toBe('number');
      expect(typeof stats.failedJobs).toBe('number');
      expect(typeof stats.successRate).toBe('number');
      expect(stats.lastUpdated).toBeInstanceOf(Date);
    });
  });

  describe('Progress Tracker', () => {
    test('should update and track job progress', async () => {
      const jobData = { url: 'https://example.com' };
      const job = await enhancedQueue.add(JobType.WEB_SCRAPING, jobData);

      await progressTracker.updateProgress(job.id, 50, {
        phase: 'scraping',
        currentStep: 'extracting content',
        totalSteps: 4,
        completedSteps: 2,
        message: 'Extracting page content...',
      });

      const progress = progressTracker.getProgress(job.id);

      expect(progress).toBeDefined();
      expect(progress?.progress).toBe(50);
      expect(progress?.details?.phase).toBe('scraping');
      expect(progress?.details?.message).toBe('Extracting page content...');
    });

    test('should provide progress statistics', () => {
      const stats = progressTracker.getProgressStats();

      expect(stats).toBeDefined();
      expect(typeof stats.totalJobs).toBe('number');
      expect(typeof stats.activeJobs).toBe('number');
      expect(typeof stats.averageProgress).toBe('number');
    });

    test('should subscribe to progress updates', (done: any) => {
      const jobData = { url: 'https://example.com' };
      
      enhancedQueue.add(JobType.WEB_SCRAPING, jobData).then((job: any) => {
        const unsubscribe = progressTracker.subscribeToJob(job.id, (data: any) => {
          expect(data.progress).toBe(75);
          expect(data.details?.message).toBe('Almost done...');
          unsubscribe();
          done();
        });

        // Trigger progress update
        progressTracker.updateProgress(job.id, 75, {
          phase: 'finalizing',
          currentStep: 'cleanup',
          totalSteps: 4,
          completedSteps: 3,
          message: 'Almost done...',
        });
      });
    });
  });

  describe('WebSocket Manager', () => {
    test('should register and manage connections', () => {
      const connectionId = 'test-connection-1';
      const mockConnection = {
        send: jest.fn(),
        close: jest.fn(),
        readyState: 1, // WebSocket.OPEN
      };

      webSocketManager.registerConnection(connectionId, mockConnection);

      const stats = webSocketManager.getStats();
      expect(stats.totalConnections).toBeGreaterThanOrEqual(1);
      expect(stats.activeConnections).toBeGreaterThanOrEqual(1);

      webSocketManager.unregisterConnection(connectionId);
    });

    test('should broadcast updates to all connections', () => {
      const connectionId1 = 'test-connection-1';
      const connectionId2 = 'test-connection-2';
      const mockConnection1 = {
        send: jest.fn(),
        close: jest.fn(),
        readyState: 1,
      };
      const mockConnection2 = {
        send: jest.fn(),
        close: jest.fn(),
        readyState: 1,
      };

      webSocketManager.registerConnection(connectionId1, mockConnection1);
      webSocketManager.registerConnection(connectionId2, mockConnection2);

      const update = {
        jobId: 'test-job',
        type: 'progress_update' as const,
        data: {
          progress: 50,
          timestamp: new Date(),
        },
      };

      webSocketManager.broadcast(update);

      expect(mockConnection1.send).toHaveBeenCalled();
      expect(mockConnection2.send).toHaveBeenCalled();

      webSocketManager.unregisterConnection(connectionId1);
      webSocketManager.unregisterConnection(connectionId2);
    });
  });

  describe('Job Manager', () => {
    test('should create job with enhanced features', async () => {
      const jobData = {
        url: 'https://example.com',
        name: 'Test Tool',
      };

      const job = await jobManager.createJob(JobType.TOOL_SUBMISSION, jobData, {
        priority: JobPriority.HIGH,
      });

      expect(job).toBeDefined();
      expect(job.type).toBe(JobType.TOOL_SUBMISSION);
      expect(job.priority).toBe(JobPriority.HIGH);
      expect(job.canPause).toBe(true);
      expect(job.canStop).toBe(true);
    });

    test('should get job with real-time progress', async () => {
      const jobData = { url: 'https://example.com' };
      const createdJob = await jobManager.createJob(JobType.WEB_SCRAPING, jobData);

      // Update progress
      await progressTracker.updateProgress(createdJob.id, 30);

      const job = await jobManager.getJob(createdJob.id);

      expect(job).toBeDefined();
      expect(job?.progress).toBe(30);
    });

    test('should get comprehensive queue statistics', async () => {
      const stats = await jobManager.getQueueStats();

      expect(stats).toBeDefined();
      expect(typeof stats.totalJobs).toBe('number');
      expect(typeof stats.successRate).toBe('number');
      expect(stats.lastUpdated).toBeInstanceOf(Date);
    });

    test('should subscribe to job events', (done: any) => {
      const jobData = { url: 'https://example.com' };
      
      jobManager.createJob(JobType.WEB_SCRAPING, jobData).then((job: any) => {
        const unsubscribe = jobManager.subscribeToJobEvents(job.id, {
          onProgressUpdate: (updatedJob: any, progress: number) => {
            expect(progress).toBe(60);
            expect(updatedJob.id).toBe(job.id);
            unsubscribe();
            done();
          },
        });

        // Trigger progress update
        progressTracker.updateProgress(job.id, 60);
      });
    });
  });

  describe('Integration Tests', () => {
    test('should handle complete job lifecycle', async () => {
      const jobData = {
        url: 'https://example.com',
        name: 'Integration Test Tool',
      };

      // Create job
      const job = await jobManager.createJob(JobType.TOOL_SUBMISSION, jobData);
      expect(job.status).toBe(JobStatus.PENDING);

      // Update progress
      await progressTracker.updateProgress(job.id, 25, {
        phase: 'scraping',
        currentStep: 'fetching page',
        totalSteps: 4,
        completedSteps: 1,
      });

      // Check progress
      const progress = progressTracker.getProgress(job.id);
      expect(progress?.progress).toBe(25);

      // Get updated job
      const updatedJob = await jobManager.getJob(job.id);
      expect(updatedJob?.progress).toBe(25);

      // Complete job
      await progressTracker.updateProgress(job.id, 100, {
        phase: 'completed',
        currentStep: 'finished',
        totalSteps: 4,
        completedSteps: 4,
      });

      const finalProgress = progressTracker.getProgress(job.id);
      expect(finalProgress?.progress).toBe(100);
    });

    test('should maintain backward compatibility with legacy queue', async () => {
      // This test ensures the legacy queue still works
      const { getJobQueue } = require('../src/lib/jobs');
      const legacyQueue = getJobQueue();

      const jobData = { url: 'https://example.com' };
      const job = await legacyQueue.add(JobType.WEB_SCRAPING, jobData);

      expect(job).toBeDefined();
      expect(job.type).toBe(JobType.WEB_SCRAPING);

      const retrievedJob = await legacyQueue.getJob(job.id);
      expect(retrievedJob?.id).toBe(job.id);
    });
  });
});
