import { JobType, <PERSON><PERSON><PERSON><PERSON> } from '../types';
import { ToolSubmissionHandler } from './tool-submission';
import { ContentGenerationHandler } from './content-generation';
import { WebScrapingHandler } from './web-scraping';
import { EmailNotificationHandler } from './email-notification';
import { BulkProcessingHandler } from './bulk-processing';

const handlers: Record<JobType, JobHandler> = {
  [JobType.TOOL_SUBMISSION]: new ToolSubmissionHandler(),
  [JobType.CONTENT_GENERATION]: new ContentGenerationHandler(),
  [JobType.WEB_SCRAPING]: new WebScrapingHandler(),
  [JobType.EMAIL_NOTIFICATION]: new EmailNotificationHandler(),
  [JobType.TOOL_PROCESSING]: new ToolSubmissionHandler(), // Alias for tool submission
  [JobType.SCREENSHOT_CAPTURE]: new WebScrapingHandler(), // Part of web scraping
  [JobType.FAVICON_EXTRACTION]: new WebScrapingHandler(), // Part of web scraping
  [JobType.BULK_PROCESSING]: new BulkProcessingHandler(),
};

export function getJobHandler(type: JobType): JobHandler {
  const handler = handlers[type];
  if (!handler) {
    throw new Error(`No handler found for job type: ${type}`);
  }
  return handler;
}

export * from './tool-submission';
export * from './content-generation';
export * from './web-scraping';
export * from './email-notification';
