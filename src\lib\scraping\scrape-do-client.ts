/**
 * Scrape.do API Client
 * Provides cost-optimized web scraping with intelligent content analysis
 */

import {
  ScrapeDoConfig,
  ScrapeOptions,
  ScrapeResult,
  ScrapeError,
  RetryConfig,
  UsageStats,
  ScrapeDoJSONResponse
} from './types';

export class ScrapeDoClient {
  private config: ScrapeDoConfig;
  private retryConfig: RetryConfig;

  constructor() {
    this.config = {
      apiKey: process.env.SCRAPE_DO_API_KEY || '8e7e405ff81145c4afe447610ddb9a7f785f494dddc',
      baseUrl: 'https://api.scrape.do',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 2000
    };

    this.retryConfig = {
      maxAttempts: 3,
      baseDelay: 2000,
      maxDelay: 10000,
      backoffMultiplier: 2
    };
  }

  /**
   * Main scraping method with cost optimization
   */
  async scrapePage(url: string, options: ScrapeOptions = {}): Promise<ScrapeResult> {
    const params = this.buildRequestParams(url, options);
    const requestUrl = `${this.config.baseUrl}/?${params.toString()}`;

    console.log(`🔗 Scraping URL: ${url}`);
    console.log(`📡 Request URL: ${requestUrl}`);

    try {
      const response = await this.makeRequest(requestUrl);

      if (!response.ok) {
        // Get response body for better error details
        const errorBody = await response.text().catch(() => 'Unable to read error response');
        console.error(`❌ HTTP ${response.status}: ${response.statusText}`);
        console.error(`📄 Error response body:`, errorBody);
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorBody}`);
      }

      // Check if we're expecting JSON response (for screenshots or network requests)
      const expectingJSON = options.captureScreenshot || options.fullPageScreenshot ||
                           options.includeNetworkRequests || options.returnJSON;

      let content: string;
      let jsonResponse: ScrapeDoJSONResponse | undefined;

      if (expectingJSON) {
        try {
          jsonResponse = await response.json() as ScrapeDoJSONResponse;
          content = jsonResponse.content || '';
          console.log(`✅ JSON scraping successful - ${content.length} characters received`);

          // Debug: Log the JSON response structure
          console.log(`🔍 JSON Response keys: ${Object.keys(jsonResponse).join(', ')}`);

          // Log additional JSON response info
          if (jsonResponse.screenshot) {
            console.log(`📸 Screenshot captured (${jsonResponse.screenshot.length} chars)`);
          }
          if (jsonResponse.fullScreenshot) {
            console.log(`📸 Full screenshot captured (${jsonResponse.fullScreenshot.length} chars)`);
          }
          if (jsonResponse.screenShots && jsonResponse.screenShots.length > 0) {
            console.log(`📸 Screenshots captured: ${jsonResponse.screenShots.length} images`);
            jsonResponse.screenShots.forEach((shot, index) => {
              if (shot.image) {
                console.log(`   - Screenshot ${index + 1}: ${shot.type} (${shot.image.length} chars)`);
              } else if (shot.error) {
                console.log(`   - Screenshot ${index + 1}: ${shot.type} - ERROR: ${shot.error}`);
              }
            });
          }
        } catch (jsonError) {
          // Fallback to text if JSON parsing fails
          content = await response.text();
          console.log(`⚠️ JSON parsing failed, using text response - ${content.length} characters received`);
        }
      } else {
        content = await response.text();
        console.log(`✅ Scraping successful - ${content.length} characters received`);
      }

      const metadata = this.extractMetadata(response.headers, options, jsonResponse);

      return {
        success: true,
        content,
        metadata,
        timestamp: new Date().toISOString(),
        url,
        // Include JSON response data if available
        ...(jsonResponse && {
          screenshot: jsonResponse.screenshot || (jsonResponse.screenShots && jsonResponse.screenShots[0]?.image),
          fullScreenshot: jsonResponse.fullScreenshot,
          screenShots: jsonResponse.screenShots,
          networkRequests: jsonResponse.networkRequests,
          frames: jsonResponse.frames,
          websockets: jsonResponse.websockets || jsonResponse.websocketRequests
        })
      };
    } catch (error) {
      return this.handleScrapeError(error as Error, url, options);
    }
  }

  /**
   * Build URL parameters for scrape.do API
   */
  private buildRequestParams(url: string, options: ScrapeOptions): URLSearchParams {
    const params = new URLSearchParams({
      token: this.config.apiKey,
      url: url // URL will be encoded by URLSearchParams
    });

    // Add optional parameters based on options
    if (options.useResidentialProxy) params.set('super', 'true');
    if (options.geoTargeting) params.set('geoCode', options.geoTargeting);
    if (options.stickySession) params.set('sessionId', options.stickySession.toString());
    if (options.enableJSRendering) params.set('render', 'true');
    if (options.deviceType) params.set('device', options.deviceType);
    if (options.waitCondition) params.set('waitUntil', options.waitCondition);
    if (options.customWaitTime) params.set('customWait', options.customWaitTime.toString());
    if (options.waitForSelector) params.set('waitSelector', options.waitForSelector);
    if (options.outputFormat) params.set('output', options.outputFormat);
    if (options.captureScreenshot) params.set('screenShot', 'true');
    if (options.fullPageScreenshot) params.set('fullScreenShot', 'true');
    if (options.includeNetworkRequests) params.set('returnJSON', 'true');
    if (options.returnJSON) params.set('returnJSON', 'true');
    if (options.blockResources !== undefined) params.set('blockResources', options.blockResources.toString());

    // Auto-enable returnJSON for screenshot features (required by API)
    if (options.captureScreenshot || options.fullPageScreenshot) {
      params.set('returnJSON', 'true');
    }
    if (options.timeout) params.set('timeout', options.timeout.toString());
    if (options.retryTimeout) params.set('retryTimeout', options.retryTimeout.toString());

    // Debug logging
    console.log('🔧 Request parameters:');
    for (const [key, value] of params.entries()) {
      if (key === 'token') {
        console.log(`   ${key}: ${value.substring(0, 8)}...`); // Hide full token
      } else {
        console.log(`   ${key}: ${value}`);
      }
    }

    return params;
  }

  /**
   * Make HTTP request with retry logic
   */
  private async makeRequest(url: string): Promise<Response> {
    let lastError: Error;

    for (let attempt = 1; attempt <= this.retryConfig.maxAttempts; attempt++) {
      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br'
          },
          signal: AbortSignal.timeout(this.config.timeout)
        });

        return response;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.retryConfig.maxAttempts) {
          const delay = Math.min(
            this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt - 1),
            this.retryConfig.maxDelay
          );
          
          console.log(`Scrape attempt ${attempt} failed, retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }

  /**
   * Extract metadata from response headers and JSON response
   */
  private extractMetadata(headers: Headers, options: ScrapeOptions, jsonResponse?: ScrapeDoJSONResponse) {
    // Extract actual credits used from scrape.do response headers
    const actualCreditsUsed = parseInt(headers.get('Scrape.do-Request-Cost') || '0') || this.calculateCreditsUsed(options);
    const remainingCredits = parseInt(headers.get('Scrape.do-Remaining-Credits') || '0');
    const resolvedUrl = headers.get('Scrape.do-Resolved-Url') || jsonResponse?.resolvedUrl || undefined;
    const requestType = this.getRequestType(options);

    return {
      creditsUsed: actualCreditsUsed,
      requestType,
      proxyType: options.useResidentialProxy ? 'residential' : 'datacenter',
      browserEnabled: options.enableJSRendering || false,
      remainingCredits: remainingCredits || undefined,
      resolvedUrl,
      // Include JSON response metadata if available
      ...(jsonResponse && {
        statusCode: jsonResponse.statusCode,
        hasScreenshot: !!(jsonResponse.screenshot || jsonResponse.fullScreenshot || (jsonResponse.screenShots && jsonResponse.screenShots.some(s => s.image))),
        networkRequestCount: jsonResponse.networkRequests?.length || 0,
        frameCount: jsonResponse.frames?.length || 0,
        websocketCount: (jsonResponse.websockets?.length || 0) + (jsonResponse.websocketRequests?.length || 0)
      })
    };
  }

  /**
   * Calculate credits used based on options
   */
  private calculateCreditsUsed(options: ScrapeOptions): number {
    let credits = 1; // Base cost for datacenter proxy

    if (options.useResidentialProxy) {
      credits = 10; // Residential/Mobile proxy base cost
    }

    if (options.enableJSRendering) {
      credits *= 5; // 5x multiplier for headless browser
    }

    return credits;
  }

  /**
   * Determine request type for logging
   */
  private getRequestType(options: ScrapeOptions): string {
    if (options.useResidentialProxy && options.enableJSRendering) {
      return 'Residential + Browser';
    } else if (options.useResidentialProxy) {
      return 'Residential Proxy';
    } else if (options.enableJSRendering) {
      return 'Datacenter + Browser';
    } else {
      return 'Datacenter Proxy';
    }
  }

  /**
   * Handle scraping errors with proper error classification
   */
  private handleScrapeError(error: Error, url: string, options: ScrapeOptions): ScrapeResult {
    const scrapeError: ScrapeError = {
      code: this.classifyError(error),
      message: error.message,
      url,
      timestamp: new Date().toISOString(),
      retryable: this.isRetryableError(error),
      context: { options }
    };

    console.error('Scraping error:', scrapeError);

    // Provide helpful suggestions for common errors
    if (scrapeError.code === 'SERVER_ERROR' && error.message.includes('502')) {
      console.log('💡 Suggestion: Try using enhanced scraping with render=true for this URL');
    }

    return {
      success: false,
      content: '',
      error: error.message,
      timestamp: new Date().toISOString(),
      url,
      metadata: {
        creditsUsed: 0,
        requestType: 'failed',
        proxyType: options.useResidentialProxy ? 'residential' : 'datacenter',
        browserEnabled: options.enableJSRendering || false
      }
    };
  }

  /**
   * Classify error types for better handling
   */
  private classifyError(error: Error): string {
    if (error.name === 'AbortError') return 'TIMEOUT';
    if (error.message.includes('fetch')) return 'NETWORK_ERROR';
    if (error.message.includes('HTTP 4')) return 'CLIENT_ERROR';
    if (error.message.includes('HTTP 5')) return 'SERVER_ERROR';
    return 'UNKNOWN_ERROR';
  }

  /**
   * Determine if error is retryable
   */
  private isRetryableError(error: Error): boolean {
    const retryableCodes = ['TIMEOUT', 'NETWORK_ERROR', 'SERVER_ERROR'];
    const errorCode = this.classifyError(error);

    // Special handling for specific server errors
    if (errorCode === 'SERVER_ERROR') {
      // Don't retry 502 errors from scrape.do - they indicate the target site is unreachable
      if (error.message.includes('502') && error.message.includes('cannot connect target url')) {
        return false;
      }
    }

    return retryableCodes.includes(errorCode);
  }

  /**
   * Get current usage statistics
   */
  async getUsageStatistics(): Promise<UsageStats> {
    try {
      const response = await fetch(`${this.config.baseUrl}/info/?token=${this.config.apiKey}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch usage stats: ${response.status}`);
      }

      const stats = await response.json();

      return {
        isActive: stats.IsActive,
        concurrentRequests: stats.ConcurrentRequest,
        maxMonthlyRequests: stats.MaxMonthlyRequest,
        remainingConcurrentRequests: stats.RemainingConcurrentRequest,
        remainingMonthlyRequests: stats.RemainingMonthlyRequest,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to get usage statistics:', error);
      throw error;
    }
  }

  /**
   * Monitor usage and provide warnings
   */
  async monitorUsage(): Promise<void> {
    try {
      const stats = await this.getUsageStatistics();

      const monthlyUsagePercent = ((stats.maxMonthlyRequests - stats.remainingMonthlyRequests) / stats.maxMonthlyRequests) * 100;
      const concurrentUsagePercent = ((stats.concurrentRequests - stats.remainingConcurrentRequests) / stats.concurrentRequests) * 100;

      if (monthlyUsagePercent > 80) {
        console.warn(`Monthly usage at ${monthlyUsagePercent.toFixed(1)}% - consider upgrading plan`);
      }

      if (concurrentUsagePercent > 90) {
        console.warn(`Concurrent requests at ${concurrentUsagePercent.toFixed(1)}% - reduce parallel processing`);
      }
    } catch (error) {
      console.error('Usage monitoring failed:', error);
    }
  }

  /**
   * Resolve relative URLs to absolute URLs
   */
  resolveUrl(relativeUrl: string, baseUrl: string): string {
    try {
      return new URL(relativeUrl, baseUrl).href;
    } catch {
      return relativeUrl;
    }
  }
}

// Export singleton instance
export const scrapeDoClient = new ScrapeDoClient();
