/**
 * Test Environment Configuration Utility
 * 
 * Handles environment variable loading and configuration for test scripts
 * Provides fallback mechanisms and proper error handling for test scenarios
 */

import { config } from 'dotenv';
import { join } from 'path';
import { existsSync } from 'fs';

interface TestEnvironmentConfig {
  supabaseUrl?: string;
  supabaseServiceKey?: string;
  supabaseAnonKey?: string;
  adminApiKey?: string;
  baseUrl?: string;
  openaiApiKey?: string;
  openrouterApiKey?: string;
  scrapeDoApiKey?: string;
  jwtSecret?: string;
  encryptionKey?: string;
}

interface EnvironmentValidationResult {
  isValid: boolean;
  missingRequired: string[];
  missingOptional: string[];
  availableServices: string[];
  recommendations: string[];
}

class TestEnvironmentLoader {
  private config: TestEnvironmentConfig = {};
  private loaded = false;

  constructor() {
    this.loadEnvironmentVariables();
  }

  private loadEnvironmentVariables(): void {
    if (this.loaded) return;

    // Try to load from various .env files in order of preference
    const envFiles = [
      '.env.local',
      '.env.test',
      '.env.development',
      '.env'
    ];

    const projectRoot = this.findProjectRoot();
    
    for (const envFile of envFiles) {
      const envPath = join(projectRoot, envFile);
      if (existsSync(envPath)) {
        console.log(`🔧 Loading environment variables from ${envFile}`);
        config({ path: envPath });
        break;
      }
    }

    // Load configuration from process.env
    this.config = {
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
      supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
      supabaseAnonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      adminApiKey: process.env.ADMIN_API_KEY,
      baseUrl: process.env.NEXT_PUBLIC_BASE_URL || process.env.SITE_URL || 'http://localhost:3000',
      openaiApiKey: process.env.OPENAI_API_KEY,
      openrouterApiKey: process.env.OPENROUTER_API_KEY,
      scrapeDoApiKey: process.env.SCRAPE_DO_API_KEY,
      jwtSecret: process.env.JWT_SECRET,
      encryptionKey: process.env.ENCRYPTION_KEY
    };

    this.loaded = true;
  }

  private findProjectRoot(): string {
    let currentDir = process.cwd();
    
    // Look for package.json to identify project root
    while (currentDir !== '/') {
      if (existsSync(join(currentDir, 'package.json'))) {
        return currentDir;
      }
      currentDir = join(currentDir, '..');
    }
    
    return process.cwd(); // Fallback to current directory
  }

  public getConfig(): TestEnvironmentConfig {
    return { ...this.config };
  }

  public validateEnvironment(): EnvironmentValidationResult {
    const result: EnvironmentValidationResult = {
      isValid: false,
      missingRequired: [],
      missingOptional: [],
      availableServices: [],
      recommendations: []
    };

    // Required for basic database operations
    const requiredVars = [
      { key: 'supabaseUrl', name: 'NEXT_PUBLIC_SUPABASE_URL' },
      { key: 'supabaseServiceKey', name: 'SUPABASE_SERVICE_ROLE_KEY' }
    ];

    // Optional but recommended for full testing
    const optionalVars = [
      { key: 'adminApiKey', name: 'ADMIN_API_KEY' },
      { key: 'openaiApiKey', name: 'OPENAI_API_KEY' },
      { key: 'openrouterApiKey', name: 'OPENROUTER_API_KEY' },
      { key: 'scrapeDoApiKey', name: 'SCRAPE_DO_API_KEY' },
      { key: 'jwtSecret', name: 'JWT_SECRET' },
      { key: 'encryptionKey', name: 'ENCRYPTION_KEY' }
    ];

    // Check required variables
    for (const variable of requiredVars) {
      const value = this.config[variable.key as keyof TestEnvironmentConfig];
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        result.missingRequired.push(variable.name);
      }
    }

    // Check optional variables
    for (const variable of optionalVars) {
      const value = this.config[variable.key as keyof TestEnvironmentConfig];
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        result.missingOptional.push(variable.name);
      }
    }

    // Determine available services
    if (this.config.supabaseUrl && this.config.supabaseServiceKey) {
      result.availableServices.push('Database (Supabase)');
    }
    if (this.config.openaiApiKey) {
      result.availableServices.push('OpenAI API');
    }
    if (this.config.openrouterApiKey) {
      result.availableServices.push('OpenRouter API');
    }
    if (this.config.scrapeDoApiKey) {
      result.availableServices.push('Scrape.do API');
    }
    if (this.config.adminApiKey) {
      result.availableServices.push('Admin API');
    }

    // Generate recommendations
    if (result.missingRequired.length === 0) {
      result.isValid = true;
      if (result.missingOptional.length > 0) {
        result.recommendations.push('Configure optional environment variables for full testing capabilities');
      }
    } else {
      result.recommendations.push('Configure required environment variables for basic testing');
    }

    if (!this.config.openaiApiKey && !this.config.openrouterApiKey) {
      result.recommendations.push('Configure at least one AI provider (OpenAI or OpenRouter) for AI testing');
    }

    if (!this.config.adminApiKey) {
      result.recommendations.push('Configure ADMIN_API_KEY for admin panel testing');
    }

    return result;
  }

  public createTestSupabaseClient() {
    if (!this.config.supabaseUrl || !this.config.supabaseServiceKey) {
      throw new Error('Supabase configuration not available for testing');
    }

    // Dynamic import to avoid issues if @supabase/supabase-js is not available
    return import('@supabase/supabase-js').then(({ createClient }) => {
      return createClient(this.config.supabaseUrl!, this.config.supabaseServiceKey!);
    });
  }

  public getTestHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (this.config.adminApiKey) {
      headers['x-api-key'] = this.config.adminApiKey;
    }

    return headers;
  }

  public isServiceAvailable(service: 'database' | 'openai' | 'openrouter' | 'scrape-do' | 'admin'): boolean {
    switch (service) {
      case 'database':
        return !!(this.config.supabaseUrl && this.config.supabaseServiceKey);
      case 'openai':
        return !!this.config.openaiApiKey;
      case 'openrouter':
        return !!this.config.openrouterApiKey;
      case 'scrape-do':
        return !!this.config.scrapeDoApiKey;
      case 'admin':
        return !!this.config.adminApiKey;
      default:
        return false;
    }
  }

  public printEnvironmentStatus(): void {
    const validation = this.validateEnvironment();
    
    console.log('🔧 TEST ENVIRONMENT STATUS');
    console.log('==========================');
    console.log(`Overall Status: ${validation.isValid ? '✅ Valid' : '❌ Invalid'}`);
    
    if (validation.availableServices.length > 0) {
      console.log(`\n✅ Available Services (${validation.availableServices.length}):`);
      validation.availableServices.forEach(service => {
        console.log(`   • ${service}`);
      });
    }

    if (validation.missingRequired.length > 0) {
      console.log(`\n❌ Missing Required Variables (${validation.missingRequired.length}):`);
      validation.missingRequired.forEach(variable => {
        console.log(`   • ${variable}`);
      });
    }

    if (validation.missingOptional.length > 0) {
      console.log(`\n⚠️ Missing Optional Variables (${validation.missingOptional.length}):`);
      validation.missingOptional.forEach(variable => {
        console.log(`   • ${variable}`);
      });
    }

    if (validation.recommendations.length > 0) {
      console.log(`\n💡 Recommendations:`);
      validation.recommendations.forEach(rec => {
        console.log(`   • ${rec}`);
      });
    }

    console.log('==========================\n');
  }

  public generateTestEnvFile(): string {
    const envContent = `# Test Environment Configuration
# Copy this to .env.local and fill in your actual values

# Required for basic testing
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Admin API
ADMIN_API_KEY=aidude_admin_2024_secure_key_xyz789

# AI Providers (at least one recommended)
OPENAI_API_KEY=sk-your_openai_key_here
OPENROUTER_API_KEY=sk-or-your_openrouter_key_here

# Scraping Service
SCRAPE_DO_API_KEY=your_scrape_do_key_here

# Security
JWT_SECRET=your_jwt_secret_at_least_32_characters_long
ENCRYPTION_KEY=your_encryption_key_64_characters_long

# Optional
SITE_URL=http://localhost:3000
NEXT_PUBLIC_BASE_URL=http://localhost:3000
`;

    return envContent;
  }
}

// Singleton instance
let testEnvironmentLoader: TestEnvironmentLoader | null = null;

export function getTestEnvironment(): TestEnvironmentLoader {
  if (!testEnvironmentLoader) {
    testEnvironmentLoader = new TestEnvironmentLoader();
  }
  return testEnvironmentLoader;
}

// Convenience functions
export function loadTestEnvironment(): TestEnvironmentConfig {
  return getTestEnvironment().getConfig();
}

export function validateTestEnvironment(): EnvironmentValidationResult {
  return getTestEnvironment().validateEnvironment();
}

export function isTestServiceAvailable(service: 'database' | 'openai' | 'openrouter' | 'scrape-do' | 'admin'): boolean {
  return getTestEnvironment().isServiceAvailable(service);
}

export function printTestEnvironmentStatus(): void {
  return getTestEnvironment().printEnvironmentStatus();
}

export function createTestSupabaseClient() {
  return getTestEnvironment().createTestSupabaseClient();
}

export function getTestHeaders(): Record<string, string> {
  return getTestEnvironment().getTestHeaders();
}

// Export types
export type { TestEnvironmentConfig, EnvironmentValidationResult };
export { TestEnvironmentLoader };
