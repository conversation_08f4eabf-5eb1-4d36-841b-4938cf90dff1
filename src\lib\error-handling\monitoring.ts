import {
  SystemError,
  Error<PERSON>ontext,
  ErrorMetrics,
  <PERSON>ert<PERSON>hr<PERSON>old,
  <PERSON>ert,
  <PERSON>rror<PERSON>eport,
  TimeRange,
  <PERSON>rror<PERSON>ever<PERSON>,
  ErrorCategory,
  ImpactAssessment
} from './types';

/**
 * Error Monitoring System
 * Tracks error metrics, generates alerts, and provides reporting
 */
export class ErrorMonitoringSystem {
  private errorHistory: SystemError[] = [];
  private errorMetrics = new Map<string, ErrorMetrics>();
  private alerts: Alert[] = [];
  private alertThresholds = new Map<string, AlertThreshold>();

  constructor() {
    this.initializeAlertThresholds();
    this.startPeriodicCleanup();
  }

  /**
   * Track an error occurrence
   */
  async trackError(error: SystemError, context: ErrorContext): Promise<void> {
    // Add to error history
    this.errorHistory.push(error);

    // Update metrics
    this.updateErrorMetrics(error);

    // Check for alert conditions
    await this.checkAlertConditions(error);

    // Log error for debugging
    this.logError(error, context);

    // Cleanup old data periodically
    this.cleanupOldData();
  }

  /**
   * Update error metrics for tracking trends
   */
  private updateErrorMetrics(error: SystemError): void {
    const key = `${error.category}_${error.type}`;
    const metrics = this.errorMetrics.get(key) || {
      count: 0,
      lastOccurrence: null,
      frequency: 0,
      trend: 'stable',
      consecutiveFailures: 0,
      recoverySuccessRate: 0,
      averageRecoveryTime: 0,
      impactScore: 0
    };

    metrics.count++;
    metrics.lastOccurrence = new Date(error.timestamp);
    metrics.frequency = this.calculateFrequency(key);
    metrics.trend = this.calculateTrend(key);
    metrics.impactScore = this.calculateImpactScore(error);

    // Update consecutive failures
    if (error.severity === ErrorSeverity.CRITICAL || error.severity === ErrorSeverity.HIGH) {
      metrics.consecutiveFailures++;
    } else {
      metrics.consecutiveFailures = 0;
    }

    this.errorMetrics.set(key, metrics);
  }

  /**
   * Check if error conditions warrant alerts
   */
  private async checkAlertConditions(error: SystemError): Promise<void> {
    const thresholds = this.alertThresholds.get(error.type);
    if (!thresholds) return;

    const metrics = this.errorMetrics.get(`${error.category}_${error.type}`);
    if (!metrics) return;

    // Check frequency threshold
    if (metrics.frequency > thresholds.maxFrequency) {
      await this.createAlert({
        type: 'frequency_exceeded',
        errorType: error.type,
        severity: error.severity,
        message: `Error frequency exceeded threshold: ${metrics.frequency}/hour > ${thresholds.maxFrequency}/hour`,
        currentFrequency: metrics.frequency,
        threshold: thresholds.maxFrequency
      });
    }

    // Check consecutive failures
    if (metrics.consecutiveFailures > thresholds.maxConsecutiveFailures) {
      await this.createAlert({
        type: 'consecutive_failures',
        errorType: error.type,
        severity: error.severity,
        message: `Consecutive failures exceeded threshold: ${metrics.consecutiveFailures} > ${thresholds.maxConsecutiveFailures}`,
        consecutiveFailures: metrics.consecutiveFailures,
        threshold: thresholds.maxConsecutiveFailures
      });
    }

    // Check for critical errors requiring immediate attention
    if (error.severity === ErrorSeverity.CRITICAL) {
      await this.createAlert({
        type: 'manual_intervention_required',
        errorType: error.type,
        severity: error.severity,
        message: `Critical error requires immediate manual intervention: ${error.message}`
      });
    }
  }

  /**
   * Create and process an alert
   */
  private async createAlert(alertData: Partial<Alert>): Promise<void> {
    const alert: Alert = {
      id: this.generateAlertId(),
      type: alertData.type || 'system_degraded',
      errorType: alertData.errorType || 'unknown',
      severity: alertData.severity || ErrorSeverity.MEDIUM,
      message: alertData.message || 'System alert triggered',
      currentFrequency: alertData.currentFrequency,
      consecutiveFailures: alertData.consecutiveFailures,
      threshold: alertData.threshold,
      timestamp: new Date().toISOString(),
      acknowledged: false,
      escalated: false,
      notificationsSent: []
    };

    // Check if similar alert already exists and is not resolved
    const existingAlert = this.alerts.find(a => 
      a.type === alert.type && 
      a.errorType === alert.errorType && 
      !a.resolvedAt
    );

    if (existingAlert) {
      // Update existing alert instead of creating duplicate
      existingAlert.message = alert.message;
      existingAlert.timestamp = alert.timestamp;
      return;
    }

    this.alerts.push(alert);

    // Send notifications
    await this.sendAlertNotifications(alert);

    console.warn('ALERT CREATED:', alert);
  }

  /**
   * Send alert notifications
   */
  private async sendAlertNotifications(alert: Alert): Promise<void> {
    try {
      // Log alert (in production, this would send emails, Slack messages, etc.)
      console.error(`🚨 ALERT: ${alert.type.toUpperCase()}`, {
        errorType: alert.errorType,
        severity: alert.severity,
        message: alert.message,
        timestamp: alert.timestamp
      });

      // Mark notification as sent
      alert.notificationsSent.push('console');

      // For critical alerts, escalate immediately
      if (alert.severity === ErrorSeverity.CRITICAL) {
        await this.escalateAlert(alert);
      }

    } catch (notificationError) {
      console.error('Failed to send alert notification:', notificationError);
    }
  }

  /**
   * Escalate alert to higher priority channels
   */
  private async escalateAlert(alert: Alert): Promise<void> {
    alert.escalated = true;
    console.error(`🔥 ESCALATED ALERT: ${alert.type.toUpperCase()}`, alert);
    // In production, this would trigger immediate notifications to on-call engineers
  }

  /**
   * Generate error report for specified time range
   */
  async generateErrorReport(timeRange: TimeRange): Promise<ErrorReport> {
    const errorsInRange = this.getErrorsInTimeRange(timeRange);
    
    const summary = {
      totalErrors: errorsInRange.length,
      errorsByCategory: this.groupErrorsByCategory(errorsInRange),
      errorsBySeverity: this.groupErrorsBySeverity(errorsInRange),
      topErrors: this.getTopErrors(errorsInRange, 10),
      recoverySuccessRate: this.calculateRecoverySuccessRate(errorsInRange),
      averageRecoveryTime: this.calculateAverageRecoveryTime(errorsInRange)
    };

    const trends = {
      errorFrequencyTrend: this.calculateErrorTrend(errorsInRange),
      recoverySuccessRate: summary.recoverySuccessRate,
      meanTimeToRecovery: summary.averageRecoveryTime,
      systemHealthScore: this.calculateSystemHealthScore()
    };

    const recommendations = this.generateRecommendations(errorsInRange);
    const criticalIssues = errorsInRange.filter(e => e.severity === ErrorSeverity.CRITICAL);
    const systemImpact = this.assessOverallSystemImpact(errorsInRange);

    return {
      timeRange,
      summary,
      trends,
      recommendations,
      criticalIssues,
      systemImpact
    };
  }

  /**
   * Get errors within specified time range
   */
  private getErrorsInTimeRange(timeRange: TimeRange): SystemError[] {
    return this.errorHistory.filter(error => {
      const errorTime = new Date(error.timestamp);
      return errorTime >= timeRange.start && errorTime <= timeRange.end;
    });
  }

  /**
   * Group errors by category
   */
  private groupErrorsByCategory(errors: SystemError[]): Record<ErrorCategory, number> {
    const grouped = {} as Record<ErrorCategory, number>;
    
    for (const category of Object.values(ErrorCategory)) {
      grouped[category] = errors.filter(e => e.category === category).length;
    }
    
    return grouped;
  }

  /**
   * Group errors by severity
   */
  private groupErrorsBySeverity(errors: SystemError[]): Record<ErrorSeverity, number> {
    const grouped = {} as Record<ErrorSeverity, number>;
    
    for (const severity of Object.values(ErrorSeverity)) {
      grouped[severity] = errors.filter(e => e.severity === severity).length;
    }
    
    return grouped;
  }

  /**
   * Get top errors by frequency
   */
  private getTopErrors(errors: SystemError[], limit: number): Array<{ type: string; count: number; severity: ErrorSeverity }> {
    const errorCounts = new Map<string, { count: number; severity: ErrorSeverity }>();
    
    errors.forEach(error => {
      const existing = errorCounts.get(error.type) || { count: 0, severity: error.severity };
      existing.count++;
      errorCounts.set(error.type, existing);
    });

    return Array.from(errorCounts.entries())
      .map(([type, data]) => ({ type, count: data.count, severity: data.severity }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  /**
   * Calculate frequency of errors for a specific type
   */
  private calculateFrequency(errorKey: string): number {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentErrors = this.errorHistory.filter(error => {
      const errorTime = new Date(error.timestamp);
      const key = `${error.category}_${error.type}`;
      return key === errorKey && errorTime >= oneHourAgo;
    });
    
    return recentErrors.length;
  }

  /**
   * Calculate error trend
   */
  private calculateTrend(errorKey: string): 'increasing' | 'decreasing' | 'stable' {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000);

    const recentErrors = this.errorHistory.filter(error => {
      const errorTime = new Date(error.timestamp);
      const key = `${error.category}_${error.type}`;
      return key === errorKey && errorTime >= oneHourAgo;
    }).length;

    const previousErrors = this.errorHistory.filter(error => {
      const errorTime = new Date(error.timestamp);
      const key = `${error.category}_${error.type}`;
      return key === errorKey && errorTime >= twoHoursAgo && errorTime < oneHourAgo;
    }).length;

    if (recentErrors > previousErrors * 1.2) return 'increasing';
    if (recentErrors < previousErrors * 0.8) return 'decreasing';
    return 'stable';
  }

  /**
   * Calculate impact score for an error
   */
  private calculateImpactScore(error: SystemError): number {
    let score = 0;
    
    // Base score by severity
    switch (error.severity) {
      case ErrorSeverity.CRITICAL: score += 100; break;
      case ErrorSeverity.HIGH: score += 75; break;
      case ErrorSeverity.MEDIUM: score += 50; break;
      case ErrorSeverity.LOW: score += 25; break;
      case ErrorSeverity.INFO: score += 10; break;
    }

    // Adjust by affected systems
    score += error.classification.affectedSystems.length * 10;

    // Adjust by impact assessment
    const impact = error.classification.estimatedImpact;
    if (impact.systemImpact === 'critical') score += 50;
    if (impact.businessImpact === 'high') score += 30;
    if (impact.userImpact === 'high') score += 20;

    return Math.min(score, 200); // Cap at 200
  }

  /**
   * Calculate recovery success rate
   */
  private calculateRecoverySuccessRate(errors: SystemError[]): number {
    const recoverableErrors = errors.filter(e => e.classification.autoRecoverable);
    if (recoverableErrors.length === 0) return 100;

    // This would need to be tracked with actual recovery results
    // For now, return a placeholder
    return 85; // 85% success rate
  }

  /**
   * Calculate average recovery time
   */
  private calculateAverageRecoveryTime(errors: SystemError[]): number {
    // This would need to be tracked with actual recovery times
    // For now, return a placeholder
    return 30000; // 30 seconds average
  }

  /**
   * Calculate error trend for report
   */
  private calculateErrorTrend(errors: SystemError[]): 'increasing' | 'decreasing' | 'stable' {
    if (errors.length < 2) return 'stable';

    const midpoint = Math.floor(errors.length / 2);
    const firstHalf = errors.slice(0, midpoint).length;
    const secondHalf = errors.slice(midpoint).length;

    if (secondHalf > firstHalf * 1.2) return 'increasing';
    if (secondHalf < firstHalf * 0.8) return 'decreasing';
    return 'stable';
  }

  /**
   * Calculate system health score
   */
  private calculateSystemHealthScore(): number {
    const recentErrors = this.errorHistory.filter(error => {
      const errorTime = new Date(error.timestamp);
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      return errorTime >= oneHourAgo;
    });

    const criticalErrors = recentErrors.filter(e => e.severity === ErrorSeverity.CRITICAL).length;
    const highErrors = recentErrors.filter(e => e.severity === ErrorSeverity.HIGH).length;

    let score = 100;
    score -= criticalErrors * 20;
    score -= highErrors * 10;
    score -= recentErrors.length * 2;

    return Math.max(score, 0);
  }

  /**
   * Generate recommendations based on error patterns
   */
  private generateRecommendations(errors: SystemError[]): string[] {
    const recommendations: string[] = [];
    
    const errorsByCategory = this.groupErrorsByCategory(errors);
    
    // AI provider recommendations
    if (errorsByCategory[ErrorCategory.AI_GENERATION] > 5) {
      recommendations.push('Consider implementing more aggressive AI provider fallback strategies');
      recommendations.push('Review AI provider quotas and rate limits');
    }

    // Network recommendations
    if (errorsByCategory[ErrorCategory.NETWORK] > 10) {
      recommendations.push('Investigate network connectivity issues');
      recommendations.push('Consider implementing circuit breaker pattern');
    }

    // Database recommendations
    if (errorsByCategory[ErrorCategory.STORAGE] > 3) {
      recommendations.push('Review database connection pool settings');
      recommendations.push('Consider implementing database health checks');
    }

    return recommendations;
  }

  /**
   * Assess overall system impact
   */
  private assessOverallSystemImpact(errors: SystemError[]): ImpactAssessment {
    const criticalErrors = errors.filter(e => e.severity === ErrorSeverity.CRITICAL).length;
    const highErrors = errors.filter(e => e.severity === ErrorSeverity.HIGH).length;

    if (criticalErrors > 0) {
      return { userImpact: 'high', systemImpact: 'critical', businessImpact: 'high' };
    }
    
    if (highErrors > 5) {
      return { userImpact: 'medium', systemImpact: 'high', businessImpact: 'medium' };
    }

    return { userImpact: 'low', systemImpact: 'low', businessImpact: 'low' };
  }

  /**
   * Initialize alert thresholds
   */
  private initializeAlertThresholds(): void {
    // Network error thresholds
    this.alertThresholds.set('NETWORK_TIMEOUT', {
      maxFrequency: 10,
      maxConsecutiveFailures: 5,
      escalationTime: 30 * 60 * 1000
    });

    // AI provider thresholds
    this.alertThresholds.set('OPENAI_RATE_LIMIT', {
      maxFrequency: 5,
      maxConsecutiveFailures: 3,
      escalationTime: 60 * 60 * 1000
    });

    this.alertThresholds.set('OPENROUTER_INSUFFICIENT_CREDITS', {
      maxFrequency: 1,
      maxConsecutiveFailures: 1,
      escalationTime: 15 * 60 * 1000
    });

    // Database thresholds
    this.alertThresholds.set('DATABASE_CONNECTION_LOST', {
      maxFrequency: 3,
      maxConsecutiveFailures: 2,
      escalationTime: 5 * 60 * 1000
    });
  }

  /**
   * Log error for debugging
   */
  private logError(error: SystemError, context: ErrorContext): void {
    console.log('ERROR TRACKED:', {
      id: error.id,
      type: error.type,
      category: error.category,
      severity: error.severity,
      message: error.message,
      context: {
        operation: context.operation,
        provider: context.provider,
        toolId: context.toolId,
        jobId: context.jobId
      },
      timestamp: error.timestamp
    });
  }

  /**
   * Clean up old error data
   */
  private cleanupOldData(): void {
    const retentionDays = 30;
    const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
    
    // Remove old errors
    this.errorHistory = this.errorHistory.filter(error => 
      new Date(error.timestamp) >= cutoffDate
    );

    // Remove resolved alerts older than 7 days
    const alertCutoff = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    this.alerts = this.alerts.filter(alert => 
      !alert.resolvedAt || new Date(alert.resolvedAt) >= alertCutoff
    );
  }

  /**
   * Start periodic cleanup
   */
  private startPeriodicCleanup(): void {
    // Clean up every hour
    setInterval(() => {
      this.cleanupOldData();
    }, 60 * 60 * 1000);
  }

  /**
   * Generate unique alert ID
   */
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get current error metrics
   */
  getErrorMetrics(): Map<string, ErrorMetrics> {
    return new Map(this.errorMetrics);
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): Alert[] {
    return this.alerts.filter(alert => !alert.resolvedAt);
  }

  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      return true;
    }
    return false;
  }

  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolvedAt = new Date().toISOString();
      return true;
    }
    return false;
  }
}
