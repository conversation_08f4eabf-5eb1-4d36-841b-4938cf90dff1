import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, EmailNotificationJobData } from '../types';
import * as nodemailer from 'nodemailer';

export class EmailNotificationHandler implements JobHandler {
  private _transporter: nodemailer.Transporter | null = null;

  private get transporter() {
    if (!this._transporter) {
      this._transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: false, // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      });
    }
    return this._transporter;
  }

  async handle(job: Job): Promise<any> {
    const data = job.data as EmailNotificationJobData;
    
    if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {
      throw new Error('SMTP configuration is missing');
    }

    try {
      const emailContent = this.generateEmailContent(data.template, data.data);
      
      const mailOptions = {
        from: `"AI Dude Directory" <${process.env.SMTP_USER}>`,
        to: Array.isArray(data.to) ? data.to.join(', ') : data.to,
        subject: data.subject,
        html: emailContent.html,
        text: emailContent.text,
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      return {
        success: true,
        messageId: result.messageId,
        sentAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Email sending failed:', error);
      throw new Error(`Email sending failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private generateEmailContent(template: string, data: any): { html: string; text: string } {
    switch (template) {
      case 'tool-submission-received':
        return this.toolSubmissionReceivedTemplate(data);
      case 'admin-tool-submission':
        return this.adminToolSubmissionTemplate(data);
      case 'admin-processing-error':
        return this.adminProcessingErrorTemplate(data);
      case 'tool-approved':
        return this.toolApprovedTemplate(data);
      case 'tool-rejected':
        return this.toolRejectedTemplate(data);
      case 'admin-tool-created':
        return this.adminToolCreatedTemplate(data);
      default:
        throw new Error(`Unknown email template: ${template}`);
    }
  }

  private toolSubmissionReceivedTemplate(data: any): { html: string; text: string } {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Thanks for Your Tool Submission!</h2>
        <p>Hey ${data.submitterName || 'there'},</p>
        <p>We've received your submission for <strong>${data.toolName}</strong> and our AI overlords are already getting to work analyzing it.</p>
        
        <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Submission Details:</h3>
          <p><strong>Tool Name:</strong> ${data.toolName}</p>
          <p><strong>URL:</strong> <a href="${data.toolUrl}">${data.toolUrl}</a></p>
          <p><strong>Submission ID:</strong> ${data.toolId}</p>
        </div>
        
        <p>Here's what happens next:</p>
        <ul>
          <li>🤖 Our AI will scrape and analyze your tool</li>
          <li>✍️ Generate witty, engaging content (because boring is banned)</li>
          <li>👨‍💼 Our human overlord will review everything</li>
          <li>🚀 If approved, your tool goes live on AI Dude Directory</li>
        </ul>
        
        <p>We'll keep you posted on the status. In the meantime, feel free to browse our directory and see what other AI tools are making waves.</p>
        
        <p>Stay awesome,<br>The AI Dude Team</p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="font-size: 12px; color: #666;">
          This is an automated message. If you have questions, reply to this email or contact us through our website.
        </p>
      </div>
    `;

    const text = `
Thanks for Your Tool Submission!

Hey ${data.submitterName || 'there'},

We've received your submission for ${data.toolName} and our AI overlords are already getting to work analyzing it.

Submission Details:
- Tool Name: ${data.toolName}
- URL: ${data.toolUrl}
- Submission ID: ${data.toolId}

Here's what happens next:
- Our AI will scrape and analyze your tool
- Generate witty, engaging content (because boring is banned)
- Our human overlord will review everything
- If approved, your tool goes live on AI Dude Directory

We'll keep you posted on the status. In the meantime, feel free to browse our directory and see what other AI tools are making waves.

Stay awesome,
The AI Dude Team
    `;

    return { html, text };
  }

  private adminToolSubmissionTemplate(data: any): { html: string; text: string } {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">New Tool Submission Received</h2>
        
        <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Submission Details:</h3>
          <p><strong>Tool Name:</strong> ${data.toolName}</p>
          <p><strong>URL:</strong> <a href="${data.toolUrl}">${data.toolUrl}</a></p>
          <p><strong>Submitter:</strong> ${data.submitterName || 'N/A'} (${data.submitterEmail})</p>
          <p><strong>Tool ID:</strong> ${data.toolId}</p>
        </div>
        
        <p>The automated processing pipeline has been triggered. Check the admin dashboard for the generated content and review status.</p>
        
        <p><a href="${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/admin/tools/${data.toolId}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Review Submission</a></p>
      </div>
    `;

    const text = `
New Tool Submission Received

Submission Details:
- Tool Name: ${data.toolName}
- URL: ${data.toolUrl}
- Submitter: ${data.submitterName || 'N/A'} (${data.submitterEmail})
- Tool ID: ${data.toolId}

The automated processing pipeline has been triggered. Check the admin dashboard for the generated content and review status.
    `;

    return { html, text };
  }

  private adminProcessingErrorTemplate(data: any): { html: string; text: string } {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #dc3545;">Tool Processing Failed</h2>
        
        <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #f5c6cb;">
          <h3 style="margin-top: 0; color: #721c24;">Error Details:</h3>
          <p><strong>Tool Name:</strong> ${data.toolName}</p>
          <p><strong>URL:</strong> <a href="${data.toolUrl}">${data.toolUrl}</a></p>
          <p><strong>Submitter:</strong> ${data.submitterEmail}</p>
          <p><strong>Error:</strong> ${data.error}</p>
        </div>
        
        <p>The automated processing pipeline encountered an error. Manual intervention may be required.</p>
      </div>
    `;

    const text = `
Tool Processing Failed

Error Details:
- Tool Name: ${data.toolName}
- URL: ${data.toolUrl}
- Submitter: ${data.submitterEmail}
- Error: ${data.error}

The automated processing pipeline encountered an error. Manual intervention may be required.
    `;

    return { html, text };
  }

  private toolApprovedTemplate(data: any): { html: string; text: string } {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #28a745;">🎉 Your Tool is Live!</h2>
        <p>Great news! Your tool <strong>${data.toolName}</strong> has been approved and is now live on AI Dude Directory.</p>
        
        <p><a href="${data.toolUrl}" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Your Tool</a></p>
        
        <p>Thanks for contributing to the AI community!</p>
      </div>
    `;

    const text = `
🎉 Your Tool is Live!

Great news! Your tool ${data.toolName} has been approved and is now live on AI Dude Directory.

View your tool: ${data.toolUrl}

Thanks for contributing to the AI community!
    `;

    return { html, text };
  }

  private toolRejectedTemplate(data: any): { html: string; text: string } {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #dc3545;">Tool Submission Update</h2>
        <p>Thanks for submitting <strong>${data.toolName}</strong> to AI Dude Directory.</p>
        
        <p>After review, we've decided not to include this tool in our directory at this time.</p>
        
        ${data.reason ? `<p><strong>Reason:</strong> ${data.reason}</p>` : ''}
        
        <p>Feel free to submit other tools in the future!</p>
      </div>
    `;

    const text = `
Tool Submission Update

Thanks for submitting ${data.toolName} to AI Dude Directory.

After review, we've decided not to include this tool in our directory at this time.

${data.reason ? `Reason: ${data.reason}` : ''}

Feel free to submit other tools in the future!
    `;

    return { html, text };
  }

  private adminToolCreatedTemplate(data: any): { html: string; text: string } {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">New Tool Created</h2>

        <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Tool Details:</h3>
          <p><strong>Tool Name:</strong> ${data.toolName}</p>
          <p><strong>URL:</strong> <a href="${data.toolUrl}">${data.toolUrl}</a></p>
          <p><strong>Tool ID:</strong> ${data.toolId}</p>
        </div>

        <p>A new tool has been created in the system. Review and approve when ready.</p>

        <p><a href="${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/admin/tools/${data.toolId}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Review Tool</a></p>
      </div>
    `;

    const text = `
New Tool Created

Tool Details:
- Tool Name: ${data.toolName}
- URL: ${data.toolUrl}
- Tool ID: ${data.toolId}

A new tool has been created in the system. Review and approve when ready.
    `;

    return { html, text };
  }
}
