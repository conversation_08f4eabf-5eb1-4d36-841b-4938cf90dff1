'use client';

import React from 'react';
import { AICategory } from '@/lib/types';
import { CategoryCard } from './CategoryCard';
import { getLayoutClasses, activeLayoutConfig } from '@/config/layout';

interface CategoryGridProps {
  categories: AICategory[];
  onShowTooltip: (content: string, element: HTMLElement, triggerType?: 'title' | 'search-icon') => void;
  onHideTooltip: () => void;
}

export function CategoryGrid({ categories, onShowTooltip, onHideTooltip }: CategoryGridProps) {
  const layoutClasses = getLayoutClasses(activeLayoutConfig);

  return (
    <div className="w-full -mt-4">
      <div className="mx-auto px-4 pb-6" style={{ maxWidth: 'var(--container-width)' }}>
        <div className={layoutClasses.gridContainer}>
          {categories.map((category) => (
            <CategoryCard
              key={category.id}
              category={category}
              onShowTooltip={onShowTooltip}
              onHideTooltip={onHideTooltip}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
