'use client';

import React from 'react';
import { CategoryGrid } from '@/components/features/CategoryGrid';
import { Tooltip } from '@/components/features/Tooltip';
import { AIDudeStory } from '@/components/features/AIDudeStory';
import { useSearchContext } from '@/providers/SearchProvider';
import { useTooltip } from '@/hooks/useTooltip';
import { AICategory } from '@/lib/types';

interface HomePageClientProps {
  categories: AICategory[];
}

export function HomePageClient({ categories }: HomePageClientProps) {
  const {
    searchTerm,
    isSearchDropdownVisible,
    searchResults,
    isLoadingSearchResults,
    handleSearchFocus,
    handleSearchBlur,
    handleSearchChange,
    handleTopSearchClick,
  } = useSearchContext();

  const { activeTooltip, triggerType, showTooltip, hideTooltip } = useTooltip();

  // Show search results if available, otherwise show categories
  const displayCategories = searchResults ? [] : categories;
  const showSearchResults = searchResults && searchResults.length > 0;

  return (
    <div className="w-full">
      {isLoadingSearchResults ? (
        <div className="flex items-center justify-center py-20 w-full">
          <div className="text-white text-lg">Searching AI tools...</div>
        </div>
      ) : showSearchResults ? (
        <div className="w-full -mt-4">
          <div className="mx-auto px-4 pb-6" style={{ maxWidth: 'var(--container-width)' }}>
            <h2 className="text-2xl font-bold text-white mb-6">
              Search Results ({searchResults.length} tools found)
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {searchResults.map((tool) => (
                <div
                  key={tool.id}
                  className="bg-zinc-800 border border-zinc-700 p-4 rounded-lg hover:bg-zinc-700 transition-colors duration-200 cursor-pointer shadow-lg"
                  onClick={() => window.open(tool.link, '_blank')}
                >
                  <h3 className="text-white font-medium mb-2">{tool.name}</h3>
                  <p className="text-gray-400 text-sm">{tool.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <CategoryGrid
          categories={displayCategories}
          onShowTooltip={showTooltip}
          onHideTooltip={hideTooltip}
        />
      )}

      {/* AI Dude Story Section */}
      <AIDudeStory />

      {/* Global Tooltip */}
      {activeTooltip && <Tooltip tooltip={activeTooltip} triggerType={triggerType} />}
    </div>
  );
}
