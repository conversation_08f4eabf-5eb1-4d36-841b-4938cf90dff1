import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * Job Queue Health Check API
 * Tests job processing system health and statistics
 */
export async function GET(request: NextRequest) {
  try {
    const startTime = Date.now();

    // Get job statistics from the last 24 hours
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

    const { data: jobs, error } = await supabase
      .from('ai_generation_jobs')
      .select('status, created_at')
      .gte('created_at', twentyFourHoursAgo);

    const responseTime = Date.now() - startTime;

    if (error) {
      return NextResponse.json(
        {
          status: 'unhealthy',
          error: error.message,
          responseTime,
          timestamp: new Date().toISOString()
        },
        { status: 503 }
      );
    }

    // Calculate job statistics
    const total = jobs?.length || 0;
    const completed = jobs?.filter(job => job.status === 'completed').length || 0;
    const failed = jobs?.filter(job => job.status === 'failed').length || 0;
    const pending = jobs?.filter(job => job.status === 'pending').length || 0;
    const processing = jobs?.filter(job => job.status === 'processing').length || 0;

    const failureRate = total > 0 ? failed / total : 0;
    const successRate = total > 0 ? completed / total : 1;

    // Determine health status based on failure rate
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    if (failureRate > 0.5) {
      status = 'unhealthy';
    } else if (failureRate > 0.1) {
      status = 'degraded';
    }

    // Check for stuck jobs (processing for more than 1 hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    const { data: stuckJobs } = await supabase
      .from('ai_generation_jobs')
      .select('id')
      .eq('status', 'processing')
      .lt('updated_at', oneHourAgo);

    const stuckJobsCount = stuckJobs?.length || 0;
    if (stuckJobsCount > 0 && status === 'healthy') {
      status = 'degraded';
    }

    return NextResponse.json({
      status,
      responseTime,
      timestamp: new Date().toISOString(),
      stats: {
        total,
        completed,
        failed,
        pending,
        processing,
        stuckJobs: stuckJobsCount,
        failureRate: Math.round(failureRate * 100) / 100,
        successRate: Math.round(successRate * 100) / 100
      },
      details: {
        timeRange: '24 hours',
        healthThreshold: 'failure rate < 10%',
        degradedThreshold: 'failure rate < 50%'
      }
    });

  } catch (error: any) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 503 }
    );
  }
}
