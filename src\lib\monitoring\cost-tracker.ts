/**
 * Cost Tracker Service
 * 
 * Monitors and tracks costs for:
 * - AI provider usage (OpenAI, OpenRouter)
 * - Scraping service costs (scrape.do)
 * - Cost optimization tracking
 * - Budget alerts and recommendations
 */

import { createClient } from '@supabase/supabase-js';
import { EventEmitter } from 'events';

export interface CostEntry {
  id?: string;
  service_type: 'openai' | 'openrouter' | 'scrape_do' | 'other';
  service_name: string;
  operation_type: 'content_generation' | 'web_scraping' | 'image_processing' | 'other';
  cost_amount: number;
  currency: 'USD' | 'EUR' | 'GBP';
  usage_units?: number;
  unit_type?: 'tokens' | 'requests' | 'pages' | 'images';
  metadata?: Record<string, any>;
  timestamp: string;
  tool_id?: string;
  job_id?: string;
}

export interface CostSummary {
  timeRange: string;
  totalCost: number;
  costByService: Record<string, number>;
  costByOperation: Record<string, number>;
  averageCostPerOperation: number;
  costTrend: 'increasing' | 'decreasing' | 'stable';
  budgetUtilization?: number;
  projectedMonthlyCost: number;
}

export interface CostOptimizationMetrics {
  totalSavings: number;
  savingsPercentage: number;
  optimizationStrategies: Array<{
    strategy: string;
    savingsAmount: number;
    usageCount: number;
  }>;
  recommendations: string[];
}

export interface BudgetAlert {
  id: string;
  alertType: 'budget_threshold' | 'cost_spike' | 'optimization_opportunity';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  currentCost: number;
  threshold?: number;
  recommendations: string[];
  timestamp: string;
}

export class CostTracker extends EventEmitter {
  private supabase: any;
  private costEntries: CostEntry[] = [];
  private budgetLimits: Record<string, number> = {};
  private isTracking = false;

  // Cost rates (updated periodically)
  private readonly costRates = {
    openai: {
      'gpt-4o-2024-11-20': {
        input: 0.0025,  // per 1K tokens
        output: 0.01    // per 1K tokens
      }
    },
    openrouter: {
      'google/gemini-2.5-pro-preview': {
        input: 0.00125, // per 1K tokens
        output: 0.005   // per 1K tokens
      }
    },
    scrape_do: {
      standard: 0.001,     // per request
      render: 0.005,       // per request with rendering
      screenshot: 0.002    // per screenshot
    }
  };

  constructor() {
    super();
    
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    this.supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Set default budget limits (monthly)
    this.budgetLimits = {
      openai: 100,      // $100/month
      openrouter: 50,   // $50/month
      scrape_do: 200,   // $200/month
      total: 500        // $500/month total
    };
  }

  /**
   * Start cost tracking
   */
  async startTracking(): Promise<void> {
    if (this.isTracking) return;

    this.isTracking = true;
    console.log('Cost tracking started');

    // Check for budget alerts periodically
    setInterval(async () => {
      await this.checkBudgetAlerts();
    }, 60000 * 60); // Every hour

    this.emit('tracking_started');
  }

  /**
   * Stop cost tracking
   */
  stopTracking(): void {
    this.isTracking = false;
    console.log('Cost tracking stopped');
    this.emit('tracking_stopped');
  }

  /**
   * Record a cost entry
   */
  async recordCost(cost: Omit<CostEntry, 'timestamp'>): Promise<void> {
    const fullCostEntry: CostEntry = {
      ...cost,
      timestamp: new Date().toISOString()
    };

    // Add to in-memory collection
    this.costEntries.push(fullCostEntry);

    // Persist to database
    try {
      const { error } = await this.supabase
        .from('cost_tracking')
        .insert(fullCostEntry);

      if (error) {
        console.error('Failed to persist cost entry:', error);
      }
    } catch (error) {
      console.error('Error persisting cost entry:', error);
    }

    this.emit('cost_recorded', fullCostEntry);

    // Check if this triggers any budget alerts
    await this.checkBudgetAlerts();
  }

  /**
   * Record OpenAI API cost
   */
  async recordOpenAICost(
    model: string,
    inputTokens: number,
    outputTokens: number,
    toolId?: string,
    jobId?: string
  ): Promise<void> {
    const rates = this.costRates.openai[model as keyof typeof this.costRates.openai];
    if (!rates) {
      console.warn(`Unknown OpenAI model for cost calculation: ${model}`);
      return;
    }

    const inputCost = (inputTokens / 1000) * rates.input;
    const outputCost = (outputTokens / 1000) * rates.output;
    const totalCost = inputCost + outputCost;

    await this.recordCost({
      service_type: 'openai',
      service_name: model,
      operation_type: 'content_generation',
      cost_amount: totalCost,
      currency: 'USD',
      usage_units: inputTokens + outputTokens,
      unit_type: 'tokens',
      metadata: {
        inputTokens,
        outputTokens,
        inputCost,
        outputCost
      },
      tool_id: toolId,
      job_id: jobId
    });
  }

  /**
   * Record OpenRouter API cost
   */
  async recordOpenRouterCost(
    model: string,
    inputTokens: number,
    outputTokens: number,
    toolId?: string,
    jobId?: string
  ): Promise<void> {
    const rates = this.costRates.openrouter[model as keyof typeof this.costRates.openrouter];
    if (!rates) {
      console.warn(`Unknown OpenRouter model for cost calculation: ${model}`);
      return;
    }

    const inputCost = (inputTokens / 1000) * rates.input;
    const outputCost = (outputTokens / 1000) * rates.output;
    const totalCost = inputCost + outputCost;

    await this.recordCost({
      service_type: 'openrouter',
      service_name: model,
      operation_type: 'content_generation',
      cost_amount: totalCost,
      currency: 'USD',
      usage_units: inputTokens + outputTokens,
      unit_type: 'tokens',
      metadata: {
        inputTokens,
        outputTokens,
        inputCost,
        outputCost
      },
      tool_id: toolId,
      job_id: jobId
    });
  }

  /**
   * Record scrape.do cost
   */
  async recordScrapingCost(
    requestType: 'standard' | 'render' | 'screenshot',
    requestCount: number = 1,
    toolId?: string,
    jobId?: string
  ): Promise<void> {
    const costPerRequest = this.costRates.scrape_do[requestType];
    const totalCost = costPerRequest * requestCount;

    await this.recordCost({
      service_type: 'scrape_do',
      service_name: 'scrape.do',
      operation_type: 'web_scraping',
      cost_amount: totalCost,
      currency: 'USD',
      usage_units: requestCount,
      unit_type: 'requests',
      metadata: {
        requestType,
        costPerRequest
      },
      tool_id: toolId,
      job_id: jobId
    });
  }

  /**
   * Get cost summary for a time range
   */
  async getCostSummary(timeRange: string = '30d'): Promise<CostSummary> {
    try {
      const hoursBack = this.parseTimeRange(timeRange);
      const startTime = new Date(Date.now() - hoursBack * 60 * 60 * 1000).toISOString();

      const { data: costs, error } = await this.supabase
        .from('cost_tracking')
        .select('*')
        .gte('timestamp', startTime)
        .order('timestamp', { ascending: false });

      if (error) {
        console.error('Failed to fetch cost data:', error);
        return this.getEmptyCostSummary(timeRange);
      }

      const costEntries = costs || [];
      const totalCost = costEntries.reduce((sum: number, entry: CostEntry) => sum + entry.cost_amount, 0);

      // Group by service
      const costByService: Record<string, number> = {};
      const costByOperation: Record<string, number> = {};

      costEntries.forEach((entry: CostEntry) => {
        costByService[entry.service_type] = (costByService[entry.service_type] || 0) + entry.cost_amount;
        costByOperation[entry.operation_type] = (costByOperation[entry.operation_type] || 0) + entry.cost_amount;
      });

      // Calculate projections
      const daysInRange = hoursBack / 24;
      const dailyAverage = totalCost / daysInRange;
      const projectedMonthlyCost = dailyAverage * 30;

      // Calculate trend (simplified)
      const costTrend = this.calculateCostTrend(costEntries);

      // Calculate budget utilization
      const monthlyBudget = this.budgetLimits.total;
      const budgetUtilization = monthlyBudget > 0 ? (projectedMonthlyCost / monthlyBudget) * 100 : undefined;

      return {
        timeRange,
        totalCost,
        costByService,
        costByOperation,
        averageCostPerOperation: costEntries.length > 0 ? totalCost / costEntries.length : 0,
        costTrend,
        budgetUtilization,
        projectedMonthlyCost
      };
    } catch (error) {
      console.error('Error generating cost summary:', error);
      return this.getEmptyCostSummary(timeRange);
    }
  }

  /**
   * Get cost optimization metrics
   */
  async getCostOptimizationMetrics(timeRange: string = '30d'): Promise<CostOptimizationMetrics> {
    // This would analyze cost patterns and identify optimization opportunities
    // For now, returning a simplified implementation
    
    const summary = await this.getCostSummary(timeRange);
    const totalCost = summary.totalCost;
    
    // Estimate savings from using cost-optimized strategies
    const estimatedSavings = totalCost * 0.15; // Assume 15% savings potential
    const savingsPercentage = totalCost > 0 ? (estimatedSavings / totalCost) * 100 : 0;

    return {
      totalSavings: estimatedSavings,
      savingsPercentage,
      optimizationStrategies: [
        {
          strategy: 'Use OpenRouter for non-critical content',
          savingsAmount: estimatedSavings * 0.6,
          usageCount: 0
        },
        {
          strategy: 'Batch scraping requests',
          savingsAmount: estimatedSavings * 0.4,
          usageCount: 0
        }
      ],
      recommendations: this.generateCostRecommendations(summary)
    };
  }

  /**
   * Check for budget alerts
   */
  private async checkBudgetAlerts(): Promise<void> {
    const monthlySummary = await this.getCostSummary('30d');
    const alerts: BudgetAlert[] = [];

    // Check total budget
    if (monthlySummary.budgetUtilization && monthlySummary.budgetUtilization > 80) {
      alerts.push({
        id: `budget_alert_${Date.now()}`,
        alertType: 'budget_threshold',
        severity: monthlySummary.budgetUtilization > 95 ? 'critical' : 'high',
        message: `Monthly budget utilization at ${monthlySummary.budgetUtilization.toFixed(1)}%`,
        currentCost: monthlySummary.projectedMonthlyCost,
        threshold: this.budgetLimits.total,
        recommendations: [
          'Review high-cost operations',
          'Consider switching to more cost-effective AI providers',
          'Implement additional cost optimization strategies'
        ],
        timestamp: new Date().toISOString()
      });
    }

    // Check for cost spikes
    const dailySummary = await this.getCostSummary('1d');
    const averageDailyCost = monthlySummary.totalCost / 30;
    
    if (dailySummary.totalCost > averageDailyCost * 2) {
      alerts.push({
        id: `spike_alert_${Date.now()}`,
        alertType: 'cost_spike',
        severity: 'medium',
        message: `Daily cost spike detected: $${dailySummary.totalCost.toFixed(2)} (avg: $${averageDailyCost.toFixed(2)})`,
        currentCost: dailySummary.totalCost,
        recommendations: [
          'Investigate unusual activity',
          'Check for failed jobs causing retries',
          'Review recent configuration changes'
        ],
        timestamp: new Date().toISOString()
      });
    }

    // Emit alerts
    alerts.forEach(alert => {
      this.emit('budget_alert', alert);
    });
  }

  /**
   * Parse time range string to hours
   */
  private parseTimeRange(timeRange: string): number {
    const match = timeRange.match(/^(\d+)([hmd])$/);
    if (!match) return 24; // Default to 1 day

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 'h': return value;      // hours
      case 'd': return value * 24; // days to hours
      case 'm': return value * 24 * 30; // months to hours (approximate)
      default: return 24;
    }
  }

  /**
   * Calculate cost trend
   */
  private calculateCostTrend(costEntries: CostEntry[]): 'increasing' | 'decreasing' | 'stable' {
    if (costEntries.length < 2) return 'stable';

    // Simple trend calculation based on first and second half of data
    const midpoint = Math.floor(costEntries.length / 2);
    const firstHalf = costEntries.slice(0, midpoint);
    const secondHalf = costEntries.slice(midpoint);

    const firstHalfAvg = firstHalf.reduce((sum, entry) => sum + entry.cost_amount, 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, entry) => sum + entry.cost_amount, 0) / secondHalf.length;

    const difference = secondHalfAvg - firstHalfAvg;
    const threshold = firstHalfAvg * 0.1; // 10% threshold

    if (difference > threshold) return 'increasing';
    if (difference < -threshold) return 'decreasing';
    return 'stable';
  }

  /**
   * Generate cost recommendations
   */
  private generateCostRecommendations(summary: CostSummary): string[] {
    const recommendations: string[] = [];

    if (summary.budgetUtilization && summary.budgetUtilization > 80) {
      recommendations.push('Consider increasing budget or implementing cost reduction strategies');
    }

    if (summary.costByService.openai > summary.costByService.openrouter * 2) {
      recommendations.push('Consider using OpenRouter for non-critical content generation to reduce costs');
    }

    if (summary.costTrend === 'increasing') {
      recommendations.push('Cost trend is increasing. Review recent changes and optimize high-cost operations');
    }

    if (recommendations.length === 0) {
      recommendations.push('Cost management is within acceptable ranges');
    }

    return recommendations;
  }

  /**
   * Get empty cost summary
   */
  private getEmptyCostSummary(timeRange: string): CostSummary {
    return {
      timeRange,
      totalCost: 0,
      costByService: {},
      costByOperation: {},
      averageCostPerOperation: 0,
      costTrend: 'stable',
      projectedMonthlyCost: 0
    };
  }

  /**
   * Set budget limits
   */
  setBudgetLimits(limits: Record<string, number>): void {
    this.budgetLimits = { ...this.budgetLimits, ...limits };
    this.emit('budget_limits_updated', this.budgetLimits);
  }

  /**
   * Get current budget limits
   */
  getBudgetLimits(): Record<string, number> {
    return { ...this.budgetLimits };
  }
}

// Export singleton instance
export const costTracker = new CostTracker();
