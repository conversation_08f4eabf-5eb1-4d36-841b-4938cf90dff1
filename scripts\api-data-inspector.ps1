# PowerShell script to inspect scraped data via API endpoints

$baseUrl = "http://localhost:3000"
$apiKey = "aidude_admin_2024_secure_key_xyz789"
$headers = @{
    'x-api-key' = $apiKey
    'Content-Type' = 'application/json'
}

Write-Host "🔍 API Data Inspector - Scraped Data Viewer" -ForegroundColor Cyan
Write-Host ""

# Function to display scraped data in a readable format
function Show-ScrapedData {
    param($jobData)
    
    if ($jobData.result -and $jobData.result.data) {
        $data = $jobData.result.data
        
        Write-Host "📋 SCRAPED DATA DETAILS:" -ForegroundColor Yellow
        Write-Host "========================" -ForegroundColor Yellow
        
        Write-Host "📄 Title: $($data.title)" -ForegroundColor White
        Write-Host "🔗 URL: $($data.url)" -ForegroundColor White
        Write-Host "📝 Text Length: $($data.text.Length) characters" -ForegroundColor White
        
        if ($data.meta) {
            Write-Host "`n🏷️ META TAGS:" -ForegroundColor Green
            $data.meta.PSObject.Properties | ForEach-Object {
                $value = $_.Value
                if ($value.Length -gt 80) { $value = $value.Substring(0, 80) + "..." }
                Write-Host "  $($_.Name): $value" -ForegroundColor Gray
            }
        }
        
        if ($data.headings -and $data.headings.Count -gt 0) {
            Write-Host "`n📑 HEADINGS:" -ForegroundColor Green
            $data.headings | Select-Object -First 8 | ForEach-Object {
                $text = $_.text
                if ($text.Length -gt 60) { $text = $text.Substring(0, 60) + "..." }
                Write-Host "  $($_.level.ToUpper()): $text" -ForegroundColor Gray
            }
            if ($data.headings.Count -gt 8) {
                Write-Host "  ... and $($data.headings.Count - 8) more headings" -ForegroundColor DarkGray
            }
        }
        
        if ($data.images -and $data.images.Count -gt 0) {
            Write-Host "`n🖼️ IMAGES ($($data.images.Count) found):" -ForegroundColor Green
            $data.images | Select-Object -First 5 | ForEach-Object -Begin { $i = 1 } -Process {
                $src = $_.src
                if ($src.Length -gt 50) { $src = $src.Substring(0, 50) + "..." }
                Write-Host "  $i. $src" -ForegroundColor Gray
                if ($_.alt) {
                    $alt = $_.alt
                    if ($alt.Length -gt 40) { $alt = $alt.Substring(0, 40) + "..." }
                    Write-Host "     Alt: $alt" -ForegroundColor DarkGray
                }
                $i++
            }
        }
        
        if ($data.links -and $data.links.Count -gt 0) {
            Write-Host "`n🔗 LINKS ($($data.links.Count) found):" -ForegroundColor Green
            $data.links | Select-Object -First 5 | ForEach-Object -Begin { $i = 1 } -Process {
                $href = $_.href
                if ($href.Length -gt 50) { $href = $href.Substring(0, 50) + "..." }
                Write-Host "  $i. $href" -ForegroundColor Gray
                if ($_.text) {
                    $text = $_.text
                    if ($text.Length -gt 40) { $text = $text.Substring(0, 40) + "..." }
                    Write-Host "     Text: $text" -ForegroundColor DarkGray
                }
                $i++
            }
        }
        
        if ($data.pricing -and $data.pricing.Count -gt 0) {
            Write-Host "`n💰 PRICING INFO:" -ForegroundColor Green
            $data.pricing | Select-Object -First 3 | ForEach-Object -Begin { $i = 1 } -Process {
                $text = $_.text
                if ($text.Length -gt 60) { $text = $text.Substring(0, 60) + "..." }
                Write-Host "  $i. [$($_.tag)] $text" -ForegroundColor Gray
                $i++
            }
        }
        
        if ($data.text) {
            Write-Host "`n📖 TEXT PREVIEW:" -ForegroundColor Green
            $preview = $data.text.Substring(0, [Math]::Min(200, $data.text.Length))
            if ($data.text.Length -gt 200) { $preview += "..." }
            Write-Host "`"$preview`"" -ForegroundColor Gray
        }
        
        if ($jobData.result.screenshot) {
            Write-Host "`n📸 SCREENSHOT:" -ForegroundColor Green
            $sizeKB = [Math]::Round($jobData.result.screenshot.Length / 1024)
            Write-Host "  Format: PNG (base64)" -ForegroundColor Gray
            Write-Host "  Size: $sizeKB KB" -ForegroundColor Gray
            Write-Host "  Preview: $($jobData.result.screenshot.Substring(0, 50))..." -ForegroundColor Gray
        }
        
    } else {
        Write-Host "❌ No scraped data found in this job" -ForegroundColor Red
    }
}

try {
    # Step 1: Get all jobs
    Write-Host "📋 Fetching all jobs..." -ForegroundColor Yellow
    $allJobs = Invoke-RestMethod -Uri "$baseUrl/api/automation/jobs" -Method Get -Headers $headers
    
    # Step 2: Filter for completed web scraping jobs
    $scrapingJobs = $allJobs.data.jobs | Where-Object { $_.type -eq "web_scraping" -and $_.status -eq "completed" }
    
    if ($scrapingJobs.Count -eq 0) {
        Write-Host "❌ No completed web scraping jobs found." -ForegroundColor Red
        Write-Host "💡 Run a scraping job first with: npm run test:automation" -ForegroundColor Yellow
        exit
    }
    
    Write-Host "✅ Found $($scrapingJobs.Count) completed scraping job(s)" -ForegroundColor Green
    Write-Host ""
    
    # Step 3: Get detailed data for each job
    for ($i = 0; $i -lt $scrapingJobs.Count; $i++) {
        $job = $scrapingJobs[$i]
        
        Write-Host "🔍 Job $($i + 1): $($job.id)" -ForegroundColor Cyan
        Write-Host "⏰ Completed: $($job.completedAt)" -ForegroundColor White
        Write-Host ""
        
        # Get full job details including result data
        try {
            $jobDetails = Invoke-RestMethod -Uri "$baseUrl/api/automation/jobs/$($job.id)" -Method Get -Headers $headers
            Show-ScrapedData -jobData $jobDetails.data
        } catch {
            Write-Host "❌ Failed to get job details: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        if ($i -lt $scrapingJobs.Count - 1) {
            Write-Host ""
            Write-Host ("-" * 80) -ForegroundColor DarkGray
            Write-Host ""
        }
    }
    
} catch {
    Write-Host "❌ API Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 Data inspection completed!" -ForegroundColor Green
