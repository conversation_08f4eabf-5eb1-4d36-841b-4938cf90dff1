/**
 * Bulk Processing Job Handler
 * Handles bulk processing jobs within the job queue system
 */

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '../types';
import { BulkProcessingJobData } from '../types';
import { getBulkProcessingEngine } from '@/lib/bulk-processing/bulk-engine';

export class BulkProcessingHandler implements JobHandler {
  async handle(job: Job): Promise<any> {
    try {
      console.log(`🔄 Processing bulk job ${job.id} with ${job.data.urls.length} URLs`);

      const bulkEngine = getBulkProcessingEngine();
      
      // Convert URLs to BulkToolData format
      const bulkToolData = (job.data as BulkProcessingJobData).urls.map((url: string) => ({
        url,
        providedData: {},
        needsGeneration: {
          name: true,
          description: true,
          features: true,
          pricing: true,
          prosAndCons: true,
          haiku: true,
          hashtags: true,
        },
      }));

      // Create bulk processing job
      const jobData = job.data as BulkProcessingJobData;
      const bulkJob = await bulkEngine.createBulkJob(
        bulkToolData,
        jobData.options,
        jobData.metadata
      );

      console.log(`✅ Bulk job ${job.id} created successfully as ${bulkJob.id}`);

      return {
        success: true,
        bulkJobId: bulkJob.id,
        totalItems: bulkJob.totalItems,
        message: `Bulk processing job created with ${bulkJob.totalItems} items`,
      };
    } catch (error) {
      console.error(`❌ Bulk processing job ${job.id} failed:`, error);
      throw error;
    }
  }

  async cleanup(job: Job): Promise<void> {
    // Cleanup logic if needed
    console.log(`🧹 Cleaning up bulk processing job ${job.id}`);
  }
}
