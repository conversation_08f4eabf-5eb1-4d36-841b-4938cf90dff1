'use client';

import React, { useState, useCallback } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { JobListTable } from './JobListTable';
import { JobFiltersPanel } from './JobFiltersPanel';
import { JobBulkActions } from './JobBulkActions';
import { Job, JobStatus, JobType } from '@/lib/jobs/types';

interface JobFilters {
  status?: JobStatus;
  type?: JobType;
  dateRange?: {
    start: Date;
    end: Date;
  };
  toolId?: string;
  search?: string;
}

interface JobMetrics {
  totalJobs: number;
  activeJobs: number;
  queuedJobs: number;
  completedToday: number;
  failedJobs: number;
  successRate: number;
  averageProcessingTime: number;
  queueHealth: 'healthy' | 'warning' | 'error';
}

interface JobMonitoringDashboardProps {
  jobs: Job[];
  metrics: JobMetrics;
  filters: JobFilters;
  onFilterChange: (filters: Partial<JobFilters>) => void;
  onClearFilters: () => void;
  onJobSelect: (job: Job) => void;
  onJobAction: (action: 'pause' | 'resume' | 'stop' | 'retry' | 'delete', jobId: string) => void;
  onBulkAction: (action: 'pause' | 'resume' | 'stop' | 'delete', jobIds: string[]) => void;
  isLoading?: boolean;
  isConnected?: boolean;
}

/**
 * Job Monitoring Dashboard Component
 * 
 * Main dashboard component that orchestrates job monitoring functionality.
 * Features:
 * - Real-time job list with filtering and search
 * - Bulk operations for multiple jobs
 * - Advanced filtering panel
 * - Connection status monitoring
 * - Responsive layout with proper spacing
 */
export function JobMonitoringDashboard({
  jobs,
  metrics,
  filters,
  onFilterChange,
  onClearFilters,
  onJobSelect,
  onJobAction,
  onBulkAction,
  isLoading = false,
  isConnected = false
}: JobMonitoringDashboardProps): React.JSX.Element {
  const [selectedJobIds, setSelectedJobIds] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');

  // Handle job selection
  const handleJobSelection = useCallback((jobId: string, selected: boolean) => {
    setSelectedJobIds(prev => {
      if (selected) {
        return [...prev, jobId];
      } else {
        return prev.filter(id => id !== jobId);
      }
    });
  }, []);

  // Handle select all
  const handleSelectAll = useCallback((selected: boolean) => {
    if (selected) {
      setSelectedJobIds(jobs.map(job => job.id));
    } else {
      setSelectedJobIds([]);
    }
  }, [jobs]);

  // Clear selection after bulk action
  const handleBulkAction = useCallback((
    action: 'pause' | 'resume' | 'stop' | 'delete',
    jobIds: string[]
  ) => {
    onBulkAction(action, jobIds);
    setSelectedJobIds([]);
  }, [onBulkAction]);

  // Filter active jobs count
  const filteredJobsCount = jobs.length;
  const hasActiveFilters = Object.keys(filters).some(key => 
    filters[key as keyof JobFilters] !== undefined && 
    filters[key as keyof JobFilters] !== ''
  );

  return (
    <div className="space-y-6">
      {/* Dashboard Header */}
      <Card className="bg-zinc-800 border border-zinc-700 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-semibold text-white">
              Job Queue ({filteredJobsCount} jobs)
            </h2>
            
            {/* Connection Status Indicator */}
            <div className="flex items-center space-x-2">
              <div 
                className={`w-2 h-2 rounded-full ${
                  isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'
                }`}
              />
              <span className="text-sm text-gray-400">
                {isConnected ? 'Live Updates' : 'Offline'}
              </span>
            </div>

            {/* Active Filters Indicator */}
            {hasActiveFilters && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-orange-400">
                  🔍 Filters Active
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onClearFilters}
                  className="text-xs"
                >
                  Clear
                </Button>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-3">
            {/* View Mode Toggle */}
            <div className="flex bg-zinc-700 rounded-lg p-1">
              <button
                onClick={() => setViewMode('table')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  viewMode === 'table'
                    ? 'bg-orange-500 text-white'
                    : 'text-gray-300 hover:text-white'
                }`}
              >
                📋 Table
              </button>
              <button
                onClick={() => setViewMode('cards')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  viewMode === 'cards'
                    ? 'bg-orange-500 text-white'
                    : 'text-gray-300 hover:text-white'
                }`}
              >
                🗃️ Cards
              </button>
            </div>

            {/* Filters Toggle */}
            <Button
              variant={showFilters ? 'primary' : 'secondary'}
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              🔍 Filters
            </Button>

            {/* Refresh Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.reload()}
              disabled={isLoading}
            >
              {isLoading ? '🔄' : '↻'} Refresh
            </Button>
          </div>
        </div>

        {/* Bulk Actions Bar */}
        {selectedJobIds.length > 0 && (
          <div className="mt-4 pt-4 border-t border-zinc-700">
            <JobBulkActions
              selectedCount={selectedJobIds.length}
              onBulkAction={handleBulkAction}
              selectedJobIds={selectedJobIds}
              onClearSelection={() => setSelectedJobIds([])}
            />
          </div>
        )}
      </Card>

      {/* Filters Panel */}
      {showFilters && (
        <JobFiltersPanel
          filters={filters}
          onFilterChange={onFilterChange}
          onClearFilters={onClearFilters}
          jobTypes={Object.values(JobType)}
          jobStatuses={Object.values(JobStatus)}
        />
      )}

      {/* Job List */}
      <Card className="bg-zinc-800 border border-zinc-700">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-gray-400">Loading jobs...</p>
          </div>
        ) : jobs.length === 0 ? (
          <div className="p-8 text-center">
            <div className="text-6xl mb-4">📭</div>
            <h3 className="text-xl font-semibold text-white mb-2">No Jobs Found</h3>
            <p className="text-gray-400 mb-4">
              {hasActiveFilters 
                ? 'No jobs match your current filters. Try adjusting your search criteria.'
                : 'No jobs have been created yet. Jobs will appear here when they are queued.'
              }
            </p>
            {hasActiveFilters && (
              <Button variant="outline" onClick={onClearFilters}>
                Clear Filters
              </Button>
            )}
          </div>
        ) : (
          <JobListTable
            jobs={jobs}
            selectedJobIds={selectedJobIds}
            onJobSelect={onJobSelect}
            onJobSelection={handleJobSelection}
            onSelectAll={handleSelectAll}
            onJobAction={onJobAction}
            viewMode={viewMode}
            isConnected={isConnected}
          />
        )}
      </Card>

      {/* Quick Stats Footer */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="bg-zinc-800 border border-zinc-700 p-4 text-center">
          <div className="text-2xl font-bold text-blue-400">{metrics.activeJobs}</div>
          <div className="text-sm text-gray-400">Active</div>
        </Card>
        <Card className="bg-zinc-800 border border-zinc-700 p-4 text-center">
          <div className="text-2xl font-bold text-yellow-400">{metrics.queuedJobs}</div>
          <div className="text-sm text-gray-400">Queued</div>
        </Card>
        <Card className="bg-zinc-800 border border-zinc-700 p-4 text-center">
          <div className="text-2xl font-bold text-green-400">{metrics.completedToday}</div>
          <div className="text-sm text-gray-400">Completed</div>
        </Card>
        <Card className="bg-zinc-800 border border-zinc-700 p-4 text-center">
          <div className="text-2xl font-bold text-red-400">{metrics.failedJobs}</div>
          <div className="text-sm text-gray-400">Failed</div>
        </Card>
      </div>
    </div>
  );
}

export default JobMonitoringDashboard;
