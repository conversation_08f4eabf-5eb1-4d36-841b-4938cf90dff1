export default function SubmissionSuccessPage() {
  return (
    <div className="min-h-screen bg-zinc-900 text-white flex items-center justify-center">
      <div className="max-w-2xl mx-auto px-4 text-center">
        <div className="bg-zinc-800 border border-green-500 rounded-lg p-8">
          <div className="text-6xl mb-6">🎉</div>
          <h1 className="text-3xl font-bold mb-4">Submission Successful!</h1>
          <p className="text-xl text-gray-300 mb-6">
            Thank you for submitting your AI tool. We've received your submission and will review it within 24-48 hours.
          </p>
          
          <div className="bg-zinc-700 rounded-lg p-6 mb-6">
            <h3 className="text-lg font-semibold mb-3">What happens next?</h3>
            <div className="space-y-3 text-left">
              <div className="flex items-start space-x-3">
                <span className="text-orange-500 font-bold">1.</span>
                <span>Our team reviews your submission for quality and accuracy</span>
              </div>
              <div className="flex items-start space-x-3">
                <span className="text-orange-500 font-bold">2.</span>
                <span>We may contact you if we need additional information</span>
              </div>
              <div className="flex items-start space-x-3">
                <span className="text-orange-500 font-bold">3.</span>
                <span>Once approved, your tool will be published on our directory</span>
              </div>
              <div className="flex items-start space-x-3">
                <span className="text-orange-500 font-bold">4.</span>
                <span>You'll receive an email confirmation when it goes live</span>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <a
              href="/"
              className="inline-block bg-orange-500 hover:bg-orange-600 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              Browse AI Tools
            </a>
            <div>
              <a
                href="/submit"
                className="text-orange-400 hover:text-orange-300 underline"
              >
                Submit Another Tool
              </a>
            </div>
          </div>

          <div className="mt-8 pt-6 border-t border-zinc-600">
            <p className="text-sm text-gray-400">
              Questions? Contact us at <a href="mailto:<EMAIL>" className="text-orange-400 hover:text-orange-300"><EMAIL></a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
