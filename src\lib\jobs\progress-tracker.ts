import { EventEmitter } from 'events';
import { createClient } from '@supabase/supabase-js';
import { Job, JobStatus, ProgressDetails, WebSocketJobUpdate } from './types';

/**
 * Progress Tracker for Enhanced Job Processing System
 * 
 * Provides real-time progress tracking with database persistence
 * and WebSocket broadcasting for live updates.
 */
export class ProgressTracker extends EventEmitter {
  private supabase;
  private progressCache = new Map<string, { progress: number; details?: ProgressDetails }>();

  constructor() {
    super();
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  /**
   * Update job progress with database persistence and real-time broadcasting
   */
  async updateProgress(
    jobId: string,
    progress: number,
    details?: ProgressDetails
  ): Promise<void> {
    try {
      // Validate progress value
      if (progress < 0 || progress > 100) {
        throw new Error(`Invalid progress value: ${progress}. Must be between 0 and 100.`);
      }

      // Update cache
      this.progressCache.set(jobId, { progress, details });

      // Update database
      await this.updateJobInDatabase(jobId, progress, details);

      // Emit local event
      this.emit('progress', { jobId, progress, details });

      // Broadcast via WebSocket
      await this.broadcastProgressUpdate(jobId, progress, details);

      console.log(`📊 Progress updated for job ${jobId}: ${progress}%${details?.message ? ` - ${details.message}` : ''}`);
    } catch (error) {
      console.error(`Failed to update progress for job ${jobId}:`, error);
      throw error;
    }
  }

  /**
   * Get current progress for a job
   */
  getProgress(jobId: string): { progress: number; details?: ProgressDetails } | null {
    return this.progressCache.get(jobId) || null;
  }

  /**
   * Subscribe to progress updates for a specific job
   */
  subscribeToJob(jobId: string, callback: (data: { progress: number; details?: ProgressDetails }) => void): () => void {
    const handler = (data: { jobId: string; progress: number; details?: ProgressDetails }) => {
      if (data.jobId === jobId) {
        callback({ progress: data.progress, details: data.details });
      }
    };

    this.on('progress', handler);

    return () => {
      this.off('progress', handler);
    };
  }

  /**
   * Subscribe to all progress updates
   */
  subscribeToAll(callback: (data: { jobId: string; progress: number; details?: ProgressDetails }) => void): () => void {
    this.on('progress', callback);

    return () => {
      this.off('progress', callback);
    };
  }

  /**
   * Update job status with progress tracking
   */
  async updateJobStatus(
    jobId: string,
    status: JobStatus,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    try {
      // Update database
      const updateData: any = {
        status,
        updated_at: new Date().toISOString(),
      };

      if (status === JobStatus.PROCESSING) {
        updateData.started_at = new Date().toISOString();
      } else if (status === JobStatus.COMPLETED || status === JobStatus.FAILED) {
        updateData.completed_at = new Date().toISOString();
      }

      await this.supabase
        .from('ai_generation_jobs')
        .update(updateData)
        .eq('id', jobId);

      // Broadcast status change
      await this.broadcastStatusChange(jobId, status, metadata);

      console.log(`📋 Status updated for job ${jobId}: ${status}`);
    } catch (error) {
      console.error(`Failed to update status for job ${jobId}:`, error);
      throw error;
    }
  }

  /**
   * Clear progress cache for completed/failed jobs
   */
  clearProgress(jobId: string): void {
    this.progressCache.delete(jobId);
  }

  /**
   * Get progress statistics for all jobs
   */
  getProgressStats(): {
    totalJobs: number;
    activeJobs: number;
    averageProgress: number;
  } {
    const entries = Array.from(this.progressCache.entries());
    const totalJobs = entries.length;
    const activeJobs = entries.filter(([, data]) => data.progress < 100).length;
    const averageProgress = totalJobs > 0 
      ? entries.reduce((sum, [, data]) => sum + data.progress, 0) / totalJobs 
      : 0;

    return {
      totalJobs,
      activeJobs,
      averageProgress: Math.round(averageProgress * 100) / 100,
    };
  }

  /**
   * Update job in database
   */
  private async updateJobInDatabase(
    jobId: string,
    progress: number,
    details?: ProgressDetails
  ): Promise<void> {
    const updateData: any = {
      progress,
      updated_at: new Date().toISOString(),
    };

    if (details) {
      // Store progress details in the progress_log field
      const { data: currentJob } = await this.supabase
        .from('ai_generation_jobs')
        .select('progress_log')
        .eq('id', jobId)
        .single();

      const progressLog = currentJob?.progress_log || [];
      progressLog.push({
        timestamp: new Date().toISOString(),
        progress,
        details,
      });

      updateData.progress_log = progressLog;
    }

    await this.supabase
      .from('ai_generation_jobs')
      .update(updateData)
      .eq('id', jobId);
  }

  /**
   * Broadcast progress update via WebSocket
   */
  private async broadcastProgressUpdate(
    jobId: string,
    progress: number,
    details?: ProgressDetails
  ): Promise<void> {
    const update: WebSocketJobUpdate = {
      jobId,
      type: 'progress_update',
      data: {
        progress,
        progressDetails: details,
        timestamp: new Date(),
      },
    };

    // Emit for WebSocket manager to pick up
    this.emit('websocket_broadcast', update);
  }

  /**
   * Broadcast status change via WebSocket
   */
  private async broadcastStatusChange(
    jobId: string,
    status: JobStatus,
    metadata?: Record<string, unknown>
  ): Promise<void> {
    const update: WebSocketJobUpdate = {
      jobId,
      type: 'status_change',
      data: {
        status,
        timestamp: new Date(),
        ...metadata,
      },
    };

    // Emit for WebSocket manager to pick up
    this.emit('websocket_broadcast', update);
  }

  /**
   * Initialize progress tracking for a job
   */
  async initializeJob(job: Job): Promise<void> {
    this.progressCache.set(job.id, { 
      progress: job.progress || 0, 
      details: job.progressDetails 
    });

    await this.updateJobStatus(job.id, job.status);
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.progressCache.clear();
    this.removeAllListeners();
  }
}

// Singleton instance
let progressTrackerInstance: ProgressTracker | null = null;

export function getProgressTracker(): ProgressTracker {
  if (!progressTrackerInstance) {
    progressTrackerInstance = new ProgressTracker();
  }
  return progressTrackerInstance;
}
