/**
 * Comprehensive Test Runner for Enhanced AI System
 *
 * Orchestrates all test suites for complete system validation:
 * - Integration tests
 * - Performance tests
 * - End-to-end workflow tests
 * - AI provider tests
 * - Migration rollback tests
 * - Generates comprehensive reports
 */

import { config } from 'dotenv';
import { join } from 'path';

// Load environment variables
config({ path: join(process.cwd(), '.env.local') });

import { EnhancedAISystemTester } from './integration/enhanced-ai-system.test';
import { LoadTester } from './performance/load-testing';
import { AdminWorkflowTester } from './e2e/admin-workflows.test';
import { AIProviderTester } from './api/ai-providers.test';
import { RollbackValidationTester } from './migration/rollback-validation.test';

interface TestSuiteResult {
  suiteName: string;
  status: 'passed' | 'failed' | 'partial';
  duration: number;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  successRate: number;
  startTime: Date;
  endTime: Date;
  error?: string;
}

interface ComprehensiveTestReport {
  overallStatus: 'passed' | 'failed' | 'partial';
  totalDuration: number;
  suiteResults: TestSuiteResult[];
  summary: {
    totalSuites: number;
    passedSuites: number;
    failedSuites: number;
    partialSuites: number;
    totalTests: number;
    totalPassed: number;
    totalFailed: number;
    overallSuccessRate: number;
  };
  recommendations: string[];
  timestamp: Date;
}

class ComprehensiveTestRunner {
  private results: TestSuiteResult[] = [];
  private startTime: Date = new Date();

  async runAllTests(): Promise<ComprehensiveTestReport> {
    console.log('🧪 ENHANCED AI SYSTEM - COMPREHENSIVE TEST SUITE');
    console.log('================================================');
    console.log(`Started: ${this.startTime.toISOString()}`);
    console.log('================================================\n');

    // Run all test suites
    await this.runTestSuite('Integration Tests', () => new EnhancedAISystemTester().runFullTestSuite());
    await this.runTestSuite('Performance Tests', () => new LoadTester().runLoadTests());
    await this.runTestSuite('End-to-End Workflows', () => new AdminWorkflowTester().runAllWorkflows());
    await this.runTestSuite('AI Provider Tests', () => new AIProviderTester().runAIProviderTests());
    await this.runTestSuite('Rollback Validation', () => new RollbackValidationTester().runRollbackValidationTests());

    // Generate comprehensive report
    const report = this.generateComprehensiveReport();
    this.printFinalReport(report);

    return report;
  }

  private async runTestSuite(suiteName: string, testFunction: () => Promise<void>): Promise<void> {
    console.log(`\n🔄 Starting ${suiteName}...`);
    console.log('='.repeat(50));

    const startTime = new Date();
    const result: TestSuiteResult = {
      suiteName,
      status: 'failed',
      duration: 0,
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      successRate: 0,
      startTime,
      endTime: new Date()
    };

    try {
      await testFunction();
      
      const endTime = new Date();
      result.endTime = endTime;
      result.duration = endTime.getTime() - startTime.getTime();
      result.status = 'passed';
      
      console.log(`\n✅ ${suiteName} completed successfully in ${result.duration}ms`);
    } catch (error: any) {
      const endTime = new Date();
      result.endTime = endTime;
      result.duration = endTime.getTime() - startTime.getTime();
      result.status = 'failed';
      result.error = error.message;
      
      console.log(`\n❌ ${suiteName} failed after ${result.duration}ms: ${error.message}`);
    }

    this.results.push(result);
    console.log('='.repeat(50));
  }

  private generateComprehensiveReport(): ComprehensiveTestReport {
    const endTime = new Date();
    const totalDuration = endTime.getTime() - this.startTime.getTime();

    const summary = {
      totalSuites: this.results.length,
      passedSuites: this.results.filter(r => r.status === 'passed').length,
      failedSuites: this.results.filter(r => r.status === 'failed').length,
      partialSuites: this.results.filter(r => r.status === 'partial').length,
      totalTests: this.results.reduce((sum, r) => sum + r.totalTests, 0),
      totalPassed: this.results.reduce((sum, r) => sum + r.passedTests, 0),
      totalFailed: this.results.reduce((sum, r) => sum + r.failedTests, 0),
      overallSuccessRate: 0
    };

    summary.overallSuccessRate = summary.totalTests > 0 ? 
      (summary.totalPassed / summary.totalTests) * 100 : 0;

    const overallStatus: 'passed' | 'failed' | 'partial' = 
      summary.failedSuites === 0 ? 'passed' :
      summary.passedSuites === 0 ? 'failed' : 'partial';

    const recommendations = this.generateRecommendations(summary);

    return {
      overallStatus,
      totalDuration,
      suiteResults: this.results,
      summary,
      recommendations,
      timestamp: endTime
    };
  }

  private generateRecommendations(summary: any): string[] {
    const recommendations: string[] = [];

    if (summary.overallSuccessRate < 70) {
      recommendations.push('🚨 CRITICAL: System has significant issues. Do not deploy to production.');
      recommendations.push('🔧 Review failed tests and resolve critical issues before proceeding.');
    } else if (summary.overallSuccessRate < 90) {
      recommendations.push('⚠️ WARNING: System has some issues that should be addressed.');
      recommendations.push('🔍 Review failed tests and consider fixing before production deployment.');
    } else if (summary.overallSuccessRate < 100) {
      recommendations.push('✅ GOOD: System is mostly functional with minor issues.');
      recommendations.push('🎯 Consider addressing remaining issues for optimal performance.');
    } else {
      recommendations.push('🎉 EXCELLENT: All tests passed! System is ready for production.');
      recommendations.push('🚀 Proceed with confidence to production deployment.');
    }

    // Specific recommendations based on failed suites
    const failedSuites = this.results.filter(r => r.status === 'failed');
    if (failedSuites.length > 0) {
      recommendations.push('\n📋 Failed Test Suites:');
      failedSuites.forEach(suite => {
        recommendations.push(`   • ${suite.suiteName}: ${suite.error || 'Unknown error'}`);
      });
    }

    // Performance recommendations
    const performanceSuite = this.results.find(r => r.suiteName === 'Performance Tests');
    if (performanceSuite && performanceSuite.status !== 'passed') {
      recommendations.push('\n⚡ Performance Issues Detected:');
      recommendations.push('   • Review API response times and database query performance');
      recommendations.push('   • Consider implementing caching strategies');
      recommendations.push('   • Monitor system resources under load');
    }

    // AI Provider recommendations
    const aiSuite = this.results.find(r => r.suiteName === 'AI Provider Tests');
    if (aiSuite && aiSuite.status !== 'passed') {
      recommendations.push('\n🤖 AI Provider Issues Detected:');
      recommendations.push('   • Verify API keys are correctly configured');
      recommendations.push('   • Check network connectivity to AI providers');
      recommendations.push('   • Review fallback mechanisms');
    }

    // Migration recommendations
    const migrationSuite = this.results.find(r => r.suiteName === 'Rollback Validation');
    if (migrationSuite && migrationSuite.status !== 'passed') {
      recommendations.push('\n🔄 Migration Issues Detected:');
      recommendations.push('   • Verify backup procedures are working correctly');
      recommendations.push('   • Test rollback procedures in staging environment');
      recommendations.push('   • Ensure data integrity checks pass');
    }

    return recommendations;
  }

  private printFinalReport(report: ComprehensiveTestReport): void {
    console.log('\n\n🏆 COMPREHENSIVE TEST REPORT');
    console.log('================================================');
    console.log(`Test Run Completed: ${report.timestamp.toISOString()}`);
    console.log(`Total Duration: ${report.totalDuration}ms (${(report.totalDuration / 1000).toFixed(2)}s)`);
    console.log(`Overall Status: ${this.getStatusIcon(report.overallStatus)} ${report.overallStatus.toUpperCase()}`);
    console.log('================================================\n');

    // Suite Results
    console.log('📊 TEST SUITE RESULTS:');
    console.log('-'.repeat(80));
    console.log('Suite Name'.padEnd(25) + 'Status'.padEnd(10) + 'Duration'.padEnd(12) + 'Success Rate');
    console.log('-'.repeat(80));

    report.suiteResults.forEach(suite => {
      const statusIcon = this.getStatusIcon(suite.status);
      const duration = `${suite.duration}ms`;
      const successRate = suite.totalTests > 0 ? 
        `${((suite.passedTests / suite.totalTests) * 100).toFixed(1)}%` : 'N/A';
      
      console.log(
        suite.suiteName.padEnd(25) + 
        `${statusIcon} ${suite.status}`.padEnd(10) + 
        duration.padEnd(12) + 
        successRate
      );
    });

    console.log('-'.repeat(80));

    // Summary Statistics
    console.log('\n📈 SUMMARY STATISTICS:');
    console.log(`   Test Suites: ${report.summary.totalSuites}`);
    console.log(`   Passed: ${report.summary.passedSuites} | Failed: ${report.summary.failedSuites} | Partial: ${report.summary.partialSuites}`);
    console.log(`   Overall Success Rate: ${report.summary.overallSuccessRate.toFixed(1)}%`);

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    report.recommendations.forEach(rec => {
      console.log(`   ${rec}`);
    });

    // Final Status
    console.log('\n' + '='.repeat(50));
    if (report.overallStatus === 'passed') {
      console.log('🎉 SYSTEM VALIDATION COMPLETE - READY FOR PRODUCTION');
    } else if (report.overallStatus === 'partial') {
      console.log('⚠️ SYSTEM VALIDATION PARTIAL - REVIEW ISSUES BEFORE PRODUCTION');
    } else {
      console.log('❌ SYSTEM VALIDATION FAILED - DO NOT DEPLOY TO PRODUCTION');
    }
    console.log('='.repeat(50));
  }

  private getStatusIcon(status: string): string {
    switch (status) {
      case 'passed': return '✅';
      case 'failed': return '❌';
      case 'partial': return '⚠️';
      default: return '❓';
    }
  }
}

// Environment validation
function validateEnvironment(): boolean {
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY',
    'ADMIN_API_KEY'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingVars.forEach(varName => {
      console.error(`   • ${varName}`);
    });
    console.error('\nPlease configure these variables before running tests.');
    return false;
  }

  return true;
}

// Main execution
async function main(): Promise<void> {
  console.log('🚀 Enhanced AI System - Comprehensive Test Suite');
  console.log('================================================\n');

  // Validate environment
  if (!validateEnvironment()) {
    process.exit(1);
  }

  // Check if server is running
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  try {
    const response = await fetch(`${baseUrl}/api/health`);
    if (!response.ok) {
      console.warn('⚠️ Health endpoint not responding, but continuing with tests...\n');
    }
  } catch (error) {
    console.warn('⚠️ Server may not be running, but continuing with tests...\n');
  }

  // Run comprehensive tests
  const runner = new ComprehensiveTestRunner();
  const report = await runner.runAllTests();

  // Exit with appropriate code
  process.exit(report.overallStatus === 'passed' ? 0 : 1);
}

// Export for use in other files
export { ComprehensiveTestRunner, ComprehensiveTestReport, TestSuiteResult };

// Main execution when run directly
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}
