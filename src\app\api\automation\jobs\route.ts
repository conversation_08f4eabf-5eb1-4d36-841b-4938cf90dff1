import { NextRequest, NextResponse } from 'next/server';
import { getJobQueue } from '@/lib/jobs/queue';
import { JobType, JobPriority } from '@/lib/jobs/types';
import { validateA<PERSON><PERSON>ey } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { type, data, options = {} } = await request.json();

    if (!type || !data) {
      return NextResponse.json(
        { success: false, error: 'Job type and data are required' },
        { status: 400 }
      );
    }

    // Validate job type
    if (!Object.values(JobType).includes(type)) {
      return NextResponse.json(
        { success: false, error: 'Invalid job type' },
        { status: 400 }
      );
    }

    const queue = getJobQueue();
    const job = await queue.add(type, data, {
      priority: options.priority || JobPriority.NORMAL,
      delay: options.delay,
      maxAttempts: options.maxAttempts,
      scheduledFor: options.scheduledFor ? new Date(options.scheduledFor) : undefined,
    });

    return NextResponse.json({
      success: true,
      job: {
        id: job.id,
        type: job.type,
        status: job.status,
        priority: job.priority,
        createdAt: job.createdAt,
        scheduledFor: job.scheduledFor,
      },
    });
  } catch (error) {
    console.error('Error creating job:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create job' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    const queue = getJobQueue();
    let jobs = await queue.getJobs(status as any);

    // Apply pagination
    const totalJobs = jobs.length;
    jobs = jobs.slice(offset, offset + limit);

    return NextResponse.json({
      success: true,
      data: {
        jobs: jobs.map(job => ({
          id: job.id,
          type: job.type,
          status: job.status,
          priority: job.priority,
          attempts: job.attempts,
          maxAttempts: job.maxAttempts,
          createdAt: job.createdAt,
          updatedAt: job.updatedAt,
          scheduledFor: job.scheduledFor,
          completedAt: job.completedAt,
          error: job.error,
          data: job.data,
          result: job.result,
          progress: job.progress,
          progressDetails: job.progressDetails,
          toolId: job.toolId,
        })),
        pagination: {
          total: totalJobs,
          limit,
          offset,
          hasMore: offset + limit < totalJobs,
        },
      },
    });
  } catch (error) {
    console.error('Error fetching jobs:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch jobs' },
      { status: 500 }
    );
  }
}
