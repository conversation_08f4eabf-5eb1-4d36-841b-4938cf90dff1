/**
 * Check migration status by verifying table existence
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing required Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkMigrationStatus() {
  console.log('🔍 Checking Enhanced AI System Migration Status...');
  console.log('=' .repeat(60));
  
  const expectedTables = [
    'ai_generation_jobs',
    'media_assets', 
    'editorial_reviews',
    'bulk_processing_jobs',
    'system_configuration'
  ];
  
  const expectedColumns = [
    'scraped_data',
    'ai_generation_status',
    'last_scraped_at',
    'editorial_review_id',
    'ai_generation_job_id',
    'submission_type',
    'submission_source',
    'content_quality_score',
    'last_ai_update'
  ];
  
  let allTablesExist = true;
  let allColumnsExist = true;
  
  // Check new tables
  console.log('\n📋 Checking new tables:');
  for (const tableName of expectedTables) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`❌ Table ${tableName}: NOT FOUND`);
        allTablesExist = false;
      } else {
        console.log(`✅ Table ${tableName}: EXISTS`);
      }
    } catch (err) {
      console.log(`❌ Table ${tableName}: ERROR - ${err}`);
      allTablesExist = false;
    }
  }
  
  // Check new columns in tools table
  console.log('\n📋 Checking new columns in tools table:');
  try {
    const { data: tools, error } = await supabase
      .from('tools')
      .select('*')
      .limit(1);
    
    if (error) {
      console.log('❌ Could not access tools table');
      allColumnsExist = false;
    } else if (tools && tools.length > 0) {
      const tool = tools[0];
      for (const columnName of expectedColumns) {
        if (columnName in tool) {
          console.log(`✅ Column tools.${columnName}: EXISTS`);
        } else {
          console.log(`❌ Column tools.${columnName}: NOT FOUND`);
          allColumnsExist = false;
        }
      }
    } else {
      console.log('⚠️  Tools table is empty, cannot verify column existence');
    }
  } catch (err) {
    console.log(`❌ Error checking tools table: ${err}`);
    allColumnsExist = false;
  }
  
  // Check system configuration
  console.log('\n📋 Checking system configuration:');
  try {
    const { data: configs, error } = await supabase
      .from('system_configuration')
      .select('config_key')
      .limit(10);
    
    if (error) {
      console.log('❌ Could not access system_configuration table');
    } else {
      console.log(`✅ System configuration: ${configs?.length || 0} entries found`);
      if (configs && configs.length > 0) {
        configs.forEach(config => {
          console.log(`   • ${config.config_key}`);
        });
      }
    }
  } catch (err) {
    console.log(`❌ Error checking system configuration: ${err}`);
  }
  
  // Summary
  console.log('\n' + '=' .repeat(60));
  console.log('📊 MIGRATION STATUS SUMMARY:');
  console.log('=' .repeat(60));
  
  if (allTablesExist && allColumnsExist) {
    console.log('🎉 ✅ MIGRATION COMPLETED SUCCESSFULLY!');
    console.log('   All new tables and columns are present.');
    console.log('   Enhanced AI System database schema is ready.');
  } else {
    console.log('⚠️  ❌ MIGRATION INCOMPLETE:');
    if (!allTablesExist) {
      console.log('   • Some new tables are missing');
    }
    if (!allColumnsExist) {
      console.log('   • Some new columns are missing from tools table');
    }
    console.log('\n💡 To complete the migration:');
    console.log('   1. Run: npm run db:show-sql');
    console.log('   2. Copy the SQL output');
    console.log('   3. Execute it in your Supabase SQL Editor');
    console.log('   4. Run this check again: npm run db:status');
  }
  
  console.log('');
}

// Run the check
checkMigrationStatus().catch(console.error);
