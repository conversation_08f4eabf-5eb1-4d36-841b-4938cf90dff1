// Configuration Management Types and Interfaces

export type ConfigurationSource = 'environment' | 'database' | 'admin' | 'default';
export type ConfigurationLevel = 'system' | 'user' | 'session';
export type EnvironmentType = 'development' | 'staging' | 'production';

// Core Environment Configuration
export interface EnvironmentConfiguration {
  // Core System
  NODE_ENV: EnvironmentType;
  PORT: number;
  SITE_URL: string;
  
  // Database
  SUPABASE_URL: string;
  SUPABASE_ANON_KEY: string;
  SUPABASE_SERVICE_ROLE_KEY: string;
  DATABASE_URL: string;
  
  // External APIs
  SCRAPE_DO_API_KEY: string;
  SCRAPE_DO_BASE_URL: string;
  OPENAI_API_KEY: string;
  OPENAI_ORGANIZATION?: string;
  OPENROUTER_API_KEY: string;
  OPENROUTER_BASE_URL: string;
  
  // Job Processing
  JOB_QUEUE_REDIS_URL?: string;
  JOB_CONCURRENCY: number;
  JOB_TIMEOUT: number;
  JOB_RETRY_ATTEMPTS: number;
  
  // Security
  ADMIN_API_KEY: string;
  JWT_SECRET: string;
  ENCRYPTION_KEY: string;
  
  // Email
  SMTP_HOST?: string;
  SMTP_PORT?: number;
  SMTP_USER?: string;
  SMTP_PASS?: string;
  
  // Analytics
  GOOGLE_ANALYTICS_ID?: string;
  
  // Feature Flags
  CONTENT_GENERATION_ENABLED: boolean;
  JOB_QUEUE_ENABLED: boolean;
  DEBUG_MODE: boolean;
  MAINTENANCE_MODE: boolean;
}

// Admin Panel Configuration (stored in database)
export interface AdminPanelConfiguration {
  aiGeneration: {
    providers: {
      openai: {
        enabled: boolean;
        model: string;
        maxTokens: number;
        temperature: number;
        timeout: number;
        priority: number;
      };
      openrouter: {
        enabled: boolean;
        model: string;
        maxTokens: number;
        temperature: number;
        implicitCaching: boolean;
        timeout: number;
        priority: number;
      };
    };
    
    modelSelection: {
      strategy: 'auto' | 'manual' | 'cost_optimized' | 'quality_optimized';
      fallbackOrder: string[];
      costThreshold: number;
      qualityThreshold: number;
    };
    
    contentGeneration: {
      autoApproval: boolean;
      qualityThreshold: number;
      editorialReviewRequired: boolean;
      maxRetries: number;
      timeoutSeconds: number;
    };
  };
  
  scraping: {
    scrapeDoConfig: {
      timeout: number;
      retryAttempts: number;
      costOptimization: {
        enabled: boolean;
        targetSavingsPercentage: number;
        creditThreshold: number;
        maxCreditsPerTool: number;
      };
      multiPageScraping: {
        enabled: boolean;
        maxPages: number;
        pageTypes: string[];
      };
    };
    
    mediaExtraction: {
      ogImageEnabled: boolean;
      faviconEnabled: boolean;
      screenshotFallback: boolean;
      imageQuality: number;
      maxImageSize: number;
    };
  };
  
  jobProcessing: {
    maxConcurrentJobs: number;
    defaultRetryAttempts: number;
    batchSize: number;
    processingDelay: number;
    priorityQueues: boolean;
    
    monitoring: {
      realTimeUpdates: boolean;
      progressTracking: boolean;
      errorNotifications: boolean;
      performanceMetrics: boolean;
    };
  };
  
  system: {
    contentQualityThreshold: number;
    autoApprovalEnabled: boolean;
    debugMode: boolean;
    maintenanceMode: boolean;
    
    security: {
      apiKeyRotationDays: number;
      sessionTimeoutMinutes: number;
      maxLoginAttempts: number;
      auditLogging: boolean;
    };
    
    performance: {
      cacheEnabled: boolean;
      cacheTTL: number;
      rateLimiting: boolean;
      requestsPerMinute: number;
    };
  };
  
  editorial: {
    workflow: {
      autoAssignment: boolean;
      reviewTimeoutHours: number;
      escalationEnabled: boolean;
      qualityChecks: boolean;
    };
    
    contentStandards: {
      minDescriptionLength: number;
      maxDescriptionLength: number;
      requiredFields: string[];
      bannedWords: string[];
    };
  };
}

// Merged System Configuration
export interface SystemConfiguration extends EnvironmentConfiguration, AdminPanelConfiguration {
  _metadata: {
    lastUpdated: string;
    version: string;
    source: ConfigurationSource;
    environment: EnvironmentType;
  };
}

// Configuration Validation
export interface ValidationRule {
  path: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  enum?: string[];
  validator?: (value: unknown) => boolean;
  message?: string;
}

export interface ValidationError {
  path: string;
  message: string;
  value: unknown;
  rule: ValidationRule;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: string[];
  score: number; // 0-100
}

// Configuration Change Tracking
export interface ConfigurationChange {
  id: string;
  path: string;
  oldValue: unknown;
  newValue: unknown;
  source: ConfigurationSource;
  userId?: string;
  timestamp: string;
  reason?: string;
  rollbackData?: { path: string; value: unknown };
}

// Configuration Access Control
export interface ConfigurationPermission {
  path: string;
  read: boolean;
  write: boolean;
  roles: string[];
}

// Configuration Manager Interface
export interface ConfigurationManagerInterface {
  // Core methods
  get<T>(path: string): T;
  set(path: string, value: unknown, source?: ConfigurationSource): Promise<void>;
  has(path: string): boolean;
  delete(path: string): Promise<void>;

  // Validation
  validate(config?: Partial<SystemConfiguration>): Promise<ValidationResult>;
  validatePath(path: string, value: unknown): Promise<ValidationResult>;

  // Configuration management
  reload(): Promise<void>;
  export(): SystemConfiguration;
  import(config: Partial<SystemConfiguration>): Promise<ValidationResult>;

  // Change tracking
  getChanges(since?: string): ConfigurationChange[];
  rollback(changeId: string): Promise<void>;

  // Provider testing
  testProvider(provider: 'openai' | 'openrouter' | 'scrape-do'): Promise<boolean>;
  testAllProviders(): Promise<Record<string, boolean>>;

  // Events
  on(event: 'change' | 'error' | 'reload', callback: (...args: unknown[]) => void): void;
  off(event: string, callback: (...args: unknown[]) => void): void;
}

// Configuration Schema
export interface ConfigurationSchema {
  environment: ValidationRule[];
  adminPanel: ValidationRule[];
  system: ValidationRule[];
}

// Encryption Configuration
export interface EncryptionConfig {
  algorithm: string;
  keyDerivation: string;
  iterations: number;
  saltLength: number;
  ivLength: number;
}

// Configuration Cache
export interface ConfigurationCache {
  data: Map<string, unknown>;
  timestamps: Map<string, number>;
  ttl: number;
  maxSize: number;
}

// Configuration Export/Import
export interface ConfigurationExport {
  version: string;
  timestamp: string;
  environment: EnvironmentType;
  configuration: Partial<SystemConfiguration>;
  metadata: {
    exportedBy?: string;
    reason?: string;
    includeSecrets: boolean;
  };
}

// Default configurations
export const DEFAULT_ENVIRONMENT_CONFIG: Partial<EnvironmentConfiguration> = {
  NODE_ENV: 'development',
  PORT: 3000,
  SITE_URL: 'http://localhost:3000',
  JOB_CONCURRENCY: 3,
  JOB_TIMEOUT: 300000,
  JOB_RETRY_ATTEMPTS: 3,
  CONTENT_GENERATION_ENABLED: true,
  JOB_QUEUE_ENABLED: true,
  DEBUG_MODE: false,
  MAINTENANCE_MODE: false,
  SCRAPE_DO_BASE_URL: 'https://api.scrape.do',
  OPENROUTER_BASE_URL: 'https://openrouter.ai/api/v1'
};

export const DEFAULT_ADMIN_CONFIG: AdminPanelConfiguration = {
  aiGeneration: {
    providers: {
      openai: {
        enabled: true,
        model: 'gpt-4o-2024-11-20',
        maxTokens: 16384,
        temperature: 0.7,
        timeout: 60000,
        priority: 1
      },
      openrouter: {
        enabled: true,
        model: 'google/gemini-2.5-pro-preview',
        maxTokens: 65536,
        temperature: 0.7,
        implicitCaching: true,
        timeout: 120000,
        priority: 2
      }
    },
    modelSelection: {
      strategy: 'auto',
      fallbackOrder: ['openai', 'openrouter'],
      costThreshold: 0.01,
      qualityThreshold: 0.8
    },
    contentGeneration: {
      autoApproval: false,
      qualityThreshold: 0.8,
      editorialReviewRequired: true,
      maxRetries: 3,
      timeoutSeconds: 300
    }
  },
  scraping: {
    scrapeDoConfig: {
      timeout: 30000,
      retryAttempts: 3,
      costOptimization: {
        enabled: true,
        targetSavingsPercentage: 60,
        creditThreshold: 100,
        maxCreditsPerTool: 50
      },
      multiPageScraping: {
        enabled: false,
        maxPages: 5,
        pageTypes: ['pricing', 'faq', 'features']
      }
    },
    mediaExtraction: {
      ogImageEnabled: true,
      faviconEnabled: true,
      screenshotFallback: true,
      imageQuality: 80,
      maxImageSize: 2048
    }
  },
  jobProcessing: {
    maxConcurrentJobs: 3,
    defaultRetryAttempts: 3,
    batchSize: 10,
    processingDelay: 1000,
    priorityQueues: true,
    monitoring: {
      realTimeUpdates: true,
      progressTracking: true,
      errorNotifications: true,
      performanceMetrics: true
    }
  },
  system: {
    contentQualityThreshold: 0.8,
    autoApprovalEnabled: false,
    debugMode: false,
    maintenanceMode: false,
    security: {
      apiKeyRotationDays: 90,
      sessionTimeoutMinutes: 60,
      maxLoginAttempts: 5,
      auditLogging: true
    },
    performance: {
      cacheEnabled: true,
      cacheTTL: 3600,
      rateLimiting: true,
      requestsPerMinute: 100
    }
  },
  editorial: {
    workflow: {
      autoAssignment: false,
      reviewTimeoutHours: 24,
      escalationEnabled: true,
      qualityChecks: true
    },
    contentStandards: {
      minDescriptionLength: 50,
      maxDescriptionLength: 500,
      requiredFields: ['name', 'description', 'category_id', 'website_url'],
      bannedWords: []
    }
  }
};
