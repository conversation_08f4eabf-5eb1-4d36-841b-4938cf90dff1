'use client';

import React from 'react';
import { ThumbsUp, ThumbsDown } from 'lucide-react';
import { AITool } from '@/lib/types';

interface ToolProsAndConsProps {
  prosAndCons: NonNullable<AITool['prosAndCons']>;
}

export function ToolProsAndCons({ prosAndCons }: ToolProsAndConsProps) {
  // Safety check for undefined or malformed data
  if (!prosAndCons || !prosAndCons.pros || !prosAndCons.cons ||
      !Array.isArray(prosAndCons.pros) || !Array.isArray(prosAndCons.cons)) {
    return null;
  }

  return (
    <section className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
      <h2 className="text-2xl font-bold text-white mb-6">Pros & Cons</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        
        {/* Pros Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <ThumbsUp size={20} className="text-green-400" />
            <h3 className="text-lg font-semibold text-green-400">Pros</h3>
          </div>
          
          <div className="space-y-3">
            {prosAndCons.pros.map((pro, index) => (
              <div
                key={index}
                className="flex items-start gap-3 p-3 rounded-lg bg-green-500/10 border border-green-500/20"
              >
                <div className="flex-shrink-0 mt-0.5">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                </div>
                <p className="text-gray-300 text-sm leading-relaxed">
                  {pro}
                </p>
              </div>
            ))}
          </div>
        </div>
        
        {/* Cons Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <ThumbsDown size={20} className="text-red-400" />
            <h3 className="text-lg font-semibold text-red-400">Cons</h3>
          </div>
          
          <div className="space-y-3">
            {prosAndCons.cons.map((con, index) => (
              <div
                key={index}
                className="flex items-start gap-3 p-3 rounded-lg bg-red-500/10 border border-red-500/20"
              >
                <div className="flex-shrink-0 mt-0.5">
                  <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                </div>
                <p className="text-gray-300 text-sm leading-relaxed">
                  {con}
                </p>
              </div>
            ))}
          </div>
        </div>
        
      </div>
      
      {/* Summary Stats */}
      <div className="mt-6 pt-6 border-t border-zinc-600">
        <div className="grid grid-cols-2 gap-4 text-center">
          <div className="p-3">
            <div className="text-2xl font-bold text-green-400 mb-1">
              {prosAndCons.pros.length}
            </div>
            <div className="text-gray-400 text-xs">Advantages</div>
          </div>
          <div className="p-3">
            <div className="text-2xl font-bold text-red-400 mb-1">
              {prosAndCons.cons.length}
            </div>
            <div className="text-gray-400 text-xs">Disadvantages</div>
          </div>
        </div>
      </div>
    </section>
  );
}
