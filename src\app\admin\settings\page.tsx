'use client';

import { useState, useEffect } from 'react';
import { ConfigurationPanel } from '@/components/admin/configuration/ConfigurationPanel';

interface ConfigurationSummary {
  environment: string;
  providersEnabled: string[];
  featuresEnabled: string[];
  lastUpdated: string;
  validationScore: number;
  healthStatus: 'healthy' | 'warning' | 'error';
}

interface ValidationResult {
  isValid: boolean;
  errors: Array<{
    path: string;
    message: string;
    value: unknown;
  }>;
  warnings: string[];
  score: number;
}

export default function AdminSettingsPage() {
  const [summary, setSummary] = useState<ConfigurationSummary | null>(null);
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadConfigurationData();
  }, []);

  const loadConfigurationData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load configuration summary
      const summaryResponse = await fetch('/api/admin/config?section=summary', {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (!summaryResponse.ok) {
        throw new Error('Failed to load configuration summary');
      }

      const summaryData = await summaryResponse.json();
      setSummary(summaryData.data);

      // Load validation status
      const validationResponse = await fetch('/api/admin/config/validate', {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (!validationResponse.ok) {
        throw new Error('Failed to load validation status');
      }

      const validationData = await validationResponse.json();
      setValidation(validationData.validation);

    } catch (error) {
      console.error('Failed to load configuration data:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-400';
      case 'warning':
        return 'text-yellow-400';
      case 'error':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return '✓';
      case 'warning':
        return '⚠';
      case 'error':
        return '✗';
      default:
        return '?';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading configuration...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-xl mb-4">Configuration Error</div>
          <div className="text-gray-300 mb-4">{error}</div>
          <button
            onClick={loadConfigurationData}
            className="bg-orange-500 hover:bg-orange-600 px-4 py-2 rounded-lg font-medium text-white"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-zinc-900 text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold">System Configuration</h1>
            <p className="text-gray-400 mt-2">
              Manage AI providers, scraping settings, and system configuration
            </p>
          </div>
          <button
            onClick={loadConfigurationData}
            className="bg-zinc-700 hover:bg-zinc-600 px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Refresh
          </button>
        </div>

        {/* Configuration Summary Cards */}
        {summary && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
              <h3 className="text-lg font-semibold mb-2">Environment</h3>
              <p className="text-2xl font-bold text-blue-400 capitalize">
                {summary.environment}
              </p>
            </div>

            <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
              <h3 className="text-lg font-semibold mb-2">Health Status</h3>
              <p className={`text-2xl font-bold ${getHealthStatusColor(summary.healthStatus)}`}>
                {getHealthStatusIcon(summary.healthStatus)} {summary.healthStatus}
              </p>
            </div>

            <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
              <h3 className="text-lg font-semibold mb-2">AI Providers</h3>
              <p className="text-2xl font-bold text-green-400">
                {summary.providersEnabled.length}
              </p>
              <p className="text-sm text-gray-400 mt-1">
                {summary.providersEnabled.join(', ') || 'None enabled'}
              </p>
            </div>

            <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
              <h3 className="text-lg font-semibold mb-2">Validation Score</h3>
              <p className={`text-2xl font-bold ${
                summary.validationScore >= 90 ? 'text-green-400' :
                summary.validationScore >= 70 ? 'text-yellow-400' : 'text-red-400'
              }`}>
                {summary.validationScore}%
              </p>
            </div>
          </div>
        )}

        {/* Validation Issues */}
        {validation && !validation.isValid && (
          <div className="bg-red-900/20 border border-red-700 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-red-400 mb-4">
              Configuration Issues ({validation.errors.length})
            </h3>
            <div className="space-y-2">
              {validation.errors.map((error, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <span className="text-red-400 mt-1">•</span>
                  <div>
                    <span className="font-medium text-red-300">{error.path}:</span>
                    <span className="text-red-200 ml-2">{error.message}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Validation Warnings */}
        {validation && validation.warnings.length > 0 && (
          <div className="bg-yellow-900/20 border border-yellow-700 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-yellow-400 mb-4">
              Configuration Warnings ({validation.warnings.length})
            </h3>
            <div className="space-y-2">
              {validation.warnings.map((warning, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <span className="text-yellow-400 mt-1">•</span>
                  <span className="text-yellow-200">{warning}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Features Status */}
        {summary && (
          <div className="bg-zinc-800 rounded-lg border border-zinc-700 p-6 mb-8">
            <h3 className="text-lg font-semibold mb-4">Enabled Features</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {summary.featuresEnabled.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <span className="text-green-400">✓</span>
                  <span className="text-gray-300">{feature}</span>
                </div>
              ))}
              {summary.featuresEnabled.length === 0 && (
                <div className="col-span-full text-gray-400 text-center py-4">
                  No features currently enabled
                </div>
              )}
            </div>
          </div>
        )}

        {/* Configuration Panel */}
        <ConfigurationPanel onConfigurationChange={loadConfigurationData} />

        {/* Last Updated */}
        {summary && (
          <div className="text-center text-gray-400 text-sm mt-8">
            Last updated: {new Date(summary.lastUpdated).toLocaleString()}
          </div>
        )}
      </div>
    </div>
  );
}
