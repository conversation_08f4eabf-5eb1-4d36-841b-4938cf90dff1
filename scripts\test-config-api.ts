#!/usr/bin/env tsx

/**
 * Test Configuration API
 * 
 * This script tests the configuration API endpoints to verify they work correctly
 * after the database schema and authentication fixes.
 */

async function testConfigAPI() {
  console.log('🧪 Testing Configuration API');
  console.log('============================================================');

  const baseUrl = 'http://localhost:3002';
  const adminApiKey = 'aidude_admin_2024_secure_key_xyz789';

  try {
    // Test 1: Configuration Summary
    console.log('📋 Test 1: Configuration Summary');
    const summaryResponse = await fetch(`${baseUrl}/api/admin/config?section=summary`, {
      headers: {
        'x-admin-api-key': adminApiKey,
        'Content-Type': 'application/json'
      }
    });

    console.log(`   Status: ${summaryResponse.status} ${summaryResponse.statusText}`);
    
    if (summaryResponse.ok) {
      const summaryData = await summaryResponse.json();
      console.log('   ✅ Summary data received:');
      console.log(`      Environment: ${summaryData.data?.environment || 'N/A'}`);
      console.log(`      Providers: ${summaryData.data?.providersEnabled?.join(', ') || 'None'}`);
      console.log(`      Features: ${summaryData.data?.featuresEnabled?.join(', ') || 'None'}`);
    } else {
      const errorData = await summaryResponse.text();
      console.log(`   ❌ Error: ${errorData}`);
    }

    // Test 2: Configuration Validation
    console.log('\n📋 Test 2: Configuration Validation');
    const validationResponse = await fetch(`${baseUrl}/api/admin/config/validate`, {
      headers: {
        'x-admin-api-key': adminApiKey,
        'Content-Type': 'application/json'
      }
    });

    console.log(`   Status: ${validationResponse.status} ${validationResponse.statusText}`);
    
    if (validationResponse.ok) {
      const validationData = await validationResponse.json();
      console.log('   ✅ Validation data received:');
      console.log(`      Valid: ${validationData.validation?.isValid || false}`);
      console.log(`      Score: ${validationData.validation?.score || 0}%`);
      console.log(`      Errors: ${validationData.validation?.errors?.length || 0}`);
    } else {
      const errorData = await validationResponse.text();
      console.log(`   ❌ Error: ${errorData}`);
    }

    // Test 3: AI Providers Configuration
    console.log('\n📋 Test 3: AI Providers Configuration');
    const aiProvidersResponse = await fetch(`${baseUrl}/api/admin/config?section=ai-providers`, {
      headers: {
        'x-admin-api-key': adminApiKey,
        'Content-Type': 'application/json'
      }
    });

    console.log(`   Status: ${aiProvidersResponse.status} ${aiProvidersResponse.statusText}`);
    
    if (aiProvidersResponse.ok) {
      const aiProvidersData = await aiProvidersResponse.json();
      console.log('   ✅ AI Providers data received:');
      console.log(`      Section: ${aiProvidersData.section}`);
      console.log(`      Data keys: ${Object.keys(aiProvidersData.data || {}).join(', ')}`);
    } else {
      const errorData = await aiProvidersResponse.text();
      console.log(`   ❌ Error: ${errorData}`);
    }

    // Test 4: System Configuration
    console.log('\n📋 Test 4: System Configuration');
    const systemResponse = await fetch(`${baseUrl}/api/admin/config?section=system`, {
      headers: {
        'x-admin-api-key': adminApiKey,
        'Content-Type': 'application/json'
      }
    });

    console.log(`   Status: ${systemResponse.status} ${systemResponse.statusText}`);
    
    if (systemResponse.ok) {
      const systemData = await systemResponse.json();
      console.log('   ✅ System data received:');
      console.log(`      Section: ${systemData.section}`);
      console.log(`      Data keys: ${Object.keys(systemData.data || {}).join(', ')}`);
    } else {
      const errorData = await systemResponse.text();
      console.log(`   ❌ Error: ${errorData}`);
    }

    console.log('\n============================================================');
    console.log('🎉 Configuration API testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testConfigAPI().catch(console.error);
