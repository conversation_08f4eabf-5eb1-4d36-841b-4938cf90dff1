// Configuration Validation System

import {
  ValidationRule,
  ValidationError,
  ValidationResult,
  SystemConfiguration
} from './types';

export class ConfigurationValidator {
  private rules: ValidationRule[] = [];

  constructor() {
    this.initializeValidationRules();
  }

  private initializeValidationRules(): void {
    this.rules = [
      // Environment Configuration Rules
      {
        path: 'NODE_ENV',
        type: 'string',
        required: true,
        enum: ['development', 'staging', 'production'],
        message: 'NODE_ENV must be development, staging, or production'
      },
      {
        path: 'PORT',
        type: 'number',
        required: true,
        min: 1,
        max: 65535,
        message: 'PORT must be between 1 and 65535'
      },
      {
        path: 'SITE_URL',
        type: 'string',
        required: true,
        pattern: /^https?:\/\/.+/,
        message: 'SITE_URL must be a valid HTTP/HTTPS URL'
      },

      // Database Configuration
      {
        path: 'SUPABASE_URL',
        type: 'string',
        required: true,
        pattern: /^https:\/\/.+\.supabase\.co$/,
        message: 'SUPABASE_URL must be a valid Supabase URL'
      },
      {
        path: 'SUPABASE_ANON_KEY',
        type: 'string',
        required: true,
        min: 100,
        message: 'SUPABASE_ANON_KEY must be a valid JWT token'
      },
      {
        path: 'SUPABASE_SERVICE_ROLE_KEY',
        type: 'string',
        required: true,
        min: 100,
        message: 'SUPABASE_SERVICE_ROLE_KEY must be a valid JWT token'
      },

      // API Keys
      {
        path: 'OPENAI_API_KEY',
        type: 'string',
        required: true,
        pattern: /^sk-/,
        message: 'OPENAI_API_KEY must start with "sk-"'
      },
      {
        path: 'OPENROUTER_API_KEY',
        type: 'string',
        required: true,
        pattern: /^sk-or-/,
        message: 'OPENROUTER_API_KEY must start with "sk-or-"'
      },
      {
        path: 'SCRAPE_DO_API_KEY',
        type: 'string',
        required: true,
        min: 10,
        message: 'SCRAPE_DO_API_KEY must be at least 10 characters'
      },

      // Security
      {
        path: 'JWT_SECRET',
        type: 'string',
        required: true,
        min: 32,
        message: 'JWT_SECRET must be at least 32 characters for security'
      },
      {
        path: 'ADMIN_API_KEY',
        type: 'string',
        required: true,
        min: 16,
        message: 'ADMIN_API_KEY must be at least 16 characters'
      },

      // Job Processing
      {
        path: 'JOB_CONCURRENCY',
        type: 'number',
        required: true,
        min: 1,
        max: 20,
        message: 'JOB_CONCURRENCY must be between 1 and 20'
      },
      {
        path: 'JOB_TIMEOUT',
        type: 'number',
        required: true,
        min: 1000,
        max: 600000,
        message: 'JOB_TIMEOUT must be between 1000ms and 600000ms (10 minutes)'
      },

      // AI Generation Configuration
      {
        path: 'aiGeneration.providers.openai.maxTokens',
        type: 'number',
        required: true,
        min: 1,
        max: 16384,
        message: 'OpenAI maxTokens must be between 1 and 16384'
      },
      {
        path: 'aiGeneration.providers.openai.temperature',
        type: 'number',
        required: true,
        min: 0,
        max: 2,
        message: 'OpenAI temperature must be between 0 and 2'
      },
      {
        path: 'aiGeneration.providers.openrouter.maxTokens',
        type: 'number',
        required: true,
        min: 1,
        max: 65536,
        message: 'OpenRouter maxTokens must be between 1 and 65536'
      },
      {
        path: 'aiGeneration.providers.openrouter.temperature',
        type: 'number',
        required: true,
        min: 0,
        max: 2,
        message: 'OpenRouter temperature must be between 0 and 2'
      },

      // Scraping Configuration
      {
        path: 'scraping.scrapeDoConfig.timeout',
        type: 'number',
        required: true,
        min: 5000,
        max: 120000,
        message: 'Scraping timeout must be between 5000ms and 120000ms'
      },
      {
        path: 'scraping.scrapeDoConfig.retryAttempts',
        type: 'number',
        required: true,
        min: 0,
        max: 10,
        message: 'Retry attempts must be between 0 and 10'
      },
      {
        path: 'scraping.scrapeDoConfig.costOptimization.targetSavingsPercentage',
        type: 'number',
        required: true,
        min: 0,
        max: 90,
        message: 'Target savings percentage must be between 0 and 90'
      },

      // System Configuration
      {
        path: 'system.contentQualityThreshold',
        type: 'number',
        required: true,
        min: 0,
        max: 1,
        message: 'Content quality threshold must be between 0 and 1'
      },
      {
        path: 'system.security.sessionTimeoutMinutes',
        type: 'number',
        required: true,
        min: 5,
        max: 1440,
        message: 'Session timeout must be between 5 minutes and 24 hours'
      },
      {
        path: 'system.performance.requestsPerMinute',
        type: 'number',
        required: true,
        min: 1,
        max: 10000,
        message: 'Requests per minute must be between 1 and 10000'
      }
    ];
  }

  /**
   * Validate complete configuration
   */
  public validate(config: Partial<SystemConfiguration>): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: string[] = [];

    for (const rule of this.rules) {
      const value = this.getNestedValue(config, rule.path);
      const error = this.validateValue(rule, value);
      
      if (error) {
        errors.push(error);
      }
    }

    // Additional cross-field validations
    this.validateCrossFieldRules(config, errors, warnings);

    // Calculate score based on errors and warnings
    const totalRules = this.rules.length;
    const errorWeight = 1;
    const warningWeight = 0.1;
    const score = Math.max(0, 100 - (errors.length * errorWeight + warnings.length * warningWeight) * (100 / totalRules));

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: Math.round(score)
    };
  }

  /**
   * Validate a specific configuration path
   */
  public validatePath(path: string, value: unknown): ValidationResult {
    const rule = this.rules.find(r => r.path === path);
    if (!rule) {
      return {
        isValid: true,
        errors: [],
        warnings: [`No validation rule found for path: ${path}`],
        score: 100
      };
    }

    const error = this.validateValue(rule, value);
    return {
      isValid: !error,
      errors: error ? [error] : [],
      warnings: [],
      score: error ? 0 : 100
    };
  }

  /**
   * Validate individual value against rule
   */
  private validateValue(rule: ValidationRule, value: unknown): ValidationError | null {
    // Check if required
    if (rule.required && (value === undefined || value === null || value === '')) {
      return {
        path: rule.path,
        message: rule.message || `${rule.path} is required`,
        value,
        rule
      };
    }

    // Skip validation if value is empty and not required
    if (!rule.required && (value === undefined || value === null || value === '')) {
      return null;
    }

    // Type validation
    if (!this.validateType(value, rule.type)) {
      return {
        path: rule.path,
        message: rule.message || `${rule.path} must be of type ${rule.type}`,
        value,
        rule
      };
    }

    // String validations
    if (rule.type === 'string' && typeof value === 'string') {
      if (rule.min && value.length < rule.min) {
        return {
          path: rule.path,
          message: rule.message || `${rule.path} must be at least ${rule.min} characters`,
          value,
          rule
        };
      }

      if (rule.max && value.length > rule.max) {
        return {
          path: rule.path,
          message: rule.message || `${rule.path} must be at most ${rule.max} characters`,
          value,
          rule
        };
      }

      if (rule.pattern && !rule.pattern.test(value)) {
        return {
          path: rule.path,
          message: rule.message || `${rule.path} does not match required pattern`,
          value,
          rule
        };
      }

      if (rule.enum && !rule.enum.includes(value)) {
        return {
          path: rule.path,
          message: rule.message || `${rule.path} must be one of: ${rule.enum.join(', ')}`,
          value,
          rule
        };
      }
    }

    // Number validations
    if (rule.type === 'number' && typeof value === 'number') {
      if (rule.min && value < rule.min) {
        return {
          path: rule.path,
          message: rule.message || `${rule.path} must be at least ${rule.min}`,
          value,
          rule
        };
      }

      if (rule.max && value > rule.max) {
        return {
          path: rule.path,
          message: rule.message || `${rule.path} must be at most ${rule.max}`,
          value,
          rule
        };
      }
    }

    // Custom validator
    if (rule.validator && !rule.validator(value)) {
      return {
        path: rule.path,
        message: rule.message || `${rule.path} failed custom validation`,
        value,
        rule
      };
    }

    return null;
  }

  /**
   * Validate cross-field rules
   */
  private validateCrossFieldRules(
    config: Partial<SystemConfiguration>, 
    errors: ValidationError[], 
    warnings: string[]
  ): void {
    // Check if both AI providers are disabled
    const openaiEnabled = this.getNestedValue(config, 'aiGeneration.providers.openai.enabled');
    const openrouterEnabled = this.getNestedValue(config, 'aiGeneration.providers.openrouter.enabled');
    
    if (openaiEnabled === false && openrouterEnabled === false) {
      warnings.push('Both AI providers are disabled - content generation will not work');
    }

    // Check if cost optimization is too aggressive
    const costOptimization = this.getNestedValue(config, 'scraping.scrapeDoConfig.costOptimization.enabled');
    const targetSavings = this.getNestedValue(config, 'scraping.scrapeDoConfig.costOptimization.targetSavingsPercentage');
    
    if (costOptimization && typeof targetSavings === 'number' && targetSavings > 80) {
      warnings.push('Very aggressive cost optimization may impact scraping quality');
    }

    // Check if job concurrency is too high for available resources
    const concurrency = this.getNestedValue(config, 'JOB_CONCURRENCY') || this.getNestedValue(config, 'jobProcessing.maxConcurrentJobs');
    if (typeof concurrency === 'number' && concurrency > 10) {
      warnings.push('High job concurrency may impact system performance');
    }

    // Check if quality threshold is too low
    const qualityThreshold = this.getNestedValue(config, 'system.contentQualityThreshold');
    if (typeof qualityThreshold === 'number' && qualityThreshold < 0.5) {
      warnings.push('Low content quality threshold may allow poor quality content');
    }

    // Check if auto-approval is enabled without editorial review
    const autoApproval = this.getNestedValue(config, 'aiGeneration.contentGeneration.autoApproval');
    const editorialReview = this.getNestedValue(config, 'aiGeneration.contentGeneration.editorialReviewRequired');
    
    if (autoApproval && !editorialReview) {
      warnings.push('Auto-approval without editorial review may compromise content quality');
    }
  }

  /**
   * Validate type
   */
  private validateType(value: unknown, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: Record<string, unknown>, path: string): unknown {
    return path.split('.').reduce((current: any, key: string) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Add custom validation rule
   */
  public addRule(rule: ValidationRule): void {
    this.rules.push(rule);
  }

  /**
   * Remove validation rule
   */
  public removeRule(path: string): void {
    this.rules = this.rules.filter(rule => rule.path !== path);
  }

  /**
   * Get all validation rules
   */
  public getRules(): ValidationRule[] {
    return [...this.rules];
  }

  /**
   * Validate provider-specific configuration
   */
  public async validateProviderConfig(provider: 'openai' | 'openrouter' | 'scrape-do', config: Record<string, unknown>): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: string[] = [];

    try {
      switch (provider) {
        case 'openai':
          await this.validateOpenAIConfig(config, errors, warnings);
          break;
        case 'openrouter':
          await this.validateOpenRouterConfig(config, errors, warnings);
          break;
        case 'scrape-do':
          await this.validateScrapeDoConfig(config, errors, warnings);
          break;
      }
    } catch (error) {
      errors.push({
        path: `${provider}.validation`,
        message: `Provider validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        value: config,
        rule: { path: `${provider}.validation`, type: 'object', required: true }
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      score: errors.length === 0 ? 100 : 0
    };
  }

  private async validateOpenAIConfig(config: Record<string, unknown>, errors: ValidationError[], warnings: string[]): Promise<void> {
    if (!config.apiKey) {
      errors.push({
        path: 'openai.apiKey',
        message: 'OpenAI API key is required',
        value: config.apiKey,
        rule: { path: 'openai.apiKey', type: 'string', required: true }
      });
    }

    if (typeof config.maxTokens === 'number' && config.maxTokens > 16384) {
      warnings.push('OpenAI maxTokens exceeds recommended limit of 16384');
    }
  }

  private async validateOpenRouterConfig(config: Record<string, unknown>, errors: ValidationError[], warnings: string[]): Promise<void> {
    if (!config.apiKey) {
      errors.push({
        path: 'openrouter.apiKey',
        message: 'OpenRouter API key is required',
        value: config.apiKey,
        rule: { path: 'openrouter.apiKey', type: 'string', required: true }
      });
    }

    if (typeof config.maxTokens === 'number' && config.maxTokens > 65536) {
      warnings.push('OpenRouter maxTokens exceeds recommended limit of 65536');
    }
  }

  private async validateScrapeDoConfig(config: Record<string, unknown>, errors: ValidationError[], warnings: string[]): Promise<void> {
    if (!config.apiKey) {
      errors.push({
        path: 'scrape-do.apiKey',
        message: 'Scrape.do API key is required',
        value: config.apiKey,
        rule: { path: 'scrape-do.apiKey', type: 'string', required: true }
      });
    }

    if (typeof config.timeout === 'number' && config.timeout < 5000) {
      warnings.push('Very low timeout may cause scraping failures');
    }
  }
}
