'use client';

import { useState, useEffect } from 'react';

export function useScrollPosition() {
  const [isScrollToTopVisible, setIsScrollToTopVisible] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isAboveFold, setIsAboveFold] = useState(true);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = Math.min((scrollTop / documentHeight) * 100, 100);

      setIsScrollToTopVisible(scrollTop > 300);
      setScrollProgress(progress);
      setIsAboveFold(scrollTop < window.innerHeight);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Call once to set initial state
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  const scrollToBottom = () => {
    window.scrollTo({
      top: document.documentElement.scrollHeight,
      behavior: 'smooth',
    });
  };

  return {
    isScrollToTopVisible,
    scrollProgress,
    isAboveFold,
    scrollToTop,
    scrollToBottom,
  };
}
