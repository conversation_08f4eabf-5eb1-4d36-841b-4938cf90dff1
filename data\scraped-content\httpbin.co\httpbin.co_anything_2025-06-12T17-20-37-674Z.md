---
url: https://httpbin.co/anything
scraped_at: 2025-06-12T17:20:37.667Z
stored_at: 2025-06-12T17:20:37.674Z
credits_used: 5
optimization_strategy: Datacenter + Browser
content_scenario: content_sufficient
content_quality: 100%
---

# Main Content

- Meta: content: light dark, name: color-scheme
- Meta: charset: utf-8

```
{
  "args": {},
  "headers": {
    "Accept": [
      "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"
    ],
    "Accept-Encoding": [
      "gzip, br"
    ],
    "Accept-Language": [
      "en-US,en;q=0.9"
    ],
    "Cdn-Loop": [
      "cloudflare; loops=1"
    ],
    "Cf-Connecting-Ip": [
      "************"
    ],
    "Cf-Ipcountry": [
      "DE"
    ],
    "Cf-Ray": [
      "94eb050d7ddec808-DUS"
    ],
    "Cf-Visitor": [
      "{\"scheme\":\"https\"}"
    ],
    "Host": [
      "httpbin.co"
    ],
    "Priority": [
      "u=0, i"
    ],
    "Sec-Ch-Ua": [
      "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""
    ],
    "Sec-Ch-Ua-Mobile": [
      "?0"
    ],
    "Sec-Ch-Ua-Platform": [
      "\"Windows\""
    ],
    "Sec-Fetch-Dest": [
      "document"
    ],
    "Sec-Fetch-Mode": [
      "navigate"
    ],
    "Sec-Fetch-Site": [
      "none"
    ],
    "Sec-Fetch-User": [
      "?1"
    ],
    "Upgrade-Insecure-Requests": [
      "1"
    ],
    "User-Agent": [
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ],
    "X-Forwarded-For": [
      "**************"
    ],
    "X-Forwarded-Host": [
      "httpbin.co"
    ],
    "X-Forwarded-Port": [
      "443"
    ],
    "X-Forwarded-Proto": [
      "https"
    ],
    "X-Forwarded-Server": [
      "4f02d29398d3"
    ],
    "X-Real-Ip": [
      "**************"
    ]
  },
  "method": "GET",
  "origin": "************",
  "url": "https://httpbin.co/anything",
  "data": "",
  "files": {},
  "form": {},
  "json": null
}

```