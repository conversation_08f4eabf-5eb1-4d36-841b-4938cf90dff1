'use client';

import { useState, useEffect } from 'react';
import { EditorialDashboard } from '@/components/admin/EditorialDashboard';
import { SubmissionReviewModal } from '@/components/admin/SubmissionReviewModal';

interface SubmissionItem {
  id: string;
  name: string;
  url: string;
  description: string;
  category: string;
  subcategory?: string;
  submitterName: string;
  submitterEmail: string;
  status: 'pending' | 'under_review' | 'approved' | 'rejected' | 'published';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  reviewNotes?: string;
  priority: 'high' | 'normal' | 'low';
  pricingType?: string;
}

interface ReviewFormData {
  decision: 'approve' | 'reject' | 'needs_revision';
  reviewNotes: string;
  priority?: 'high' | 'normal' | 'low';
  featuredDate?: string;
  editorialText?: string;
  qualityScore?: number;
}

export default function EditorialDashboardPage() {
  const [selectedSubmission, setSelectedSubmission] = useState<SubmissionItem | null>(null);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleSubmissionSelect = (submission: SubmissionItem) => {
    setSelectedSubmission(submission);
    setIsReviewModalOpen(true);
  };

  const handleCloseReviewModal = () => {
    setIsReviewModalOpen(false);
    setSelectedSubmission(null);
  };

  const handleSubmitReview = async (submissionId: string, reviewData: ReviewFormData) => {
    try {
      const response = await fetch('/api/admin/editorial/review', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': 'admin-dashboard-access'
        },
        body: JSON.stringify({
          submissionId,
          decision: reviewData.decision,
          reviewNotes: reviewData.reviewNotes,
          priority: reviewData.priority,
          featuredDate: reviewData.featuredDate,
          editorialText: reviewData.editorialText,
          qualityScore: reviewData.qualityScore,
          reviewerId: 'admin' // In a real app, this would come from the authenticated user
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit review');
      }

      const result = await response.json();
      console.log('Review submitted successfully:', result);

      // Trigger refresh of the dashboard data
      setRefreshTrigger(prev => prev + 1);
      
      return result;
    } catch (error) {
      console.error('Failed to submit review:', error);
      throw error;
    }
  };

  return (
    <div className="min-h-screen bg-zinc-900">
      {/* Enhanced Editorial Dashboard with submission selection */}
      <EditorialDashboardWithSelection 
        onSubmissionSelect={handleSubmissionSelect}
        refreshTrigger={refreshTrigger}
      />

      {/* Review Modal */}
      {selectedSubmission && (
        <SubmissionReviewModal
          submission={selectedSubmission}
          isOpen={isReviewModalOpen}
          onClose={handleCloseReviewModal}
          onSubmitReview={handleSubmitReview}
        />
      )}
    </div>
  );
}

// Enhanced Editorial Dashboard component with submission selection capability
interface EditorialDashboardWithSelectionProps {
  onSubmissionSelect: (submission: SubmissionItem) => void;
  refreshTrigger: number;
}

function EditorialDashboardWithSelection({ 
  onSubmissionSelect, 
  refreshTrigger 
}: EditorialDashboardWithSelectionProps) {
  const [submissions, setSubmissions] = useState<SubmissionItem[]>([]);
  const [stats, setStats] = useState({
    totalSubmissions: 0,
    pendingReview: 0,
    underReview: 0,
    approvedToday: 0,
    rejectedToday: 0,
    averageReviewTime: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadEditorialData();
  }, [refreshTrigger]);

  const loadEditorialData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [submissionsResponse, statsResponse] = await Promise.all([
        fetch('/api/admin/editorial/submissions', {
          headers: {
            'x-admin-api-key': 'admin-dashboard-access'
          }
        }),
        fetch('/api/admin/editorial/stats', {
          headers: {
            'x-admin-api-key': 'admin-dashboard-access'
          }
        })
      ]);

      if (!submissionsResponse.ok || !statsResponse.ok) {
        throw new Error('Failed to load editorial data');
      }

      const submissionsData = await submissionsResponse.json();
      const statsData = await statsResponse.json();

      setSubmissions(submissionsData.submissions || []);
      setStats(statsData.stats || stats);
    } catch (err) {
      console.error('Failed to load editorial data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load data');
      
      // Mock data for development
      setSubmissions([
        {
          id: '1',
          name: 'AI Writing Assistant',
          url: 'https://example.com',
          description: 'An advanced AI tool for content creation and writing assistance.',
          category: 'writing-tools',
          submitterName: 'John Doe',
          submitterEmail: '<EMAIL>',
          status: 'pending',
          submittedAt: '2024-01-15T10:30:00Z',
          priority: 'normal'
        },
        {
          id: '2',
          name: 'Image Generator Pro',
          url: 'https://imagegen.com',
          description: 'Professional AI image generation with advanced customization options.',
          category: 'image-generators',
          submitterName: 'Jane Smith',
          submitterEmail: '<EMAIL>',
          status: 'under_review',
          submittedAt: '2024-01-14T15:45:00Z',
          reviewedBy: 'admin',
          priority: 'high'
        }
      ]);
      
      setStats({
        totalSubmissions: 156,
        pendingReview: 23,
        underReview: 8,
        approvedToday: 12,
        rejectedToday: 3,
        averageReviewTime: 2.5
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredSubmissions = submissions.filter(submission => {
    const matchesStatus = selectedStatus === 'all' || submission.status === selectedStatus;
    const matchesSearch = searchTerm === '' || 
      submission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      submission.submitterName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      submission.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesStatus && matchesSearch;
  });

  // Enhanced dashboard component that includes submission selection
  return (
    <div className="min-h-screen bg-zinc-900 text-white font-roboto">
      <div className="container mx-auto px-4 py-8" style={{ maxWidth: 'var(--container-width)' }}>
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Editorial Dashboard</h1>
            <p className="text-gray-300">Manage user submissions and editorial workflow</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <button
              onClick={loadEditorialData}
              className="px-4 py-2 bg-zinc-700 hover:bg-zinc-600 text-white rounded-lg transition-colors"
            >
              Refresh
            </button>
            <a
              href="/admin"
              className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
            >
              ← Back to Admin
            </a>
          </div>
        </div>

        {error && (
          <div className="bg-red-900/20 border border-red-500 rounded-lg p-4 mb-6">
            <p className="text-red-400">{error}</p>
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
              <p className="text-gray-300">Loading editorial dashboard...</p>
            </div>
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-8">
              <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Total Submissions</p>
                    <p className="text-2xl font-bold text-white">{stats.totalSubmissions}</p>
                  </div>
                </div>
              </div>

              <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Pending Review</p>
                    <p className="text-2xl font-bold text-yellow-400">{stats.pendingReview}</p>
                  </div>
                </div>
              </div>

              <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Under Review</p>
                    <p className="text-2xl font-bold text-blue-400">{stats.underReview}</p>
                  </div>
                </div>
              </div>

              <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Approved Today</p>
                    <p className="text-2xl font-bold text-green-400">{stats.approvedToday}</p>
                  </div>
                </div>
              </div>

              <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Rejected Today</p>
                    <p className="text-2xl font-bold text-red-400">{stats.rejectedToday}</p>
                  </div>
                </div>
              </div>

              <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Avg Review Time</p>
                    <p className="text-2xl font-bold text-white">{stats.averageReviewTime}h</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Filters and Search */}
            <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6 mb-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Search submissions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-4 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>
                
                <div className="flex items-center gap-4">
                  <select
                    value={selectedStatus}
                    onChange={(e) => setSelectedStatus(e.target.value)}
                    className="px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="all">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="under_review">Under Review</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                    <option value="published">Published</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Submissions Table */}
            <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-white">Submissions ({filteredSubmissions.length})</h2>
              </div>

              {filteredSubmissions.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-lg mb-2">No submissions found</div>
                  <p className="text-gray-500">Try adjusting your filters or search terms</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredSubmissions.map((submission) => (
                    <div 
                      key={submission.id} 
                      className="bg-zinc-700 border border-zinc-600 rounded-lg p-4 hover:bg-zinc-600 transition-colors cursor-pointer"
                      onClick={() => onSubmissionSelect(submission)}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-white">{submission.name}</h3>
                          <p className="text-gray-300 text-sm mt-1">{submission.description}</p>
                          <div className="flex items-center gap-4 mt-2 text-sm text-gray-400">
                            <span>By: {submission.submitterName}</span>
                            <span>Category: {submission.category}</span>
                            <span>Priority: {submission.priority}</span>
                          </div>
                        </div>
                        <div className="text-right">
                          <span className={`px-3 py-1 rounded-full text-sm ${
                            submission.status === 'pending' ? 'bg-yellow-400/10 text-yellow-400' :
                            submission.status === 'under_review' ? 'bg-blue-400/10 text-blue-400' :
                            submission.status === 'approved' ? 'bg-green-400/10 text-green-400' :
                            submission.status === 'rejected' ? 'bg-red-400/10 text-red-400' :
                            'bg-gray-400/10 text-gray-400'
                          }`}>
                            {submission.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </span>
                          <div className="text-sm text-gray-400 mt-1">
                            {new Date(submission.submittedAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
