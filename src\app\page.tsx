import React from 'react';
import { CategoryGrid } from '@/components/features/CategoryGrid';
import { Tooltip } from '@/components/features/Tooltip';
import { AIDudeStory } from '@/components/features/AIDudeStory';
import { SearchProvider } from '@/providers/SearchProvider';
import { getCategoriesWithTools } from '@/lib/supabase';
import { HomePageClient } from '@/components/features/HomePageClient';

export default async function Home() {
  // Fetch categories with their tools from database
  const categories = await getCategoriesWithTools(10); // Limit to 10 tools per category for homepage

  // Filter categories to show only first 12 for homepage
  const displayCategories = categories.slice(0, 12);

  return (
    <SearchProvider>
      <HomePageClient categories={displayCategories} />
    </SearchProvider>
  );
}
