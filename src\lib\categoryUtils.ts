import { AICategory, AITool, ToolFilters, PaginationInfo } from './types';
import { getCategories, getToolById, getCategoryWithTools, getTools } from './supabase';

/**
 * Find category by ID or slug (database version)
 */
export async function findCategoryBySlug(slug: string): Promise<AICategory | null> {
  try {
    const categories = await getCategories();
    return categories.find(category =>
      category.id === slug ||
      category.title.toLowerCase().replace(/\s+/g, '-') === slug.toLowerCase()
    ) || null;
  } catch (error) {
    console.error('Error finding category by slug:', error);
    return null;
  }
}

/**
 * Find tool by ID (database version)
 */
export async function findToolById(toolId: string): Promise<AITool | null> {
  try {
    return await getToolById(toolId);
  } catch (error) {
    console.error('Error finding tool by ID:', error);
    return null;
  }
}

/**
 * Get all unique subcategories for a given category (database version)
 */
export async function getSubcategoriesForCategory(categoryId: string): Promise<string[]> {
  try {
    const { tools } = await getCategoryWithTools(categoryId);

    const subcategories = tools
      .map(tool => tool.subcategory)
      .filter((subcategory): subcategory is string => Boolean(subcategory))
      .filter((subcategory, index, array) => array.indexOf(subcategory) === index);

    return subcategories.sort();
  } catch (error) {
    console.error('Error getting subcategories for category:', error);
    return [];
  }
}

/**
 * Get tools for a category, optionally filtered by subcategory (database version)
 */
export async function getToolsForCategory(
  categoryId: string,
  subcategory?: string
): Promise<AITool[]> {
  try {
    const { tools } = await getCategoryWithTools(categoryId);

    if (subcategory) {
      return tools.filter(tool =>
        tool.subcategory?.toLowerCase() === subcategory.toLowerCase()
      );
    }

    return tools;
  } catch (error) {
    console.error('Error getting tools for category:', error);
    return [];
  }
}

/**
 * Filter tools based on provided filters
 */
export function filterTools(tools: AITool[], filters: ToolFilters): AITool[] {
  let filteredTools = [...tools];
  
  // Search filter
  if (filters.search && filters.search.trim()) {
    const searchTerm = filters.search.toLowerCase();
    filteredTools = filteredTools.filter(tool =>
      tool.name.toLowerCase().includes(searchTerm) ||
      tool.description.toLowerCase().includes(searchTerm) ||
      tool.detailedDescription?.toLowerCase().includes(searchTerm) ||
      tool.features?.some(feature => feature.toLowerCase().includes(searchTerm))
    );
  }
  
  // Tags filter
  if (filters.tags && filters.tags.length > 0) {
    filteredTools = filteredTools.filter(tool =>
      tool.tags?.some(tag => filters.tags!.includes(tag.type))
    );
  }
  
  // Pricing filter
  if (filters.pricing) {
    filteredTools = filteredTools.filter(tool =>
      tool.pricing?.type === filters.pricing
    );
  }
  
  // Verified filter
  if (filters.verified !== undefined) {
    filteredTools = filteredTools.filter(tool =>
      Boolean(tool.isVerified) === filters.verified
    );
  }
  
  return filteredTools;
}

/**
 * Sort tools based on sort criteria
 */
export function sortTools(tools: AITool[], sortBy: string, sortOrder: 'asc' | 'desc' = 'asc'): AITool[] {
  const sortedTools = [...tools];
  
  sortedTools.sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'rating':
        const ratingA = a.reviews?.rating || 0;
        const ratingB = b.reviews?.rating || 0;
        comparison = ratingA - ratingB;
        break;
      case 'newest':
        // Prioritize tools with New tag, then by name
        const isNewA = a.tags?.some(tag => tag.type === 'New') ? 1 : 0;
        const isNewB = b.tags?.some(tag => tag.type === 'New') ? 1 : 0;
        comparison = isNewB - isNewA || a.name.localeCompare(b.name);
        break;
      case 'popular':
        // Prioritize tools with Trending tag, then by rating, then by name
        const isTrendingA = a.tags?.some(tag => tag.type === 'Trending') ? 1 : 0;
        const isTrendingB = b.tags?.some(tag => tag.type === 'Trending') ? 1 : 0;
        const popularityA = isTrendingA + (a.reviews?.rating || 0) / 10;
        const popularityB = isTrendingB + (b.reviews?.rating || 0) / 10;
        comparison = popularityB - popularityA || a.name.localeCompare(b.name);
        break;
      default:
        comparison = a.name.localeCompare(b.name);
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });
  
  return sortedTools;
}

/**
 * Paginate tools array
 */
export function paginateTools(
  tools: AITool[], 
  page: number, 
  itemsPerPage: number = 24
): { tools: AITool[]; pagination: PaginationInfo } {
  const totalItems = tools.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const currentPage = Math.max(1, Math.min(page, totalPages));
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  
  return {
    tools: tools.slice(startIndex, endIndex),
    pagination: {
      currentPage,
      totalPages,
      totalItems,
      itemsPerPage,
    },
  };
}

/**
 * Generate breadcrumb data for category pages (database version)
 */
export async function generateBreadcrumbs(categoryId: string, subcategory?: string) {
  try {
    const category = await findCategoryBySlug(categoryId);
    if (!category) return [];

    const breadcrumbs = [
      { label: 'Home', href: '/' },
      { label: category.title, href: `/category/${category.id}` },
    ];

    if (subcategory) {
      breadcrumbs.push({
        label: subcategory,
        href: `/category/${category.id}/${subcategory.toLowerCase().replace(/\s+/g, '-')}`,
      });
    }

    return breadcrumbs;
  } catch (error) {
    console.error('Error generating breadcrumbs:', error);
    return [];
  }
}

/**
 * Generate SEO-friendly slug from category/subcategory name
 */
export function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

/**
 * Get category display name from slug (database version)
 */
export async function getCategoryDisplayName(slug: string): Promise<string> {
  try {
    const category = await findCategoryBySlug(slug);
    return category?.title || slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  } catch (error) {
    console.error('Error getting category display name:', error);
    return slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }
}
