'use client';

import React from 'react';
import { Tags as TagsIcon } from 'lucide-react';
import { AITool } from '@/lib/types';
import { Tag } from '@/components/ui/Tag';

interface ToolTagsCardProps {
  tool: AITool;
}

export function ToolTagsCard({ tool }: ToolTagsCardProps) {
  // Don't render if no tags
  if (!tool.tags || tool.tags.length === 0) {
    return null;
  }

  return (
    <section className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
      <div className="flex items-center gap-3 mb-4">
        <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
          <TagsIcon size={16} className="text-white" />
        </div>
        <h3 className="text-xl font-bold text-white">Tags</h3>
      </div>

      <div className="flex flex-wrap gap-2">
        {tool.tags.map((tag, index) => (
          <Tag
            key={`${tool.id}-tag-${index}`}
            type={tag.type}
            label={tag.label}
            className="transition-transform duration-200 hover:scale-105"
          />
        ))}
      </div>

      <div className="mt-4 pt-4 border-t border-zinc-600">
        <p className="text-gray-400 text-sm">
          Tags help categorize and identify key features of {tool.name}. 
          Use these tags to find similar tools or understand the tool's primary focus areas.
        </p>
      </div>
    </section>
  );
}
