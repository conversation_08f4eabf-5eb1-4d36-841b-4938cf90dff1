/**
 * Test script for dual AI provider system
 * Tests OpenAI and OpenRouter integration with fallback mechanisms
 */

import { AIContentGenerator, performHealthCheck, AIUtils } from './index';

async function testDualProviderSystem() {
  console.log('🚀 Starting Dual AI Provider System Test\n');

  // Test 1: Configuration Validation
  console.log('1️⃣ Testing Configuration Validation...');
  const configValidation = AIUtils.validateConfiguration();
  console.log('Configuration Status:', configValidation.valid ? '✅ Valid' : '❌ Invalid');
  if (!configValidation.valid) {
    console.log('Issues:', configValidation.issues);
  }
  console.log();

  // Test 2: Provider Health Check
  console.log('2️⃣ Testing Provider Health Check...');
  try {
    const healthCheck = await performHealthCheck();
    console.log('System Status:', healthCheck.status);
    console.log('OpenAI Status:', healthCheck.providers.openai.status);
    console.log('OpenRouter Status:', healthCheck.providers.openrouter.status);
    if (healthCheck.providers.openai.latency) {
      console.log('OpenAI Latency:', `${healthCheck.providers.openai.latency}ms`);
    }
    if (healthCheck.providers.openrouter.latency) {
      console.log('OpenRouter Latency:', `${healthCheck.providers.openrouter.latency}ms`);
    }
  } catch (error) {
    console.error('Health check failed:', error);
  }
  console.log();

  // Test 3: Model Selection
  console.log('3️⃣ Testing Model Selection...');
  const { ModelSelector } = await import('./model-selector');
  
  const testCases = [
    { contentSize: 5000, complexity: 'simple' as const, priority: 'speed' as const, features: [] },
    { contentSize: 50000, complexity: 'medium' as const, priority: 'quality' as const, features: ['caching'] },
    { contentSize: 200000, complexity: 'complex' as const, priority: 'quality' as const, features: ['multimodal'] },
    { contentSize: 10000, complexity: 'medium' as const, priority: 'cost' as const, scrapingCost: 5, features: [] }
  ];

  testCases.forEach((testCase, index) => {
    const selectedModel = ModelSelector.selectOptimalModel(testCase);
    console.log(`Test Case ${index + 1}:`, {
      input: testCase,
      selected: `${selectedModel.provider}/${selectedModel.model}`,
      reasoning: selectedModel.reasoning
    });
  });
  console.log();

  // Test 4: Content Generation with Small Content
  console.log('4️⃣ Testing Content Generation (Small Content)...');
  const smallTestContent = `
# Test AI Tool

**URL:** https://example-ai-tool.com

**Title:** Example AI Tool

**Description:** A powerful AI tool for testing purposes

**Main Content:**
This is a test AI tool designed to demonstrate content generation capabilities.
It includes various features like text processing, data analysis, and automation.
The tool is designed for developers and businesses looking to integrate AI into their workflows.

**Features:**
- Text processing and analysis
- Data visualization
- API integration
- Real-time processing
- Custom model training

**Pricing Information:**
Free tier: Basic features
Pro tier: $29/month - Advanced features
Enterprise: Custom pricing
`;

  try {
    const generator = new AIContentGenerator();
    const result = await generator.generateContent(
      smallTestContent,
      'https://example-ai-tool.com',
      {
        complexity: 'simple',
        priority: 'speed',
        maxRetries: 2
      }
    );

    if (result.success) {
      console.log('✅ Content generation successful');
      console.log('Provider used:', result.modelUsed?.provider);
      console.log('Model used:', result.modelUsed?.model);
      console.log('Validation score:', result.validation?.score);
      console.log('Token usage:', result.tokenUsage);
      console.log('Strategy used:', result.strategyUsed);
      
      // Show sample of generated content
      if (result.content?.detailed_description) {
        console.log('Sample description:', result.content.detailed_description.substring(0, 100) + '...');
      }
      if (result.content?.features) {
        console.log('Generated features count:', result.content.features.length);
      }
    } else {
      console.log('❌ Content generation failed:', result.error);
    }
  } catch (error) {
    console.error('❌ Content generation test failed:', error);
  }
  console.log();

  // Test 5: Provider Fallback
  console.log('5️⃣ Testing Provider Fallback...');
  try {
    // Test with a configuration that might trigger fallback
    const generator = new AIContentGenerator();
    const result = await generator.generateContent(
      smallTestContent,
      'https://example-ai-tool.com',
      {
        complexity: 'medium',
        priority: 'quality',
        maxRetries: 1 // Low retry count to test fallback faster
      }
    );

    console.log('Fallback test result:', result.success ? '✅ Success' : '❌ Failed');
    if (result.success) {
      console.log('Final provider used:', result.modelUsed?.provider);
      console.log('Strategy used:', result.strategyUsed);
    }
  } catch (error) {
    console.error('❌ Fallback test failed:', error);
  }
  console.log();

  // Test 6: Context Window Management
  console.log('6️⃣ Testing Context Window Management...');
  const { ContextWindowManager } = await import('./context-window-manager');
  
  const largeContent = smallTestContent.repeat(50); // Make content very large
  const tokenCount = ContextWindowManager.calculateTokenCount(largeContent);
  console.log('Large content token count:', tokenCount);
  
  const { OPENAI_CONFIG, OPENROUTER_CONFIG } = await import('./types');
  
  const openaiCanHandle = ContextWindowManager.canModelHandleContent(largeContent, OPENAI_CONFIG);
  const openrouterCanHandle = ContextWindowManager.canModelHandleContent(largeContent, OPENROUTER_CONFIG);
  
  console.log('OpenAI can handle large content:', openaiCanHandle ? '✅ Yes' : '❌ No');
  console.log('OpenRouter can handle large content:', openrouterCanHandle ? '✅ Yes' : '❌ No');
  
  if (!openaiCanHandle) {
    const chunks = ContextWindowManager.splitContentForModel(largeContent, OPENAI_CONFIG);
    console.log('OpenAI would need', chunks.length, 'chunks');
  }
  
  if (!openrouterCanHandle) {
    const chunks = ContextWindowManager.splitContentForModel(largeContent, OPENROUTER_CONFIG);
    console.log('OpenRouter would need', chunks.length, 'chunks');
  }
  console.log();

  // Test 7: Error Handling
  console.log('7️⃣ Testing Error Handling...');
  const { AIErrorHandler } = await import('./error-handler');
  
  // Simulate different types of errors
  const testErrors = [
    { code: 'rate_limit_exceeded', message: 'Rate limit exceeded' },
    { status: 402, message: 'Insufficient credits' },
    { code: 'context_length_exceeded', message: 'Context too long' }
  ];

  testErrors.forEach(async (error, index) => {
    const errorResult = await AIErrorHandler.handleAIError(error, {
      provider: index % 2 === 0 ? 'openai' : 'openrouter',
      attempt: 1,
      maxRetries: 3
    });

    console.log(`Error ${index + 1}:`, {
      error: error.code || `HTTP ${error.status}`,
      retryable: errorResult.retryable,
      suggestion: errorResult.suggestion
    });
  });
  console.log();

  console.log('🎉 Dual AI Provider System Test Complete!\n');
}

// Export for use in other test files
export { testDualProviderSystem };

// Run tests if this file is executed directly
if (require.main === module) {
  testDualProviderSystem().catch(console.error);
}
