'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { FAQ_ITEMS } from '@/lib/constants';

export function FAQSection() {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleItem = (id: string) => {
    setExpandedItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <div className="bg-sky-100 py-16 px-6">
      <div className="mx-auto" style={{ maxWidth: 'var(--container-width)' }}>
        <div className="space-y-6">
          {FAQ_ITEMS.map((item) => {
            const isExpanded = expandedItems.includes(item.id);
            
            return (
              <div key={item.id} className="bg-white rounded-lg shadow-sm">
                <button
                  onClick={() => toggleItem(item.id)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                >
                  <h3 className="text-lg font-bold text-zinc-800 pr-4">
                    {item.question}
                  </h3>
                  {isExpanded ? (
                    <ChevronUp size={20} className="text-zinc-600 flex-shrink-0" />
                  ) : (
                    <ChevronDown size={20} className="text-zinc-600 flex-shrink-0" />
                  )}
                </button>
                
                {isExpanded && (
                  <div className="px-6 pb-4 animate-in slide-in-from-top-2 duration-200">
                    <p className="text-zinc-700 leading-relaxed">
                      {item.answer}
                    </p>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
