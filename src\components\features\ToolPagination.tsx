'use client';

import React from 'react';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import { PaginationInfo } from '@/lib/types';

interface ToolPaginationProps {
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  className?: string;
}

export function ToolPagination({ pagination, onPageChange, className = '' }: ToolPaginationProps) {
  const { currentPage, totalPages, totalItems, itemsPerPage } = pagination;
  
  if (totalPages <= 1) return null;

  const generatePageNumbers = () => {
    const pages: (number | 'ellipsis')[] = [];
    const maxVisiblePages = 7;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);
      
      if (currentPage > 4) {
        pages.push('ellipsis');
      }
      
      // Show pages around current page
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      
      if (currentPage < totalPages - 3) {
        pages.push('ellipsis');
      }
      
      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  const pageNumbers = generatePageNumbers();
  
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  return (
    <div className={`flex flex-col sm:flex-row items-center justify-between gap-4 ${className}`}>
      
      {/* Results info */}
      <div className="text-gray-400 text-sm">
        Showing {startItem}-{endItem} of {totalItems} tools
      </div>
      
      {/* Pagination controls */}
      <div className="flex items-center gap-1">
        
        {/* Previous button */}
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={`flex items-center gap-1 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
            currentPage === 1
              ? 'text-gray-500 cursor-not-allowed'
              : 'text-gray-300 hover:text-white hover:bg-zinc-700'
          }`}
        >
          <ChevronLeft size={16} />
          Previous
        </button>
        
        {/* Page numbers */}
        <div className="flex items-center gap-1 mx-2">
          {pageNumbers.map((page, index) => {
            if (page === 'ellipsis') {
              return (
                <div key={`ellipsis-${index}`} className="px-2 py-2">
                  <MoreHorizontal size={16} className="text-gray-500" />
                </div>
              );
            }
            
            const isActive = page === currentPage;
            
            return (
              <button
                key={page}
                onClick={() => onPageChange(page)}
                className={`w-10 h-10 rounded-lg text-sm font-medium transition-all duration-200 ${
                  isActive
                    ? 'text-white border-2'
                    : 'text-gray-300 hover:text-white hover:bg-zinc-700'
                }`}
                style={isActive ? {
                  backgroundColor: 'rgb(255, 150, 0)',
                  borderColor: 'rgb(255, 150, 0)'
                } : {}}
                onMouseEnter={isActive ? (e) => {
                  e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
                  e.currentTarget.style.borderColor = 'rgb(255, 170, 30)';
                } : undefined}
                onMouseLeave={isActive ? (e) => {
                  e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
                  e.currentTarget.style.borderColor = 'rgb(255, 150, 0)';
                } : undefined}
              >
                {page}
              </button>
            );
          })}
        </div>
        
        {/* Next button */}
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={`flex items-center gap-1 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
            currentPage === totalPages
              ? 'text-gray-500 cursor-not-allowed'
              : 'text-gray-300 hover:text-white hover:bg-zinc-700'
          }`}
        >
          Next
          <ChevronRight size={16} />
        </button>
      </div>
    </div>
  );
}
