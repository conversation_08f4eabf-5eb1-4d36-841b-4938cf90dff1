# CategoryCard Component - Pixel-Perfect Implementation

## Overview

The CategoryCard component is a pixel-perfect React component that matches the AI-DUDE.COM design system. It displays AI tool categories with their associated tools in a visually appealing card format.

## Component Architecture

### 1. CategoryCard.tsx
**Main container component with the following features:**

- **TypeScript Interface**: Fully typed with existing `AICategory` interface
- **Performance**: Optimized with `React.memo` and `useCallback` hooks
- **Accessibility**: Semantic HTML with ARIA labels and keyboard navigation
- **Visual Design**: Pixel-perfect styling matching design specifications

### 2. ToolListItem.tsx
**Individual tool item component featuring:**

- **Numbered List**: Sequential numbering (1., 2., 3...)
- **Favicon Display**: 16x16px tool logos with lazy loading
- **Hover Effects**: Background changes and search icon animation
- **Tag Integration**: Category badges with proper styling
- **Tooltip Support**: Integrated with global tooltip system

### 3. Tag.tsx
**Badge component for tool categorization:**

- **Color Schemes**: Pixel-perfect colors for each tag type
- **Icon Support**: Star icons for PREMIUM, flame icons for HOT
- **Accessibility**: Proper ARIA labels and contrast ratios

## Visual Specifications

### Card Container
```css
bg-zinc-800 border [dynamic-color] rounded-lg p-4 h-fit shadow-lg
```
**Layout Updates**: Optimized for compact design with reduced padding

**Dynamic Border Colors:**
- **Writing Tools**: `border-sky-500` (matches sky button)
- **Image Generators**: `border-green-500` (matches green button)
- **Chatbots**: `border-pink-500` (matches pink button)
- **Dev Tools**: `border-yellow-600` (matches yellow button)
- **Fallback**: `border-zinc-700` (if color extraction fails)

### Header Section
- **Icon**: 16px Lucide React icon, `text-white`, `mt-1` (compact size)
- **Title**: `text-base font-bold text-white leading-tight mb-1` (optimized typography)
  - **Interactive**: Cursor pointer with hover tooltip
  - **Animated Underline**: Expands from center on hover (200ms transition)
  - **Dynamic Color**: Underline color matches category button color
  - **Accessibility**: ARIA labels and keyboard navigation
- **Description**: `text-gray-400 text-xs leading-relaxed`
- **Layout**: `flex items-start gap-3 mb-4` (reduced spacing)

### Tool List
- **Container**: `max-h-64 overflow-y-auto custom-scrollbar mb-4` (compact height)
- **Items**: `px-2 py-2` with `gap-2.5`
- **Numbers**: `text-gray-500 text-xs font-medium w-5`
- **Names**: `text-gray-300 text-sm font-medium`
- **Hover**: `bg-zinc-700` with search icon fade-in

### Tags/Badges
- **AI**: `bg-teal-600 text-white border-teal-500`
- **NEW**: `bg-yellow-500 text-black border-yellow-400`
- **PREMIUM**: `bg-purple-600 text-white border-purple-500` + star icon
- **HOT**: `bg-red-500 text-white border-red-400` + flame icon
- **VR**: `bg-blue-600 text-white border-blue-500`

### CTA Button
- **Layout**: `w-full px-4 py-3 rounded-lg`
- **Typography**: `font-semibold text-sm`
- **Colors**: Dynamic from `category.seeAllButton` properties
- **Icon**: ArrowRight, size 14
- **Effects**: `hover:brightness-110 active:scale-[0.98]`

## Integration Features

### Tooltip System
```typescript
onShowTooltip: (content: string, element: HTMLElement) => void;
onHideTooltip: () => void;
```

### Navigation
- **Tool Clicks**: `window.open(tool.link, '_blank', 'noopener,noreferrer')`
- **CTA Button**: Console log for development, ready for routing

### Performance Optimizations
- **React.memo**: Prevents unnecessary re-renders
- **useCallback**: Stable callback references
- **Lazy Loading**: Images loaded on demand
- **Proper Keys**: Stable keys for list items

### Accessibility Features
- **Semantic HTML**: `<article>`, `<header>`, `<section>`, `<footer>`
- **ARIA Labels**: Descriptive labels for screen readers
- **Keyboard Navigation**: Tab navigation and Enter/Space activation
- **Focus Management**: Visible focus indicators
- **Screen Reader Support**: Proper content hierarchy

## Usage Example

```typescript
import { CategoryCard } from '@/components/features/CategoryCard';
import { useTooltip } from '@/hooks/useTooltip';

function CategoryGrid() {
  const { showTooltip, hideTooltip } = useTooltip();

  return (
    <div className="max-w-7xl mx-auto px-4 py-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {categories.map((category) => (
          <CategoryCard
            key={category.id}
            category={category}
            onShowTooltip={showTooltip}
            onHideTooltip={hideTooltip}
          />
        ))}
      </div>
    </div>
  );
}
```

## Technical Implementation

### Dynamic Border Color System
The CategoryCard implements an intelligent color extraction system that automatically matches the card border to the category's button color:

```typescript
const getBorderColor = useCallback(() => {
  const colorClass = category.seeAllButton.colorClass;

  // Extract the base color from the background class (e.g., "bg-sky-500" -> "sky-500")
  const bgColorMatch = colorClass.match(/bg-(\w+-\d+)/);

  if (bgColorMatch) {
    const colorName = bgColorMatch[1]; // e.g., "sky-500", "green-500", "pink-500", "yellow-600"
    return `border-${colorName}`;
  }

  // Fallback to default zinc border if extraction fails
  return 'border-zinc-700';
}, [category.seeAllButton.colorClass]);
```

**Color Mapping Examples:**
- `bg-sky-500 hover:bg-sky-400` → `border-sky-500` + `#0ea5e9` underline
- `bg-green-500 hover:bg-green-400` → `border-green-500` + `#22c55e` underline
- `bg-pink-500 hover:bg-pink-400` → `border-pink-500` + `#ec4899` underline
- `bg-yellow-600 hover:bg-yellow-500` → `border-yellow-600` + `#ca8a04` underline

### Interactive Title Tooltip System
The category title implements an advanced tooltip system with visual feedback:

```typescript
// Title hover handlers for tooltip
const handleTitleMouseEnter = useCallback((e: React.MouseEvent<HTMLHeadingElement>) => {
  setIsTitleHovered(true);
  onShowTooltip(category.description, e.currentTarget);
}, [category.description, onShowTooltip]);

const handleTitleMouseLeave = useCallback(() => {
  setIsTitleHovered(false);
  onHideTooltip();
}, [onHideTooltip]);
```

**Animated Underline Effect:**
- Uses CSS `::after` pseudo-element with `transform: scaleX()`
- Expands from center outward using `transform-origin: center`
- 200ms transition duration for smooth animation
- Dynamic color via CSS custom properties: `--underline-color`

**CSS Implementation:**
```css
.category-title-underline::after {
  background-color: var(--underline-color, #3f3f46);
}

.category-title-hover:hover {
  transform: translateY(-1px);
}
```

### Component Structure
1. **Article Container**: Semantic wrapper with dynamic border colors and ARIA attributes
2. **Header Section**: Icon, title, and description with proper hierarchy
3. **Tool List Section**: Scrollable list with numbered items
4. **Footer Section**: CTA button with dynamic styling

### State Management
- **Local State**: Hover states for interactive elements
- **Callback Optimization**: Memoized event handlers including color extraction
- **Performance**: Minimal re-renders with stable references

### Responsive Behavior
- **Text Truncation**: `truncate` class for overflow handling
- **Flexible Layout**: `flex-1 min-w-0` for proper sizing
- **Grid Compatibility**: Works with existing responsive grid

## Browser Support
- **Modern Browsers**: Chrome, Firefox, Safari, Edge
- **CSS Features**: CSS Grid, Flexbox, Custom Properties
- **JavaScript**: ES2020+ features with TypeScript support

## Testing Considerations
- **Unit Tests**: Component rendering and interaction
- **Integration Tests**: Tooltip and navigation functionality
- **Accessibility Tests**: Screen reader and keyboard navigation
- **Visual Tests**: Pixel-perfect design verification
