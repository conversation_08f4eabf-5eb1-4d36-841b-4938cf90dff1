# Production Deployment Guide

## 🚀 Critical Production Steps

### 1. Environment Variables Setup

**Required Production Environment Variables:**

```bash
# Supabase (Already configured)
NEXT_PUBLIC_SUPABASE_URL=https://gvcdqspryxrvxadfpwux.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Background Jobs & Automation
JOB_QUEUE_ENABLED=true
MAX_CONCURRENT_JOBS=5  # Increase for production
JOB_RETRY_ATTEMPTS=3

# AI & Content Generation
OPENAI_API_KEY=your-production-openai-key
OPENAI_MODEL=gpt-4o
CONTENT_GENERATION_ENABLED=true

# Email (CRITICAL - Configure real SMTP)
SMTP_HOST=smtp.gmail.com  # or your email provider
SMTP_PORT=587
SMTP_USER=<EMAIL>  # Real email
SMTP_PASS=your-app-password      # Real password
ADMIN_EMAIL=<EMAIL> # Real admin email

# Security (CRITICAL - Generate new keys)
JWT_SECRET=your-production-jwt-secret-256-bit
ADMIN_API_KEY=your-production-admin-api-key

# Production URLs
VERCEL_URL=your-production-domain.com
NEXT_PUBLIC_BASE_URL=https://your-production-domain.com

# Web Scraping (Production optimized)
PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
SCREENSHOT_QUALITY=80
MAX_SCRAPE_TIMEOUT=45000  # Increased for production

# Monitoring & Analytics
GOOGLE_ANALYTICS_ID=your-real-ga-id
SENTRY_DSN=your-sentry-dsn  # For error tracking
```

### 2. Vercel Deployment Steps

**Step 1: Prepare for Deployment**
```bash
# Test production build locally
npm run build
npm start

# Verify all pages load correctly
# Test API endpoints: /api/categories, /api/tools, /api/submissions
```

**Step 2: Deploy to Vercel**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod

# Configure environment variables in Vercel dashboard
# Add all production environment variables
```

**Step 3: Post-Deployment Verification**
- [ ] Homepage loads correctly
- [ ] Tool detail pages work
- [ ] API endpoints respond
- [ ] Job queue processes submissions
- [ ] Email notifications work
- [ ] Database connections stable

### 3. Domain & SSL Configuration

**Custom Domain Setup:**
1. Add domain in Vercel dashboard
2. Configure DNS records
3. Enable automatic SSL certificates
4. Test HTTPS redirects

### 4. Performance Optimization

**Vercel Configuration (vercel.json):**
```json
{
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 60
    }
  },
  "crons": [
    {
      "path": "/api/automation/cleanup",
      "schedule": "0 2 * * *"
    }
  ]
}
```

## 🧪 **PRIORITY 2: Testing & Validation**

### End-to-End Testing Workflow

**Test 1: Complete Tool Submission**
```bash
# Run comprehensive test
npm run test:automation

# Manual test via API
curl -X POST https://your-domain.com/api/submissions \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://openai.com",
    "name": "OpenAI",
    "description": "AI research company",
    "category": "ai-development",
    "submitterEmail": "<EMAIL>",
    "submitterName": "Test User"
  }'
```

**Test 2: Job Queue Monitoring**
```bash
# Check job status
curl -H "X-API-Key: your-admin-key" \
  https://your-domain.com/api/automation/jobs

# Monitor specific job
curl -H "X-API-Key: your-admin-key" \
  https://your-domain.com/api/automation/jobs/[job-id]
```

**Test 3: Email Notifications**
- Submit test tool
- Verify admin notification received
- Check email formatting and content

### Load Testing
```bash
# Install artillery for load testing
npm install -g artillery

# Create load test config
# Test API endpoints under load
# Monitor job queue performance
```

## ⚡ **PRIORITY 3: Performance Optimization**

### Job Queue Optimizations

**Enhanced Configuration:**
```bash
# Production job settings
MAX_CONCURRENT_JOBS=5        # Increase concurrency
JOB_RETRY_ATTEMPTS=3         # Keep retry attempts
JOB_TIMEOUT=300000          # 5 minute timeout
QUEUE_CLEANUP_INTERVAL=3600  # Cleanup every hour
```

### Content Generation Improvements

**Optimized Prompts:**
- Reduce token usage while maintaining quality
- Implement caching for similar tools
- Add content validation before saving

### Web Scraping Enhancements

**Production Settings:**
```bash
MAX_SCRAPE_TIMEOUT=45000     # Increased timeout
CONCURRENT_SCRAPES=2         # Limit concurrent scraping
USER_AGENT_ROTATION=true     # Rotate user agents
PROXY_ENABLED=false          # Enable if needed
```

## 📊 **PRIORITY 4: Monitoring & Maintenance**

### Error Tracking & Logging

**Recommended Tools:**
1. **Sentry** - Error tracking and performance monitoring
2. **Vercel Analytics** - Built-in performance metrics
3. **Supabase Logs** - Database query monitoring

**Implementation:**
```bash
# Install Sentry
npm install @sentry/nextjs

# Configure in next.config.js
# Add error boundaries to components
# Set up performance monitoring
```

### Health Checks

**API Health Endpoint:**
```typescript
// /api/health
export async function GET() {
  const checks = {
    database: await checkDatabase(),
    jobQueue: await checkJobQueue(),
    openai: await checkOpenAI(),
    email: await checkEmail(),
  };
  
  return Response.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    checks
  });
}
```

### Automated Maintenance

**Cleanup Jobs:**
```bash
# Add to package.json scripts
"cleanup:jobs": "tsx scripts/cleanup-completed-jobs.ts",
"cleanup:logs": "tsx scripts/cleanup-old-logs.ts",
"backup:db": "tsx scripts/backup-database.ts"
```

## 🔧 **PRIORITY 5: Feature Enhancements**

### Admin Dashboard
- Job queue monitoring interface
- Tool approval/rejection workflow
- Content moderation tools
- Analytics and metrics

### Enhanced Automation
- Duplicate tool detection
- Auto-categorization improvements
- Batch processing capabilities
- Scheduled content updates

### User Experience
- Real-time submission status
- Email confirmation system
- Tool suggestion improvements
- Advanced search and filtering

## 🚨 **Immediate Action Items**

### Before Production Deploy:
1. **Configure real SMTP credentials**
2. **Generate secure JWT and API keys**
3. **Set up error tracking (Sentry)**
4. **Test complete workflow end-to-end**
5. **Configure custom domain**

### Week 1 Post-Deploy:
1. **Monitor job queue performance**
2. **Verify email notifications**
3. **Check error rates and logs**
4. **Test under realistic load**
5. **Gather user feedback**

### Week 2-4:
1. **Implement admin dashboard**
2. **Add comprehensive monitoring**
3. **Optimize performance bottlenecks**
4. **Enhance content generation**
5. **Plan feature roadmap**

## 📋 **Production Deployment Checklist**

### Pre-Deployment (Critical)
- [ ] **Environment Variables Configured**
  - [ ] Real SMTP credentials (SMTP_HOST, SMTP_USER, SMTP_PASS)
  - [ ] Production admin email (ADMIN_EMAIL)
  - [ ] Secure JWT secret (256-bit random string)
  - [ ] Secure admin API key
  - [ ] OpenAI API key with sufficient credits
  - [ ] Production domain URL (NEXT_PUBLIC_BASE_URL)

- [ ] **Security Configuration**
  - [ ] JWT_SECRET is cryptographically secure
  - [ ] ADMIN_API_KEY is unique and complex
  - [ ] No development keys in production
  - [ ] Environment variables properly secured

- [ ] **Testing Complete**
  - [ ] `npm run health:check` passes
  - [ ] `npm run test:e2e` passes
  - [ ] `npm run production:verify` passes
  - [ ] Manual tool submission test successful
  - [ ] Email notifications working

### Deployment Process
- [ ] **Build Verification**
  - [ ] `npm run build` completes successfully
  - [ ] No TypeScript errors
  - [ ] All pages render correctly
  - [ ] API endpoints respond properly

- [ ] **Vercel Deployment**
  - [ ] Project deployed to Vercel
  - [ ] Environment variables configured in Vercel
  - [ ] Custom domain configured (if applicable)
  - [ ] SSL certificates active
  - [ ] Function timeouts configured (60s for API routes)

### Post-Deployment (First 24 Hours)
- [ ] **Immediate Verification**
  - [ ] Homepage loads correctly
  - [ ] Tool detail pages work
  - [ ] API health endpoint responds: `/api/health`
  - [ ] Job queue processing submissions
  - [ ] Email notifications being sent

- [ ] **Monitoring Setup**
  - [ ] Production monitoring started: `npm run monitor:start`
  - [ ] Error tracking configured (Sentry recommended)
  - [ ] Performance monitoring active
  - [ ] Alert notifications working

- [ ] **Performance Testing**
  - [ ] Load test API endpoints
  - [ ] Monitor job queue under load
  - [ ] Check database performance
  - [ ] Verify email delivery rates

### Ongoing Maintenance
- [ ] **Daily Tasks**
  - [ ] Check monitoring alerts
  - [ ] Review job queue status
  - [ ] Monitor error rates
  - [ ] Verify email delivery

- [ ] **Weekly Tasks**
  - [ ] Run job cleanup: `npm run cleanup:jobs`
  - [ ] Review performance metrics
  - [ ] Check failed job patterns
  - [ ] Update content generation prompts if needed

- [ ] **Monthly Tasks**
  - [ ] Review and rotate API keys
  - [ ] Analyze usage patterns
  - [ ] Plan feature enhancements
  - [ ] Update dependencies

## 🔧 **Quick Commands Reference**

```bash
# Pre-deployment testing
npm run production:verify

# Health monitoring
npm run health:check

# End-to-end testing
npm run test:e2e

# Start production monitoring
npm run monitor:start

# Clean up old jobs
npm run cleanup:jobs

# View job statistics
npm run cleanup:jobs stats

# Deploy preparation
npm run deploy:prepare
```

## 🆘 **Troubleshooting Guide**

### Common Issues

**Job Queue Not Processing:**
```bash
# Check if queue is enabled
echo $JOB_QUEUE_ENABLED

# Check job status
npm run cleanup:jobs stats

# Restart monitoring
npm run monitor:start
```

**Email Notifications Failing:**
```bash
# Test SMTP configuration
npm run health:check

# Check email job status
npm run cleanup:jobs stats
```

**High Job Failure Rate:**
```bash
# Check recent failed jobs
npm run cleanup:jobs stats

# Review error logs
# Check OpenAI API credits
# Verify web scraping targets
```

**Database Connection Issues:**
```bash
# Test database connectivity
npm run health:check

# Check Supabase dashboard
# Verify connection string
# Check service role key
```
