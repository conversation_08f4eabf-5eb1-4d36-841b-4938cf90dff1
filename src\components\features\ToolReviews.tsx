'use client';

import React from 'react';
import { Star, Users, ThumbsUp } from 'lucide-react';
import { AITool } from '@/lib/types';

interface ToolReviewsProps {
  reviews: NonNullable<AITool['reviews']>;
}

export function ToolReviews({ reviews }: ToolReviewsProps) {
  // Generate star rating display
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={i} size={16} className="text-yellow-400 fill-current" />
      );
    }
    
    if (hasHalfStar) {
      stars.push(
        <div key="half" className="relative">
          <Star size={16} className="text-gray-400" />
          <div className="absolute inset-0 overflow-hidden w-1/2">
            <Star size={16} className="text-yellow-400 fill-current" />
          </div>
        </div>
      );
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Star key={`empty-${i}`} size={16} className="text-gray-400" />
      );
    }
    
    return stars;
  };

  // Get rating color based on score
  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return 'text-green-400';
    if (rating >= 4.0) return 'text-yellow-400';
    if (rating >= 3.0) return 'text-orange-400';
    return 'text-red-400';
  };

  // Get rating description
  const getRatingDescription = (rating: number) => {
    if (rating >= 4.5) return 'Excellent';
    if (rating >= 4.0) return 'Very Good';
    if (rating >= 3.5) return 'Good';
    if (rating >= 3.0) return 'Average';
    return 'Below Average';
  };

  return (
    <section className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
      <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
        <ThumbsUp size={20} className="text-blue-400" />
        User Reviews
      </h3>
      
      {/* Overall Rating */}
      <div className="text-center mb-6">
        <div className={`text-4xl font-bold mb-2 ${getRatingColor(reviews.rating)}`}>
          {reviews.rating.toFixed(1)}
        </div>
        
        <div className="flex justify-center gap-1 mb-2">
          {renderStars(reviews.rating)}
        </div>
        
        <div className="text-gray-400 text-sm mb-1">
          {getRatingDescription(reviews.rating)}
        </div>
        
        <div className="flex items-center justify-center gap-1 text-gray-400 text-xs">
          <Users size={12} />
          <span>{reviews.totalReviews.toLocaleString()} reviews</span>
        </div>
      </div>
      
      {/* Rating Breakdown */}
      <div className="space-y-2 mb-6">
        {[5, 4, 3, 2, 1].map((stars) => {
          // Calculate percentage for each star rating (mock data for now)
          const percentage = stars === Math.round(reviews.rating) ? 60 : 
                           Math.abs(stars - reviews.rating) <= 1 ? 25 : 10;
          
          return (
            <div key={stars} className="flex items-center gap-2 text-sm">
              <span className="text-gray-400 w-2">{stars}</span>
              <Star size={12} className="text-yellow-400 fill-current" />
              <div className="flex-1 bg-zinc-700 rounded-full h-2">
                <div 
                  className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${percentage}%` }}
                />
              </div>
              <span className="text-gray-400 text-xs w-8">{percentage}%</span>
            </div>
          );
        })}
      </div>
      
      {/* Review Highlights */}
      {reviews.highlights && reviews.highlights.length > 0 && (
        <div>
          <h4 className="text-white font-medium mb-3">What users love:</h4>
          <div className="space-y-2">
            {reviews.highlights.map((highlight, index) => (
              <div
                key={index}
                className="flex items-start gap-2 p-2 bg-zinc-700/50 rounded text-sm"
              >
                <ThumbsUp size={14} className="text-green-400 mt-0.5 flex-shrink-0" />
                <span className="text-gray-300">{highlight}</span>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Trust Indicators */}
      <div className="mt-6 pt-4 border-t border-zinc-600">
        <div className="grid grid-cols-2 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-green-400">
              {Math.round((reviews.rating / 5) * 100)}%
            </div>
            <div className="text-gray-400 text-xs">Satisfaction</div>
          </div>
          <div>
            <div className="text-lg font-bold text-blue-400">
              {reviews.totalReviews > 1000 ? '1K+' : reviews.totalReviews}
            </div>
            <div className="text-gray-400 text-xs">Reviews</div>
          </div>
        </div>
      </div>
    </section>
  );
}
