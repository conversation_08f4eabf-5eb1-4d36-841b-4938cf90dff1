/**
 * Database Migration Runner for Enhanced AI System
 * 
 * This module provides utilities for running database migrations
 * against the Supabase PostgreSQL database.
 * 
 * Usage:
 * - Run migrations: npm run migrate
 * - Rollback migrations: npm run migrate:rollback
 * - Check migration status: npm run migrate:status
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing required Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

interface Migration {
  id: string;
  name: string;
  sql: string;
  rollbackSql?: string;
}

interface MigrationRecord {
  id: string;
  name: string;
  executed_at: string;
  checksum: string;
}

/**
 * Create migrations tracking table if it doesn't exist
 */
async function ensureMigrationsTable(): Promise<void> {
  // Check if migrations table exists
  const { data: tables, error: tablesError } = await supabase
    .from('information_schema.tables')
    .select('table_name')
    .eq('table_schema', 'public')
    .eq('table_name', '_migrations');

  if (tablesError) {
    console.log('Note: Could not check for migrations table existence, proceeding with creation attempt');
  }

  // If table doesn't exist, we'll need to create it manually
  // For now, let's assume it exists or create it through the Supabase dashboard
  console.log('✅ Migrations table check completed');
}

/**
 * Calculate checksum for migration content
 */
function calculateChecksum(content: string): string {
  const crypto = require('crypto');
  return crypto.createHash('sha256').update(content).digest('hex');
}

/**
 * Load migration file
 */
function loadMigration(filename: string): Migration {
  const migrationPath = join(process.cwd(), 'src/lib/database/migrations', filename);
  const rollbackPath = migrationPath.replace('.sql', '_rollback.sql');
  
  try {
    const sql = readFileSync(migrationPath, 'utf-8');
    let rollbackSql: string | undefined;
    
    try {
      rollbackSql = readFileSync(rollbackPath, 'utf-8');
    } catch {
      // Rollback file is optional
    }
    
    const id = filename.replace('.sql', '');
    const name = id.replace(/^\d+_/, '').replace(/_/g, ' ');
    
    return { id, name, sql, rollbackSql };
  } catch (error) {
    throw new Error(`Failed to load migration ${filename}: ${error}`);
  }
}

/**
 * Get executed migrations from database
 */
async function getExecutedMigrations(): Promise<MigrationRecord[]> {
  const { data, error } = await supabase
    .from('_migrations')
    .select('*')
    .order('executed_at', { ascending: true });

  if (error) {
    throw new Error(`Failed to get executed migrations: ${error.message}`);
  }

  return data || [];
}

/**
 * Execute a migration
 */
async function executeMigration(migration: Migration): Promise<void> {
  console.log(`Executing migration: ${migration.id} - ${migration.name}`);

  const checksum = calculateChecksum(migration.sql);

  // For now, we'll output the SQL that needs to be executed manually
  console.log(`\n📋 SQL to execute manually in Supabase SQL Editor:`);
  console.log('=' .repeat(80));
  console.log(migration.sql);
  console.log('=' .repeat(80));

  // Note: In a real implementation, you would execute this SQL through Supabase's SQL editor
  // or use a database connection that allows DDL operations

  console.log(`✅ Migration ${migration.id} SQL prepared for manual execution`);
}

/**
 * Rollback a migration
 */
async function rollbackMigration(migration: Migration): Promise<void> {
  if (!migration.rollbackSql) {
    throw new Error(`No rollback script available for migration ${migration.id}`);
  }

  console.log(`Rolling back migration: ${migration.id} - ${migration.name}`);

  // For now, we'll output the rollback SQL that needs to be executed manually
  console.log(`\n📋 Rollback SQL to execute manually in Supabase SQL Editor:`);
  console.log('=' .repeat(80));
  console.log(migration.rollbackSql);
  console.log('=' .repeat(80));

  console.log(`✅ Migration ${migration.id} rollback SQL prepared for manual execution`);
}

/**
 * Run all pending migrations
 */
export async function runMigrations(): Promise<void> {
  try {
    console.log('🚀 Starting database migrations...');
    
    await ensureMigrationsTable();
    
    const executedMigrations = await getExecutedMigrations();
    const executedIds = new Set(executedMigrations.map(m => m.id));
    
    // Define available migrations in order
    const availableMigrations = [
      '001_enhanced_ai_system_schema.sql'
    ];
    
    const pendingMigrations = availableMigrations.filter(filename => {
      const id = filename.replace('.sql', '');
      return !executedIds.has(id);
    });
    
    if (pendingMigrations.length === 0) {
      console.log('✅ No pending migrations');
      return;
    }
    
    console.log(`Found ${pendingMigrations.length} pending migrations`);
    
    for (const filename of pendingMigrations) {
      const migration = loadMigration(filename);
      await executeMigration(migration);
    }
    
    console.log('🎉 All migrations completed successfully');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

/**
 * Rollback the last migration
 */
export async function rollbackLastMigration(): Promise<void> {
  try {
    console.log('🔄 Rolling back last migration...');
    
    await ensureMigrationsTable();
    
    const executedMigrations = await getExecutedMigrations();
    
    if (executedMigrations.length === 0) {
      console.log('✅ No migrations to rollback');
      return;
    }
    
    const lastMigration = executedMigrations[executedMigrations.length - 1];
    const migration = loadMigration(`${lastMigration.id}.sql`);
    
    await rollbackMigration(migration);
    
    console.log('🎉 Rollback completed successfully');
    
  } catch (error) {
    console.error('❌ Rollback failed:', error);
    throw error;
  }
}

/**
 * Show migration status
 */
export async function showMigrationStatus(): Promise<void> {
  try {
    await ensureMigrationsTable();
    
    const executedMigrations = await getExecutedMigrations();
    
    console.log('\n📊 Migration Status:');
    console.log('==================');
    
    if (executedMigrations.length === 0) {
      console.log('No migrations executed yet');
    } else {
      executedMigrations.forEach(migration => {
        console.log(`✅ ${migration.id} - ${migration.name} (${migration.executed_at})`);
      });
    }
    
    console.log(`\nTotal executed migrations: ${executedMigrations.length}`);
    
  } catch (error) {
    console.error('❌ Failed to get migration status:', error);
    throw error;
  }
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];
  
  switch (command) {
    case 'up':
    case 'migrate':
      runMigrations().catch(process.exit);
      break;
    case 'down':
    case 'rollback':
      rollbackLastMigration().catch(process.exit);
      break;
    case 'status':
      showMigrationStatus().catch(process.exit);
      break;
    default:
      console.log('Usage: npm run migrate [up|down|status]');
      console.log('  up/migrate: Run pending migrations');
      console.log('  down/rollback: Rollback last migration');
      console.log('  status: Show migration status');
      process.exit(1);
  }
}
