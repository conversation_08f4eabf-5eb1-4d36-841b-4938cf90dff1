'use client';

import { useState } from 'react';
import { ManualEntryProcessor } from '@/lib/bulk-processing/file-processors';

interface ManualEntrySectionProps {
  active: boolean;
  onSelect: () => void;
  onDataProcessed: (data: any) => void;
  onError: (error: string) => void;
}

/**
 * Manual Entry Section Component
 * 
 * Provides a textarea interface for manual URL entry
 * with real-time validation and processing.
 */
export function ManualEntrySection({
  active,
  onSelect,
  onDataProcessed,
  onError,
}: ManualEntrySectionProps) {
  const [input, setInput] = useState('');
  const [processing, setProcessing] = useState(false);
  const [validationResult, setValidationResult] = useState<any>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setInput(value);
    
    if (!active && value.trim()) {
      onSelect();
    }

    // Real-time validation for non-empty input
    if (value.trim()) {
      validateInput(value);
    } else {
      setValidationResult(null);
    }
  };

  const validateInput = (input: string) => {
    try {
      const processor = new ManualEntryProcessor();
      const result = processor.processInput(input);
      
      if (result.success && result.data) {
        setValidationResult({
          valid: result.data.validItems.length,
          invalid: result.data.invalidItems.length,
          total: result.data.totalItems,
        });
      }
    } catch (err) {
      setValidationResult(null);
    }
  };

  const handleProcess = async () => {
    if (!input.trim()) {
      onError('Please enter some URLs to process');
      return;
    }

    setProcessing(true);

    try {
      const processor = new ManualEntryProcessor();
      const result = processor.processInput(input);

      if (result.success && result.data) {
        if (result.data.validItems.length === 0) {
          onError('No valid URLs found in the input');
        } else {
          onDataProcessed(result.data);
        }
      } else {
        onError(result.error || 'Failed to process input');
      }
    } catch (err) {
      onError(err instanceof Error ? err.message : 'Failed to process input');
    } finally {
      setProcessing(false);
    }
  };

  const handleClear = () => {
    setInput('');
    setValidationResult(null);
  };

  const exampleUrls = [
    'https://example-ai-tool.com',
    'https://another-tool.ai',
    '# This is a comment - will be ignored',
    'https://third-tool.com/features',
  ].join('\n');

  return (
    <div className={`
      bg-zinc-800 border-2 rounded-lg p-6 transition-all duration-200 cursor-pointer
      ${active 
        ? 'border-orange-500 bg-zinc-800' 
        : 'border-zinc-700 hover:border-zinc-600'
      }
    `}>
      <div className="text-center mb-6">
        <div className={`
          inline-flex items-center justify-center w-16 h-16 rounded-full mb-4
          ${active ? 'bg-orange-500 text-white' : 'bg-zinc-700 text-gray-400'}
        `}>
          <span className="text-2xl">✏️</span>
        </div>
        <h3 className="text-xl font-semibold text-white mb-2">Manual Entry</h3>
        <p className="text-gray-400 text-sm">
          Enter URLs directly, one per line
        </p>
      </div>

      {/* Input Area */}
      <div className="space-y-4">
        <div className="relative">
          <textarea
            value={input}
            onChange={handleInputChange}
            placeholder={`Enter URLs, one per line:\n\n${exampleUrls}`}
            className="w-full h-64 bg-zinc-700 border border-zinc-600 rounded-lg p-4 text-white placeholder-gray-500 resize-none focus:outline-none focus:border-orange-500 transition-colors"
            onClick={() => !active && onSelect()}
          />
          
          {/* Character Count */}
          <div className="absolute bottom-2 right-2 text-xs text-gray-500">
            {input.length} characters
          </div>
        </div>

        {/* Validation Results */}
        {validationResult && (
          <div className="bg-zinc-700 rounded-lg p-4">
            <h4 className="text-sm font-medium text-white mb-2">Validation Results:</h4>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="text-green-400 font-bold text-lg">{validationResult.valid}</div>
                <div className="text-gray-400">Valid URLs</div>
              </div>
              <div className="text-center">
                <div className="text-red-400 font-bold text-lg">{validationResult.invalid}</div>
                <div className="text-gray-400">Invalid URLs</div>
              </div>
              <div className="text-center">
                <div className="text-blue-400 font-bold text-lg">{validationResult.total}</div>
                <div className="text-gray-400">Total Items</div>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={handleProcess}
            disabled={!input.trim() || processing}
            className={`
              flex-1 py-3 px-4 rounded-lg font-medium transition-colors
              ${input.trim() && !processing
                ? 'bg-orange-500 hover:bg-orange-600 text-white'
                : 'bg-zinc-600 text-gray-400 cursor-not-allowed'
              }
            `}
          >
            {processing ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                <span>Processing...</span>
              </div>
            ) : (
              'Process URLs'
            )}
          </button>
          
          <button
            onClick={handleClear}
            disabled={!input.trim()}
            className={`
              px-4 py-3 rounded-lg font-medium transition-colors
              ${input.trim()
                ? 'bg-zinc-600 hover:bg-zinc-500 text-white'
                : 'bg-zinc-700 text-gray-500 cursor-not-allowed'
              }
            `}
          >
            Clear
          </button>
        </div>

        {/* Help Text */}
        <div className="text-xs text-gray-500 space-y-1">
          <p><strong>Tips:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-2">
            <li>Enter one URL per line</li>
            <li>Lines starting with # are treated as comments</li>
            <li>Empty lines are automatically ignored</li>
            <li>Duplicate URLs will be removed</li>
            <li>Maximum 1000 URLs per batch</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
