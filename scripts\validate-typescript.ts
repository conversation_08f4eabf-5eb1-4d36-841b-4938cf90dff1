#!/usr/bin/env tsx

/**
 * Comprehensive TypeScript Validation Script
 * 
 * This script provides a proper way to validate TypeScript compilation
 * for Next.js projects, specifically focusing on the bulk processing UI components.
 */

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';

interface ValidationResult {
  success: boolean;
  errors: string[];
  warnings: string[];
}

class TypeScriptValidator {
  private projectRoot: string;

  constructor() {
    this.projectRoot = process.cwd();
  }

  /**
   * Validate TypeScript using Next.js build process
   */
  async validateWithNextJS(): Promise<ValidationResult> {
    console.log('🔍 Validating TypeScript using Next.js build process...');
    
    return new Promise((resolve) => {
      const nextBuild = spawn('npx', ['next', 'build', '--no-lint'], {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });

      let stdout = '';
      let stderr = '';

      nextBuild.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      nextBuild.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      nextBuild.on('close', (code) => {
        const errors: string[] = [];
        const warnings: string[] = [];

        // Parse output for TypeScript errors
        const output = stdout + stderr;
        const lines = output.split('\n');

        for (const line of lines) {
          if (line.includes('Type error:') || line.includes('TS')) {
            errors.push(line.trim());
          } else if (line.includes('Warning:')) {
            warnings.push(line.trim());
          }
        }

        resolve({
          success: code === 0 && errors.length === 0,
          errors,
          warnings
        });
      });
    });
  }

  /**
   * Validate specific bulk processing components
   */
  async validateBulkProcessingComponents(): Promise<ValidationResult> {
    console.log('🔍 Validating bulk processing components...');
    
    const componentsToCheck = [
      'src/app/admin/bulk/page.tsx',
      'src/components/admin/bulk-processing/BulkProcessingDashboard.tsx',
      'src/components/admin/bulk-processing/FileUploadSection.tsx',
      'src/components/admin/bulk-processing/ManualEntrySection.tsx',
      'src/components/admin/bulk-processing/ProcessingOptionsPanel.tsx',
      'src/components/admin/bulk-processing/ProgressTracker.tsx',
      'src/components/admin/bulk-processing/ResultsViewer.tsx',
      'src/components/admin/bulk-processing/BulkJobHistory.tsx'
    ];

    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if all files exist
    for (const component of componentsToCheck) {
      const filePath = path.join(this.projectRoot, component);
      try {
        await fs.access(filePath);
        console.log(`✅ ${component} exists`);
      } catch (error) {
        errors.push(`❌ ${component} not found`);
      }
    }

    // Check TypeScript syntax using tsx
    for (const component of componentsToCheck) {
      try {
        const result = await this.checkComponentSyntax(component);
        if (!result.success) {
          errors.push(...result.errors);
        }
        warnings.push(...result.warnings);
      } catch (error) {
        errors.push(`❌ ${component}: ${error}`);
      }
    }

    return {
      success: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Check individual component syntax
   */
  private async checkComponentSyntax(componentPath: string): Promise<ValidationResult> {
    return new Promise((resolve) => {
      const tsxCheck = spawn('npx', ['tsx', '--check', componentPath], {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });

      let stderr = '';

      tsxCheck.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      tsxCheck.on('close', (code) => {
        const errors: string[] = [];
        const warnings: string[] = [];

        if (code !== 0 && stderr) {
          errors.push(`${componentPath}: ${stderr.trim()}`);
        }

        resolve({
          success: code === 0,
          errors,
          warnings
        });
      });
    });
  }

  /**
   * Run comprehensive validation
   */
  async runValidation(): Promise<void> {
    console.log('🚀 Starting comprehensive TypeScript validation...\n');

    // 1. Validate bulk processing components
    const componentResult = await this.validateBulkProcessingComponents();
    
    if (componentResult.success) {
      console.log('✅ All bulk processing components are valid');
    } else {
      console.log('❌ Bulk processing component validation failed:');
      componentResult.errors.forEach(error => console.log(`  ${error}`));
    }

    if (componentResult.warnings.length > 0) {
      console.log('⚠️  Warnings:');
      componentResult.warnings.forEach(warning => console.log(`  ${warning}`));
    }

    console.log('\n' + '='.repeat(60));

    // 2. Validate using Next.js (optional, can be slow)
    if (process.argv.includes('--full')) {
      console.log('\n🔍 Running full Next.js build validation...');
      const nextResult = await this.validateWithNextJS();
      
      if (nextResult.success) {
        console.log('✅ Next.js build validation passed');
      } else {
        console.log('❌ Next.js build validation failed:');
        nextResult.errors.forEach(error => console.log(`  ${error}`));
      }
    } else {
      console.log('\n💡 Use --full flag to run complete Next.js build validation');
    }

    // Summary
    console.log('\n📊 Validation Summary:');
    console.log(`  Components: ${componentResult.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  Errors: ${componentResult.errors.length}`);
    console.log(`  Warnings: ${componentResult.warnings.length}`);

    if (componentResult.success) {
      console.log('\n🎉 TypeScript validation completed successfully!');
      console.log('📦 Bulk processing UI components are ready for production.');
    } else {
      console.log('\n❌ TypeScript validation failed. Please fix the errors above.');
      process.exit(1);
    }
  }
}

// Run validation
const validator = new TypeScriptValidator();
validator.runValidation().catch((error) => {
  console.error('❌ Validation script failed:', error);
  process.exit(1);
});
