'use client';

import { ToolSubmissionForm } from '@/components/forms/ToolSubmissionForm';

export default function SubmitToolPage() {
  return (
    <div className="min-h-screen bg-zinc-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-4">Submit Your AI Tool</h1>
            <p className="text-xl text-gray-300 mb-6">
              Help the community discover amazing AI tools by submitting yours for review
            </p>
            <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6 text-left">
              <h3 className="text-lg font-semibold mb-3">📋 Submission Guidelines</h3>
              <ul className="space-y-2 text-gray-300">
                <li>• Your tool must be publicly accessible</li>
                <li>• Provide accurate and detailed information</li>
                <li>• Include a working website URL</li>
                <li>• Tools are reviewed within 24-48 hours</li>
                <li>• We may contact you for additional information</li>
              </ul>
            </div>
          </div>

          {/* Submission Form */}
          <ToolSubmissionForm 
            onSuccess={() => {
              // Redirect to success page or show success message
              window.location.href = '/submit/success';
            }}
          />

          {/* Additional Info */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-3">🚀 Why Submit?</h3>
              <ul className="space-y-2 text-gray-300">
                <li>• Increase your tool's visibility</li>
                <li>• Reach thousands of AI enthusiasts</li>
                <li>• Get valuable user feedback</li>
                <li>• Join our growing community</li>
              </ul>
            </div>

            <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-3">📞 Need Help?</h3>
              <p className="text-gray-300 mb-3">
                Having trouble with your submission? We're here to help!
              </p>
              <div className="space-y-2 text-sm">
                <p>📧 Email: <EMAIL></p>
                <p>💬 Discord: Join our community</p>
                <p>📱 Twitter: @aidudedirectory</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
