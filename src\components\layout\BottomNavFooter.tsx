'use client';

import React, { useState } from 'react';
import { Twitter, Mail, PlusCircle, Newspaper, MessageSquare, Sun } from 'lucide-react';
import Link from 'next/link';

export function BottomNavFooter() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  const footerLinks = [
    'ABOUT ME',
    'CONTACT ME',
    'MY BLOG',
    'RTA',
    'PRIVACY POLICY',
    'TERMS OF SERVICE',
    'FAQ',
  ];

  // Static animation delays to prevent hydration errors
  const animationDelays = [
    '0.1s',
    '0.3s',
    '0.5s',
    '0.7s',
    '0.9s',
    '1.1s',
    '1.3s',
  ];

  return (
    <footer className="bg-orange-400 py-8 px-6">
      <div className="max-w-6xl mx-auto">
        {/* Social Icons Row */}
        <div className="flex justify-center gap-4 mb-6">
          <Link
            href="/contact"
            className="w-12 h-12 flex items-center justify-center rounded-full border-2 border-black bg-white transition-all duration-200 ease-in-out transform hover:rotate-6 hover:scale-110 enhanced-icon"
            style={{
              transition: 'all 0.2s ease-in-out'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
            aria-label="Contact AI Dude"
          >
            <Mail size={20} className="text-black transition-colors duration-200" />
          </Link>

          <Link
            href="/submit-tool"
            className="w-12 h-12 flex items-center justify-center rounded-full border-2 border-black bg-orange-500 transition-all duration-200 ease-in-out transform hover:rotate-6 hover:scale-110 enhanced-icon"
            style={{
              transition: 'all 0.2s ease-in-out'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(235, 130, 0)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#f97316'}
            aria-label="Submit your AI Tool"
          >
            <PlusCircle size={20} className="text-white transition-colors duration-200" />
          </Link>

          <Link
            href="https://x.com/aidude_com"
            target="_blank"
            rel="noopener noreferrer"
            className="w-12 h-12 flex items-center justify-center rounded-full border-2 border-black bg-white transition-all duration-200 ease-in-out transform hover:rotate-6 hover:scale-110 enhanced-icon"
            style={{
              transition: 'all 0.2s ease-in-out'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
            aria-label="Follow my Twitter"
          >
            <Twitter size={20} className="text-black transition-colors duration-200" />
          </Link>

          <Link
            href="/blog"
            className="w-12 h-12 flex flex-col items-center justify-center rounded-full border-2 border-black bg-white transition-all duration-200 ease-in-out transform hover:rotate-6 hover:scale-110 enhanced-icon"
            style={{
              transition: 'all 0.2s ease-in-out'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
            aria-label="AI Dude's blog isn't boring"
          >
            <Newspaper size={16} className="text-black transition-colors duration-200" />
            <span className="text-[8px] font-medium leading-tight text-black transition-colors duration-200">BLOG</span>
          </Link>

          <Link
            href="https://aidude.shop"
            target="_blank"
            rel="noopener noreferrer"
            className="w-12 h-12 flex flex-col items-center justify-center rounded-full border-2 border-black bg-white transition-all duration-200 ease-in-out transform hover:rotate-6 hover:scale-110 enhanced-icon"
            style={{
              transition: 'all 0.2s ease-in-out'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
            aria-label="AI Dude's shop"
          >
            <MessageSquare size={16} className="text-black transition-colors duration-200" />
            <span className="text-[8px] font-medium leading-tight text-black transition-colors duration-200">SHOP</span>
          </Link>

          <button
            onClick={() => setIsDarkMode(!isDarkMode)}
            className="w-12 h-12 flex items-center justify-center rounded-full border-2 border-black bg-white transition-all duration-200 ease-in-out transform hover:rotate-6 hover:scale-110 enhanced-icon"
            style={{
              transition: 'all 0.2s ease-in-out'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
            aria-label="Theme Mode"
          >
            <Sun size={20} className="text-black transition-colors duration-200" />
          </button>
        </div>

        {/* Primary Links */}
        <div className="flex flex-wrap justify-center gap-4 mb-6">
          {footerLinks.map((link, index) => (
            <button
              key={link}
              className="text-zinc-800 hover:text-white font-medium text-sm transition-colors duration-200 enhanced-button wave-animation"
              style={{
                animationDelay: animationDelays[index]
              }}
            >
              {link}
            </button>
          ))}
        </div>

        {/* Additional Info */}
        <div className="flex justify-center text-zinc-800">
          <div className="text-sm font-medium shine-effect">
            © 2024 TheAIDude.com - The Best AI Tools Site!
          </div>
        </div>
      </div>
    </footer>
  );
}
