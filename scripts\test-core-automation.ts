#!/usr/bin/env tsx

/**
 * Test core automation features without email dependencies
 * Run with: npx tsx scripts/test-core-automation.ts
 */

import { config } from 'dotenv';
import { getJobQueue, JobType, JobPriority } from '../src/lib/jobs';

// Load environment variables
config({ path: '.env.local' });

async function testCoreAutomation() {
  console.log('🚀 Testing Core Automation Features (No Email Required)\n');

  if (process.env.JOB_QUEUE_ENABLED !== 'true') {
    console.log('❌ Job queue is disabled. Enable it in .env.local');
    process.exit(1);
  }

  console.log('✅ Job queue is enabled');
  console.log('✅ Email notifications will be skipped (no SMTP config needed)');
  console.log('✅ Core features: Web Scraping + Content Generation + Job Processing\n');

  try {
    const queue = getJobQueue();

    // Test 1: Web Scraping (Core Feature)
    console.log('🕷️ Testing Web Scraping...');
    const scrapingJob = await queue.add(
      JobType.WEB_SCRAPING,
      {
        url: 'https://openai.com',
        options: {
          timeout: 15000,
          extractImages: true,
          extractLinks: true,
        },
      },
      { priority: JobPriority.HIGH }
    );
    console.log(`✅ Scraping job queued: ${scrapingJob.id}`);

    // Test 2: Content Generation (Core Feature)
    console.log('\n🤖 Testing Content Generation...');
    const contentJob = await queue.add(
      JobType.CONTENT_GENERATION,
      {
        url: 'https://openai.com',
        scrapedData: {
          title: 'OpenAI',
          text: 'OpenAI is an AI research and deployment company. Our mission is to ensure that artificial general intelligence benefits all of humanity.',
          meta: {
            description: 'OpenAI is an AI research and deployment company.',
            'og:title': 'OpenAI',
          },
          headings: [
            { level: 'h1', text: 'OpenAI' },
            { level: 'h2', text: 'Research' },
            { level: 'h2', text: 'Products' },
          ],
          pricing: [
            { text: 'Free tier available', tag: 'div' },
            { text: 'Pay-per-use pricing', tag: 'span' },
          ],
        },
      },
      { priority: JobPriority.NORMAL }
    );
    console.log(`✅ Content generation job queued: ${contentJob.id}`);

    // Test 3: Tool Submission (End-to-End Workflow)
    console.log('\n🛠️ Testing Tool Submission Workflow...');
    const toolJob = await queue.add(
      JobType.TOOL_SUBMISSION,
      {
        url: 'https://github.com/microsoft/vscode',
        name: 'Visual Studio Code',
        description: 'Free source-code editor made by Microsoft',
        category: 'development',
        submitterEmail: '<EMAIL>',
        submitterName: 'Test User',
      },
      { priority: JobPriority.NORMAL }
    );
    console.log(`✅ Tool submission job queued: ${toolJob.id}`);

    // Monitor jobs for 60 seconds
    console.log('\n⏱️ Monitoring job progress for 60 seconds...\n');
    const startTime = Date.now();
    const jobIds = [scrapingJob.id, contentJob.id, toolJob.id];
    
    const monitorInterval = setInterval(async () => {
      const jobs = await Promise.all(jobIds.map(id => queue.getJob(id)));
      
      console.log('📊 Job Status Update:');
      jobs.forEach((job, index) => {
        if (job) {
          const emoji = job.status === 'completed' ? '✅' : 
                       job.status === 'failed' ? '❌' : 
                       job.status === 'processing' ? '🔄' : '⏳';
          console.log(`  ${emoji} ${['Scraping', 'Content Gen', 'Tool Submission'][index]}: ${job.status}`);
          if (job.error) {
            console.log(`    Error: ${job.error.substring(0, 100)}...`);
          }
        }
      });
      console.log('');

      // Check if all jobs are done or timeout
      const allDone = jobs.every(job => job && ['completed', 'failed'].includes(job.status));
      const timeoutReached = Date.now() - startTime > 60000;

      if (allDone || timeoutReached) {
        clearInterval(monitorInterval);
        
        console.log('🏁 Final Results:');
        console.log('================');
        
        for (let i = 0; i < jobs.length; i++) {
          const job = jobs[i];
          const names = ['Web Scraping', 'Content Generation', 'Tool Submission'];
          
          if (job) {
            console.log(`\n${names[i]}:`);
            console.log(`  Status: ${job.status}`);
            console.log(`  Attempts: ${job.attempts}/${job.maxAttempts}`);
            
            if (job.status === 'completed' && job.result) {
              console.log(`  ✅ Success!`);
              if (i === 0) { // Scraping
                console.log(`    - Scraped title: ${job.result.data?.title || 'N/A'}`);
                console.log(`    - Images found: ${job.result.data?.images?.length || 0}`);
                console.log(`    - Links found: ${job.result.data?.links?.length || 0}`);
              } else if (i === 1) { // Content Generation
                console.log(`    - Generated content structure: ${Object.keys(job.result.content || {}).join(', ')}`);
              } else if (i === 2) { // Tool Submission
                console.log(`    - Tool processing completed`);
              }
            } else if (job.status === 'failed') {
              console.log(`  ❌ Failed: ${job.error}`);
            } else {
              console.log(`  ⏳ Still ${job.status}...`);
            }
          }
        }

        console.log('\n🎉 Core automation testing completed!');
        console.log('\n📝 Summary:');
        console.log('- Web scraping: Extracts website data and screenshots');
        console.log('- Content generation: Creates AI-powered tool descriptions');
        console.log('- Tool submission: End-to-end processing workflow');
        console.log('- Email notifications: Skipped (configure SMTP when needed)');
        console.log('\n✅ Your automation system is working! Ready for production.');
        
        process.exit(0);
      }
    }, 3000); // Check every 3 seconds

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Test interrupted');
  process.exit(0);
});

testCoreAutomation();
