/**
 * Migration Module - Task 4.1: Data Migration Execution
 * 
 * Comprehensive data migration system with backup, validation, and rollback capabilities.
 * Provides robust error handling, integrity verification, and recovery procedures.
 */

export { DataMigrationExecutor } from './data-migration-executor';
export { MigrationValidator } from './migration-validator';
export { RollbackManager } from './rollback-manager';

export type {
  MigrationConfig,
  MigrationResult,
  BackupData
} from './data-migration-executor';

export type {
  ValidationResult,
  ValidationError,
  ValidationWarning,
  ValidationSummary
} from './migration-validator';

export type {
  RollbackConfig,
  RollbackResult
} from './rollback-manager';

/**
 * Migration System Overview
 * 
 * This module provides a complete data migration solution for Task 4.1 with:
 * 
 * 1. **DataMigrationExecutor**: Main migration orchestrator
 *    - Creates comprehensive backups before migration
 *    - Migrates existing tool data to enhanced schema
 *    - Preserves job history and configuration
 *    - Provides rollback capabilities on failure
 * 
 * 2. **MigrationValidator**: Validation and integrity checking
 *    - Pre-migration data integrity validation
 *    - Post-migration verification
 *    - Schema compliance checking
 *    - Foreign key relationship validation
 * 
 * 3. **RollbackManager**: Rollback and recovery system
 *    - Automatic rollback on migration failure
 *    - Manual rollback capabilities
 *    - Backup integrity verification
 *    - Data restoration with validation
 * 
 * Usage Examples:
 * 
 * ```typescript
 * // Execute migration with default settings
 * const executor = new DataMigrationExecutor();
 * const result = await executor.executeMigration();
 * 
 * // Validate migration integrity
 * const validator = new MigrationValidator();
 * const validation = await validator.validateMigration();
 * 
 * // Rollback if needed
 * const rollbackManager = new RollbackManager();
 * await rollbackManager.executeRollback(backupPath);
 * ```
 * 
 * CLI Usage:
 * 
 * ```bash
 * # Execute migration
 * npm run migrate:execute execute
 * 
 * # Check status
 * npm run migrate:status
 * 
 * # Dry run validation
 * npm run migrate:dry-run
 * 
 * # Rollback
 * npm run migrate:rollback
 * ```
 */
