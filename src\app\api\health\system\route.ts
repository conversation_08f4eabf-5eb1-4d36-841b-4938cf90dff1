import { NextRequest, NextResponse } from 'next/server';
import { errorHandlingSystem } from '@/lib/error-handling';

/**
 * System Health Check API
 * Provides comprehensive system health status
 */
export async function GET(request: NextRequest) {
  try {
    const startTime = Date.now();

    // Run comprehensive health checks
    const healthStatus = await errorHandlingSystem.healthChecker.runAllHealthChecks();
    
    const responseTime = Date.now() - startTime;

    // Get error metrics from the last hour
    const errorMetrics = errorHandlingSystem.errorManager.getErrorMetrics();
    const activeAlerts = errorHandlingSystem.errorManager.getActiveAlerts();

    // Calculate system health score
    const healthScore = calculateHealthScore(healthStatus, errorMetrics, activeAlerts);

    // Determine overall status
    let overallStatus = healthStatus.overall;
    if (healthScore < 50) {
      overallStatus = 'unhealthy';
    } else if (healthScore < 80) {
      overallStatus = 'degraded';
    }

    return NextResponse.json({
      status: overallStatus,
      responseTime,
      timestamp: new Date().toISOString(),
      healthScore,
      systemHealth: {
        overall: healthStatus.overall,
        summary: healthStatus.summary,
        criticalIssues: healthStatus.criticalIssues,
        recommendations: healthStatus.recommendations
      },
      componentHealth: healthStatus.checks,
      errorMetrics: {
        totalErrorTypes: errorMetrics.size,
        activeAlerts: activeAlerts.length,
        criticalAlerts: activeAlerts.filter((a: any) => a.severity === 'critical').length
      },
      alerts: activeAlerts.map((alert: any) => ({
        id: alert.id,
        type: alert.type,
        severity: alert.severity,
        message: alert.message,
        timestamp: alert.timestamp,
        acknowledged: alert.acknowledged
      })),
      uptime: {
        process: Math.floor(process.uptime()),
        system: getSystemUptime()
      },
      resources: {
        memory: getMemoryUsage(),
        cpu: getCpuUsage()
      }
    });

  } catch (error: any) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 503 }
    );
  }
}

/**
 * Calculate overall health score based on various metrics
 */
function calculateHealthScore(
  healthStatus: any,
  errorMetrics: Map<string, any>,
  activeAlerts: any[]
): number {
  let score = 100;

  // Deduct points for unhealthy components
  const unhealthyComponents = healthStatus.summary.unhealthy;
  const degradedComponents = healthStatus.summary.degraded;
  
  score -= unhealthyComponents * 20;
  score -= degradedComponents * 10;

  // Deduct points for active alerts
  const criticalAlerts = activeAlerts.filter(a => a.severity === 'critical').length;
  const highAlerts = activeAlerts.filter(a => a.severity === 'high').length;
  
  score -= criticalAlerts * 15;
  score -= highAlerts * 5;

  // Deduct points for high error frequency
  let totalRecentErrors = 0;
  for (const metrics of errorMetrics.values()) {
    totalRecentErrors += metrics.frequency || 0;
  }
  
  if (totalRecentErrors > 10) {
    score -= Math.min(totalRecentErrors - 10, 20);
  }

  return Math.max(score, 0);
}

/**
 * Get system uptime (placeholder - would need OS-specific implementation)
 */
function getSystemUptime(): number {
  // In a real implementation, this would get actual system uptime
  return Math.floor(process.uptime());
}

/**
 * Get memory usage information
 */
function getMemoryUsage(): object {
  const memoryUsage = process.memoryUsage();
  return {
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
    external: Math.round(memoryUsage.external / 1024 / 1024), // MB
    rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
    usagePercentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)
  };
}

/**
 * Get CPU usage information (placeholder)
 */
function getCpuUsage(): object {
  // In a real implementation, this would calculate actual CPU usage
  return {
    percentage: Math.floor(Math.random() * 20) + 10, // Placeholder: 10-30%
    loadAverage: process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0]
  };
}
