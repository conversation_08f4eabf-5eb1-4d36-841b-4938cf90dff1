'use client';

import React from 'react';

export function FooterIllustration() {
  return (
    <div className="relative bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 py-16 px-6 overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-10 left-10 w-16 h-16 bg-white rounded-full animate-pulse"></div>
        <div className="absolute top-20 right-20 w-12 h-12 bg-yellow-300 rounded-full animate-bounce"></div>
        <div className="absolute bottom-20 left-1/4 w-20 h-20 bg-green-300 rounded-full animate-pulse"></div>
        <div className="absolute bottom-10 right-1/3 w-14 h-14 bg-red-300 rounded-full animate-bounce"></div>
      </div>

      {/* AI-themed Cityscape */}
      <div className="relative z-10 mx-auto" style={{ maxWidth: 'var(--container-width)' }}>
        <div className="flex items-end justify-center space-x-4 mb-8">
          {/* Buildings representing AI tools */}
          <div className="bg-white bg-opacity-20 w-16 h-32 rounded-t-lg"></div>
          <div className="bg-white bg-opacity-30 w-20 h-40 rounded-t-lg"></div>
          <div className="bg-white bg-opacity-25 w-24 h-48 rounded-t-lg"></div>
          <div className="bg-white bg-opacity-35 w-18 h-36 rounded-t-lg"></div>
          <div className="bg-white bg-opacity-20 w-16 h-28 rounded-t-lg"></div>
        </div>

        {/* Bookmark Me Banner */}
        <div className="text-center">
          <div className="inline-block bg-white bg-opacity-90 px-8 py-4 rounded-lg wave-animation">
            <h2 className="text-3xl font-bold text-black shine-effect">
              BOOKMARK ME!
            </h2>
          </div>
        </div>

        {/* Data streams */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full pointer-events-none">
          <div className="absolute top-0 left-0 w-2 h-full bg-gradient-to-b from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
          <div className="absolute top-0 right-0 w-2 h-full bg-gradient-to-b from-transparent via-white to-transparent opacity-30 animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>
      </div>
    </div>
  );
}
