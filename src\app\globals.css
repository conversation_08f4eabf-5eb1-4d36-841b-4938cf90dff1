@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
@import "tailwindcss";

/* Dark theme for AI Tools Directory */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: #18181b; /* zinc-900 */
  color: #f4f4f5; /* zinc-100 */
  font-family: 'Roboto', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* Custom scrollbar styles for category lists with auto-hide */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent; /* Hidden by default */
  transition: scrollbar-color 0.3s ease;
}

.custom-scrollbar:hover {
  scrollbar-color: #52525b #27272a; /* zinc-600 on zinc-800 - show on hover */
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent; /* Hidden by default */
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: transparent; /* Hidden by default */
  border-radius: 3px;
  transition: background 0.3s ease;
}

.custom-scrollbar:hover::-webkit-scrollbar-track {
  background: #27272a; /* zinc-800 - show on hover */
}

.custom-scrollbar:hover::-webkit-scrollbar-thumb {
  background: #52525b; /* zinc-600 - show on hover */
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #71717a; /* zinc-500 */
}

/* Enhanced animations based on theporndude.com analysis */
@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes pulse-separator {
  0%, 100% {
    opacity: 1;
    transform: scaleX(1);
  }
  50% {
    opacity: 0.9;
    transform: scaleX(1.02);
  }
}

@keyframes wave {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes subtle-bounce {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(var(--glow-color), 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(var(--glow-color), 0.6);
  }
}

.shine-effect {
  position: relative;
  overflow: hidden;
}

.shine-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shine 2s infinite;
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.wave-animation {
  animation: wave 3s ease-in-out infinite;
}

/* Tooltip pointer styles - Enhanced for theporndude.com styling */
.tooltip-pointer {
  position: absolute;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
}

.tooltip-pointer.top {
  border-top: 6px solid #27272a; /* zinc-800 */
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.tooltip-pointer.bottom {
  border-bottom: 6px solid #27272a; /* zinc-800 */
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.tooltip-pointer.left {
  border-left: 6px solid #27272a; /* zinc-800 */
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

.tooltip-pointer.right {
  border-right: 6px solid #27272a; /* zinc-800 */
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

/* Enhanced separator animations based on theporndude.com analysis */
.enhanced-separator {
  transition: all 0.3s ease-out;
  transform-origin: center;
}

.enhanced-separator.active {
  animation: pulse-separator 2s ease-in-out infinite;
}

.enhanced-separator.glow {
  --glow-color: var(--separator-color, 63, 63, 70);
  animation: glow-pulse 2s ease-in-out infinite;
}

/* Enhanced card hover effects */
.enhanced-card {
  transition: all 0.25s ease-out;
  transform-origin: center;
}

.enhanced-card:hover {
  transform: translateY(-2px) scale(1.01);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.enhanced-card.subtle-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Enhanced button animations */
.enhanced-button {
  transition: all 0.2s ease-out;
  transform-origin: center;
}

.enhanced-button:hover {
  transform: scale(1.02);
  filter: brightness(1.1);
}

.enhanced-button:active {
  transform: scale(0.98);
  transition: all 0.1s ease-out;
}

/* Enhanced icon animations */
.enhanced-icon {
  transition: all 0.25s ease-out;
}

.enhanced-icon:hover {
  animation: subtle-bounce 0.6s ease-in-out;
}

/* Dynamic underline effect for category titles */
.category-title-underline::after {
  background-color: var(--underline-color, #3f3f46);
}

/* Enhanced hover effect for category titles */
.category-title-hover {
  transition: all 0.25s ease-out;
}

.category-title-hover:hover {
  transform: translateY(-1px);
  filter: brightness(1.1);
}

/* Smooth image scaling effects */
.enhanced-image {
  transition: transform 0.3s ease-out;
  transform-origin: center center;
}

.enhanced-image:hover {
  transform: scale(1.05);
}

/* Enhanced opacity transitions */
.enhanced-fade {
  transition: opacity 0.25s ease-out, visibility 0.25s ease-out;
}

.enhanced-fade.hidden {
  opacity: 0;
  visibility: hidden;
}

/* ThePortnDude.com Style Implementation - Exact Replication */
:root {
  /* Header Layout Variables */
  --top-bar-height: 34px;
  --top-bar-border-width: 3px;
  --header-mob-height: 70px;
  --header-desk-height: 210px;
  --container-width: 1150px;

  /* Color variables */
  --tpd-body-bg: rgb(118, 118, 118);
  --tpd-card-bg: rgb(253, 253, 253);
  --tpd-card-hover-bg: rgb(248, 248, 248);
  --tpd-text-primary: rgb(78, 78, 78);
  --tpd-text-secondary: rgb(69, 69, 69);
  --tpd-text-on-color: rgb(255, 255, 255);
  --tpd-border-base: rgba(118, 118, 118, 0.5);
  --tpd-button-text: rgb(255, 255, 255);

  /* Typography variables */
  --tpd-font-family: 'Roboto', sans-serif;
  --tpd-header-size: 14px;
  --tpd-header-weight: 500;
  --tpd-header-line-height: 47px;
  --tpd-desc-size: 16px;
  --tpd-desc-weight: 400;
  --tpd-desc-line-height: 19.2px;
  --tpd-button-size: 14px;
  --tpd-button-weight: 400;
  --tpd-button-line-height: 16.8px;

  /* Spacing variables - Exact measurements from theporndude.com */
  --tpd-card-padding: 0px;
  --tpd-card-padding-bottom: 45px;
  --tpd-card-border-radius: 10px;
  --tpd-card-border-width: 2.82828px; /* Exact border width */
  --tpd-button-padding: 4px 6px;
  --tpd-button-border-radius: 10px;
  --tpd-divider-thickness: 2.99874px; /* Exact divider thickness */

  /* Animation variables */
  --tpd-transition-fast: 0.15s;
  --tpd-transition-medium: 0.25s;
  --tpd-transition-slow: 0.4s;
  --tpd-ease-out: ease-out;
  --tpd-ease-in-out: ease-in-out;
}

/* Pulse animation from theporndude.com */
@keyframes tpd-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes tpd-fadeEffect {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Dark Theme Top Bar Styles */
.dark-theme-top-bar {
  background-color: #2a2b31;
  border-color: #151515;
  color: #fff;
}

.dark-theme-top-bar h1 {
  color: #fff;
}

.dark-theme-top-bar b {
  color: #fff;
}

/* ThePortnDude.com Card Style */
.tpd-card {
  background-color: var(--tpd-card-bg);
  border: var(--tpd-card-border-width) solid;
  border-color: var(--category-border-color, var(--tpd-border-base));
  border-radius: var(--tpd-card-border-radius);
  padding: var(--tpd-card-padding);
  padding-bottom: var(--tpd-card-padding-bottom);
  font-family: var(--tpd-font-family);
  transition: all var(--tpd-transition-medium) var(--tpd-ease-out);
  position: relative;
  overflow: hidden;
}

.tpd-card:hover {
  background-color: var(--tpd-card-hover-bg);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* ThePortnDude.com Header Style with Exact Divider Animation */
.tpd-header {
  background-color: var(--category-color, var(--tpd-border-base));
  color: var(--tpd-text-on-color);
  font-size: var(--tpd-header-size);
  font-weight: var(--tpd-header-weight);
  line-height: var(--tpd-header-line-height);
  text-align: center;
  text-transform: uppercase;
  margin: 0;
  padding: 1px 5px; /* Exact padding from theporndude.com */
  border-radius: var(--tpd-card-border-radius) var(--tpd-card-border-radius) 0 0;
  margin: calc(var(--tpd-card-border-width) * -1);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 47px;
  position: relative; /* Required for ::after pseudo-element */
}

/* Exact Divider Line Animation from ThePortnDude.com */
.tpd-header::after {
  content: "";
  position: absolute;
  width: 55px; /* Initial small width */
  height: var(--tpd-divider-thickness); /* Exact thickness: 2.99874px */
  background-color: var(--category-color, var(--tpd-border-base));
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%); /* Center the line */
  transition: width 0.3s ease; /* Exact 300ms transition */
}

/* Divider expands on card hover */
.tpd-card:hover .tpd-header::after {
  width: calc(100% - 25px); /* Expands to nearly full width */
}

/* ThePortnDude.com Description Style */
.tpd-description {
  color: var(--tpd-text-on-color);
  font-size: var(--tpd-desc-size);
  font-weight: var(--tpd-desc-weight);
  line-height: var(--tpd-desc-line-height);
  background-color: var(--category-color, var(--tpd-border-base));
  padding: 8px 16px;
  margin: 0 calc(var(--tpd-card-border-width) * -1) 16px;
  text-align: center;
}

/* ThePortnDude.com Content Area */
.tpd-content {
  padding: 0 16px;
  color: var(--tpd-text-primary);
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* ThePortnDude.com Button Style with Exact Pulse Animation */
.tpd-button {
  background-color: var(--category-color, var(--tpd-border-base));
  color: var(--tpd-button-text);
  font-size: var(--tpd-button-size);
  font-weight: var(--tpd-button-weight);
  line-height: var(--tpd-button-line-height);
  padding: var(--tpd-button-padding);
  border-radius: var(--tpd-button-border-radius);
  border: none;
  text-transform: capitalize;
  text-align: center;
  cursor: pointer;
  font-family: var(--tpd-font-family);
  width: calc(100% - 32px);
  margin: 0 16px;
  margin-top: auto;
  position: absolute;
  bottom: 16px;
  left: 0;
  right: 0;
  /* Exact animations from theporndude.com */
  animation: 0.2s ease 0s 1 normal none running tpd-fadeEffect;
  transition: all var(--tpd-transition-fast) var(--tpd-ease-out);
}

.tpd-button:hover {
  animation: 1s ease 0s infinite normal none running tpd-pulse;
}

.tpd-button:active {
  transform: scale(0.98);
}

/* ThePortnDude.com Tool List */
.tpd-tool-list {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
  overflow-y: auto;
  max-height: 300px;
}

.tpd-tool-item {
  padding: 8px 0;
  border-bottom: 1px solid rgba(78, 78, 78, 0.1);
  font-size: 13px;
  color: var(--tpd-text-primary);
  transition: color var(--tpd-transition-fast) var(--tpd-ease-out);
}

.tpd-tool-item:hover {
  color: var(--category-color, var(--tpd-border-base));
}

.tpd-tool-item:last-child {
  border-bottom: none;
}

/* Image Quality Optimization */
.optimized-image {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: pixelated;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* High DPI Image Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .optimized-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: auto;
  }
}

/* Line clamp utilities for tool detail pages */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
