'use client';

import React from 'react';
import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  href: string;
}

interface CategoryBreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function CategoryBreadcrumb({ items, className = '' }: CategoryBreadcrumbProps) {
  if (items.length === 0) return null;

  return (
    <nav 
      className={`flex items-center space-x-2 text-sm ${className}`}
      aria-label="Breadcrumb"
    >
      {items.map((item, index) => {
        const isLast = index === items.length - 1;
        const isHome = index === 0;
        
        return (
          <React.Fragment key={item.href}>
            {index > 0 && (
              <ChevronRight 
                size={14} 
                className="text-gray-500 flex-shrink-0" 
                aria-hidden="true"
              />
            )}
            
            {isLast ? (
              <span 
                className="text-white font-medium flex items-center gap-1"
                aria-current="page"
              >
                {isHome && <Home size={14} />}
                {item.label}
              </span>
            ) : (
              <Link
                href={item.href}
                className="text-gray-400 hover:text-white transition-colors duration-200 flex items-center gap-1"
              >
                {isHome && <Home size={14} />}
                {item.label}
              </Link>
            )}
          </React.Fragment>
        );
      })}
    </nav>
  );
}
