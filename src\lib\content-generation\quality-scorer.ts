/**
 * Quality Scoring System
 * 
 * Evaluates content quality across multiple dimensions and provides
 * actionable feedback for content improvement.
 */

import { QualityScore, ValidationResult, QualityMetrics, QualityFlag } from '../types';

export interface ScoringCriteria {
  completeness: number;      // 0-100: How complete is the content
  accuracy: number;          // 0-100: How accurate is the information
  engagement: number;        // 0-100: How engaging is the writing
  structure: number;         // 0-100: How well structured is the content
  relevance: number;         // 0-100: How relevant to the tool
  consistency: number;       // 0-100: Internal consistency
}

export interface QualityThresholds {
  excellent: number;         // 90+
  good: number;             // 75+
  acceptable: number;       // 60+
  poor: number;             // Below 60
}

export class QualityScorer {
  private thresholds: QualityThresholds;
  private weights: ScoringCriteria;

  constructor() {
    this.thresholds = {
      excellent: 90,
      good: 75,
      acceptable: 60,
      poor: 60
    };

    // Weights for different quality aspects (should sum to 1.0)
    this.weights = {
      completeness: 0.25,    // 25% - Most important
      accuracy: 0.20,        // 20% - Very important
      engagement: 0.15,      // 15% - Important for user experience
      structure: 0.15,       // 15% - Important for readability
      relevance: 0.15,       // 15% - Important for accuracy
      consistency: 0.10      // 10% - Important but less critical
    };
  }

  /**
   * Main quality scoring method
   */
  async scoreContent(
    content: any,
    validation?: ValidationResult
  ): Promise<QualityScore> {
    try {
      // Calculate individual quality metrics
      const metrics = await this.calculateQualityMetrics(content, validation);
      
      // Calculate weighted overall score
      const overall = this.calculateOverallScore(metrics);
      
      // Identify quality flags
      const flags = this.identifyQualityFlags(content, metrics, validation);
      
      // Generate improvement suggestions
      const suggestions = this.generateSuggestions(metrics, flags);
      
      // Determine quality level
      const level = this.determineQualityLevel(overall);

      return {
        overall,
        breakdown: metrics,
        level,
        flags,
        suggestions,
        scoredAt: new Date().toISOString(),
        version: '1.0'
      };

    } catch (error: any) {
      console.error('Quality scoring failed:', error);
      
      return {
        overall: 0,
        breakdown: {
          completeness: 0,
          accuracy: 0,
          engagement: 0,
          structure: 0,
          relevance: 0,
          consistency: 0
        },
        level: 'poor',
        flags: [{
          type: 'error',
          severity: 'high',
          message: `Quality scoring failed: ${error.message}`,
          field: 'scoring_system'
        }],
        suggestions: ['Content could not be scored due to system error'],
        scoredAt: new Date().toISOString(),
        version: '1.0'
      };
    }
  }

  /**
   * Calculate detailed quality metrics
   */
  private async calculateQualityMetrics(
    content: any,
    validation?: ValidationResult
  ): Promise<QualityMetrics> {
    const completeness = this.scoreCompleteness(content);
    const accuracy = this.scoreAccuracy(content, validation);
    const engagement = this.scoreEngagement(content);
    const structure = this.scoreStructure(content);
    const relevance = this.scoreRelevance(content);
    const consistency = this.scoreConsistency(content);

    return {
      completeness,
      accuracy,
      engagement,
      structure,
      relevance,
      consistency
    };
  }

  /**
   * Score content completeness
   */
  private scoreCompleteness(content: any): number {
    let score = 0;
    const maxScore = 100;
    
    // Required fields check (40 points)
    const requiredFields = ['detailed_description', 'features', 'pricing', 'pros_and_cons'];
    const presentFields = requiredFields.filter(field => content[field]);
    score += (presentFields.length / requiredFields.length) * 40;

    // Optional fields check (30 points)
    const optionalFields = ['haiku', 'hashtags', 'social_links'];
    const presentOptionalFields = optionalFields.filter(field => content[field]);
    score += (presentOptionalFields.length / optionalFields.length) * 30;

    // Content depth check (30 points)
    if (content.detailed_description && content.detailed_description.length >= 150) {
      score += 10;
    }
    if (content.features && content.features.length >= 3) {
      score += 10;
    }
    if (content.pros_and_cons && content.pros_and_cons.pros && content.pros_and_cons.cons) {
      score += 10;
    }

    return Math.min(score, maxScore);
  }

  /**
   * Score content accuracy
   */
  private scoreAccuracy(content: any, validation?: ValidationResult): number {
    let score = 100;

    // Deduct points for validation errors
    if (validation) {
      score -= validation.errors.length * 15;
      score -= validation.warnings.length * 5;
    }

    // Check for obvious inaccuracies
    if (content.detailed_description) {
      const desc = content.detailed_description.toLowerCase();
      
      // Deduct for placeholder content
      if (desc.includes('placeholder') || desc.includes('lorem ipsum')) {
        score -= 30;
      }
      
      // Deduct for vague descriptions
      if (desc.includes('various') && desc.includes('different') && desc.includes('multiple')) {
        score -= 10;
      }
    }

    // Check pricing accuracy
    if (content.pricing && content.pricing.type) {
      const validTypes = ['Free', 'Paid', 'Freemium', 'Open Source'];
      if (!validTypes.includes(content.pricing.type)) {
        score -= 20;
      }
    }

    return Math.max(0, score);
  }

  /**
   * Score content engagement
   */
  private scoreEngagement(content: any): number {
    let score = 0;

    if (content.detailed_description) {
      const desc = content.detailed_description;
      
      // Length appropriateness (20 points)
      if (desc.length >= 150 && desc.length <= 500) {
        score += 20;
      } else if (desc.length > 500 && desc.length <= 1000) {
        score += 15;
      } else if (desc.length > 100) {
        score += 10;
      }

      // Sentence variety (15 points)
      const sentences = desc.split(/[.!?]+/).filter((s: string) => s.trim().length > 0);
      if (sentences.length >= 3) {
        const avgLength = sentences.reduce((sum: number, s: string) => sum + s.length, 0) / sentences.length;
        if (avgLength >= 20 && avgLength <= 100) {
          score += 15;
        } else {
          score += 8;
        }
      }

      // Engaging language (15 points)
      const engagingWords = ['innovative', 'powerful', 'efficient', 'intuitive', 'advanced', 'cutting-edge'];
      const foundEngagingWords = engagingWords.filter(word => 
        desc.toLowerCase().includes(word)
      ).length;
      score += Math.min(foundEngagingWords * 3, 15);
    }

    // Haiku creativity (20 points)
    if (content.haiku && content.haiku.lines && content.haiku.lines.length === 3) {
      score += 20;
    }

    // Features engagement (15 points)
    if (content.features && Array.isArray(content.features)) {
      const avgFeatureLength = content.features.reduce((sum: number, f: string) => sum + f.length, 0) / content.features.length;
      if (avgFeatureLength >= 20 && avgFeatureLength <= 100) {
        score += 15;
      } else if (avgFeatureLength >= 10) {
        score += 8;
      }
    }

    // Hashtags relevance (15 points)
    if (content.hashtags && Array.isArray(content.hashtags) && content.hashtags.length >= 3) {
      score += 15;
    }

    return Math.min(score, 100);
  }

  /**
   * Score content structure
   */
  private scoreStructure(content: any): number {
    let score = 0;

    // JSON structure validity (30 points)
    try {
      JSON.stringify(content);
      score += 30;
    } catch {
      return 0;
    }

    // Required structure completeness (40 points)
    const structureChecks = [
      content.detailed_description && typeof content.detailed_description === 'string',
      content.features && Array.isArray(content.features),
      content.pricing && typeof content.pricing === 'object',
      content.pros_and_cons && content.pros_and_cons.pros && content.pros_and_cons.cons
    ];
    
    score += (structureChecks.filter(Boolean).length / structureChecks.length) * 40;

    // Array structure quality (30 points)
    if (content.features && Array.isArray(content.features) && content.features.length > 0) {
      score += 10;
    }
    if (content.hashtags && Array.isArray(content.hashtags) && content.hashtags.length > 0) {
      score += 10;
    }
    if (content.pros_and_cons && content.pros_and_cons.pros && content.pros_and_cons.cons) {
      score += 10;
    }

    return Math.min(score, 100);
  }

  /**
   * Score content relevance
   */
  private scoreRelevance(content: any): number {
    let score = 80; // Start with good baseline

    // Check for AI/tech relevance
    if (content.detailed_description) {
      const desc = content.detailed_description.toLowerCase();
      const aiKeywords = ['ai', 'artificial intelligence', 'machine learning', 'automation', 'algorithm', 'neural', 'deep learning'];
      const foundKeywords = aiKeywords.filter(keyword => desc.includes(keyword)).length;
      
      if (foundKeywords >= 2) {
        score += 20;
      } else if (foundKeywords >= 1) {
        score += 10;
      } else {
        score -= 10;
      }
    }

    return Math.min(score, 100);
  }

  /**
   * Score internal consistency
   */
  private scoreConsistency(content: any): number {
    let score = 100;

    // Check tone consistency between description and features
    if (content.detailed_description && content.features) {
      // This is a simplified check - in practice, you might use NLP
      const descTone = this.analyzeTone(content.detailed_description);
      const featuresTone = this.analyzeTone(content.features.join(' '));
      
      if (Math.abs(descTone - featuresTone) > 0.3) {
        score -= 15;
      }
    }

    // Check for contradictory information
    if (content.pricing && content.pricing.type === 'Free' && content.pros_and_cons) {
      const consText = content.pros_and_cons.cons.join(' ').toLowerCase();
      if (consText.includes('expensive') || consText.includes('costly')) {
        score -= 20;
      }
    }

    return Math.max(0, score);
  }

  /**
   * Calculate weighted overall score
   */
  private calculateOverallScore(metrics: QualityMetrics): number {
    return Math.round(
      metrics.completeness * this.weights.completeness +
      metrics.accuracy * this.weights.accuracy +
      metrics.engagement * this.weights.engagement +
      metrics.structure * this.weights.structure +
      metrics.relevance * this.weights.relevance +
      metrics.consistency * this.weights.consistency
    );
  }

  /**
   * Identify quality flags
   */
  private identifyQualityFlags(
    content: any,
    metrics: QualityMetrics,
    validation?: ValidationResult
  ): QualityFlag[] {
    const flags: QualityFlag[] = [];

    // Low completeness flag
    if (metrics.completeness < 60) {
      flags.push({
        type: 'completeness',
        severity: 'high',
        message: 'Content is missing required fields or lacks depth',
        field: 'general'
      });
    }

    // Low accuracy flag
    if (metrics.accuracy < 70) {
      flags.push({
        type: 'accuracy',
        severity: 'high',
        message: 'Content may contain inaccurate or placeholder information',
        field: 'detailed_description'
      });
    }

    // Validation error flags
    if (validation && validation.errors.length > 0) {
      flags.push({
        type: 'validation',
        severity: 'high',
        message: `${validation.errors.length} validation errors found`,
        field: 'schema'
      });
    }

    return flags;
  }

  /**
   * Generate improvement suggestions
   */
  private generateSuggestions(metrics: QualityMetrics, flags: QualityFlag[]): string[] {
    const suggestions: string[] = [];

    if (metrics.completeness < 80) {
      suggestions.push('Add more detailed information to improve completeness');
    }

    if (metrics.engagement < 70) {
      suggestions.push('Use more engaging language and varied sentence structure');
    }

    if (metrics.structure < 80) {
      suggestions.push('Improve content organization and structure');
    }

    if (flags.length > 0) {
      suggestions.push('Address validation errors and quality flags');
    }

    return suggestions;
  }

  /**
   * Determine quality level based on overall score
   */
  private determineQualityLevel(score: number): 'excellent' | 'good' | 'acceptable' | 'poor' {
    if (score >= this.thresholds.excellent) return 'excellent';
    if (score >= this.thresholds.good) return 'good';
    if (score >= this.thresholds.acceptable) return 'acceptable';
    return 'poor';
  }

  /**
   * Simple tone analysis (placeholder for more sophisticated NLP)
   */
  private analyzeTone(text: string): number {
    const positiveWords = ['great', 'excellent', 'amazing', 'powerful', 'innovative'];
    const negativeWords = ['bad', 'poor', 'limited', 'difficult', 'complex'];
    
    const words = text.toLowerCase().split(/\s+/);
    const positiveCount = words.filter(word => positiveWords.includes(word)).length;
    const negativeCount = words.filter(word => negativeWords.includes(word)).length;
    
    return (positiveCount - negativeCount) / words.length;
  }
}
