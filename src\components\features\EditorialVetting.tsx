'use client';

import React from 'react';
import { Info } from 'lucide-react';

interface EditorialVettingProps {
  toolName: string;
  featuredDate?: string;
}

export function EditorialVetting({ toolName, featuredDate = "August 7th 2024" }: EditorialVettingProps) {
  return (
    <section className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
      <div className="flex items-start gap-3">
        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
          <Info size={16} className="text-white" />
        </div>
        <div>
          <p className="text-gray-300 text-sm leading-relaxed">
            {toolName} was manually vetted by our editorial team and was first featured on {featuredDate}.
          </p>
        </div>
      </div>
    </section>
  );
}
