'use client';

import React from 'react';
import Link from 'next/link';
import { ExternalLink, ArrowRight } from 'lucide-react';
import { AITool } from '@/lib/types';
import { AI_CATEGORIES } from '@/lib/constants';
import { ResponsiveImage } from '@/components/ui/ResponsiveImage';
import { Tag } from '@/components/ui/Tag';

interface RelatedToolsProps {
  currentTool: AITool;
}

export function RelatedTools({ currentTool }: RelatedToolsProps) {
  // Find tools in the same category, excluding the current tool
  const getRelatedTools = (): AITool[] => {
    const currentCategory = AI_CATEGORIES.find(cat => cat.id === currentTool.category);
    if (!currentCategory) return [];
    
    return currentCategory.tools
      .filter(tool => tool.id !== currentTool.id)
      .slice(0, 4); // Show up to 4 related tools
  };

  const relatedTools = getRelatedTools();

  if (relatedTools.length === 0) {
    return null;
  }

  return (
    <section className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-white">Related Tools</h2>
        <Link
          href={`/category/${currentTool.category}`}
          className="text-orange-400 hover:text-orange-300 transition-colors duration-200 flex items-center gap-1 text-sm"
        >
          View all
          <ArrowRight size={14} />
        </Link>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {relatedTools.map((tool) => (
          <Link
            key={tool.id}
            href={`/tools/${tool.id}`}
            className="group block"
          >
            <div className="bg-zinc-700 border border-zinc-600 rounded-lg p-4 hover:bg-zinc-600 hover:border-zinc-500 transition-all duration-200 h-full">
              
              {/* Tool Header */}
              <div className="flex items-start gap-3 mb-3">
                <ResponsiveImage
                  src={tool.logoUrl}
                  alt={`${tool.name} logo`}
                  width={40}
                  height={40}
                  className="rounded-lg border border-zinc-500 flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <h3 className="text-white font-medium text-sm group-hover:text-orange-400 transition-colors duration-200 truncate">
                    {tool.name}
                  </h3>
                  {tool.tags && tool.tags.length > 0 && (
                    <div className="flex gap-1 mt-1">
                      {tool.tags.slice(0, 2).map((tag, index) => (
                        <Tag
                          key={index}
                          type={tag.type}
                          label={tag.label}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>
              
              {/* Tool Description */}
              <p className="text-gray-400 text-xs leading-relaxed mb-3 line-clamp-3">
                {tool.description}
              </p>
              
              {/* Tool Footer */}
              <div className="flex items-center justify-between">
                <span className="text-gray-500 text-xs capitalize">
                  {tool.category.replace('-', ' ')}
                </span>
                <ExternalLink 
                  size={14} 
                  className="text-gray-500 group-hover:text-orange-400 transition-colors duration-200" 
                />
              </div>
              
            </div>
          </Link>
        ))}
      </div>
      
      {/* Category Info */}
      <div className="mt-6 pt-4 border-t border-zinc-600 text-center">
        <p className="text-gray-400 text-sm">
          Explore more tools in the{' '}
          <Link
            href={`/category/${currentTool.category}`}
            className="text-orange-400 hover:text-orange-300 transition-colors duration-200 font-medium"
          >
            {currentTool.category.replace('-', ' ')}
          </Link>
          {' '}category
        </p>
      </div>
    </section>
  );
}
