import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { validateApi<PERSON>ey } from '@/lib/auth';

/**
 * GET /api/admin/editorial/submissions
 * Retrieve all tool submissions for editorial review
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build query
    let query = supabase
      .from('tool_submissions')
      .select('*')
      .order('submitted_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    const { data: submissions, error } = await query;

    if (error) {
      console.error('Failed to fetch submissions:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch submissions' },
        { status: 500 }
      );
    }

    // Transform data to match frontend interface
    const transformedSubmissions = submissions?.map(submission => ({
      id: submission.id,
      name: submission.name,
      url: submission.url,
      description: submission.description,
      category: submission.category,
      subcategory: submission.subcategory,
      submitterName: submission.submitter_name,
      submitterEmail: submission.submitter_email,
      status: submission.status,
      submittedAt: submission.submitted_at,
      reviewedAt: submission.reviewed_at,
      reviewNotes: submission.review_notes,
      priority: submission.priority || 'normal',
      pricingType: submission.pricing_type
    })) || [];

    return NextResponse.json({
      success: true,
      submissions: transformedSubmissions,
      total: submissions?.length || 0
    });

  } catch (error) {
    console.error('Editorial submissions API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/editorial/submissions
 * Create a new tool submission (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate required fields
    const { name, url, description, category, submitterEmail } = body;
    
    if (!name || !url || !description || !category || !submitterEmail) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: name, url, description, category, submitterEmail' 
        },
        { status: 400 }
      );
    }

    // Validate URL format
    try {
      new URL(url);
    } catch {
      return NextResponse.json(
        { success: false, error: 'Invalid URL format' },
        { status: 400 }
      );
    }

    // Create submission record
    const submissionData = {
      name,
      url,
      description,
      category,
      subcategory: body.subcategory || null,
      submitter_name: body.submitterName || 'Admin',
      submitter_email: submitterEmail,
      pricing_type: body.pricingType || null,
      status: 'pending',
      priority: body.priority || 'normal',
      submitted_at: new Date().toISOString()
    };

    const { data: submission, error } = await supabase
      .from('tool_submissions')
      .insert(submissionData)
      .select()
      .single();

    if (error) {
      console.error('Failed to create submission:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create submission' },
        { status: 500 }
      );
    }

    // Transform response data
    const transformedSubmission = {
      id: submission.id,
      name: submission.name,
      url: submission.url,
      description: submission.description,
      category: submission.category,
      subcategory: submission.subcategory,
      submitterName: submission.submitter_name,
      submitterEmail: submission.submitter_email,
      status: submission.status,
      submittedAt: submission.submitted_at,
      priority: submission.priority,
      pricingType: submission.pricing_type
    };

    return NextResponse.json({
      success: true,
      submission: transformedSubmission,
      message: 'Submission created successfully'
    });

  } catch (error) {
    console.error('Create submission API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/editorial/submissions
 * Update submission status and review information
 */
export async function PUT(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { submissionId, status, reviewNotes, priority, reviewedBy } = body;

    if (!submissionId) {
      return NextResponse.json(
        { success: false, error: 'Submission ID is required' },
        { status: 400 }
      );
    }

    // Update submission
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (status) updateData.status = status;
    if (reviewNotes) updateData.review_notes = reviewNotes;
    if (priority) updateData.priority = priority;
    if (reviewedBy) updateData.reviewed_by = reviewedBy;
    
    // Set reviewed_at timestamp if status is being changed from pending
    if (status && status !== 'pending') {
      updateData.reviewed_at = new Date().toISOString();
    }

    const { data: submission, error } = await supabase
      .from('tool_submissions')
      .update(updateData)
      .eq('id', submissionId)
      .select()
      .single();

    if (error) {
      console.error('Failed to update submission:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update submission' },
        { status: 500 }
      );
    }

    // Transform response data
    const transformedSubmission = {
      id: submission.id,
      name: submission.name,
      url: submission.url,
      description: submission.description,
      category: submission.category,
      subcategory: submission.subcategory,
      submitterName: submission.submitter_name,
      submitterEmail: submission.submitter_email,
      status: submission.status,
      submittedAt: submission.submitted_at,
      reviewedAt: submission.reviewed_at,
      reviewNotes: submission.review_notes,
      priority: submission.priority,
      pricingType: submission.pricing_type
    };

    return NextResponse.json({
      success: true,
      submission: transformedSubmission,
      message: 'Submission updated successfully'
    });

  } catch (error) {
    console.error('Update submission API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/editorial/submissions
 * Delete a submission (admin only)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const submissionId = searchParams.get('id');

    if (!submissionId) {
      return NextResponse.json(
        { success: false, error: 'Submission ID is required' },
        { status: 400 }
      );
    }

    const { error } = await supabase
      .from('tool_submissions')
      .delete()
      .eq('id', submissionId);

    if (error) {
      console.error('Failed to delete submission:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to delete submission' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Submission deleted successfully'
    });

  } catch (error) {
    console.error('Delete submission API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
