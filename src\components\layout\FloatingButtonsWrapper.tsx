'use client';

import React from 'react';
import { ArrowUp, ArrowDown, MessageCircle } from 'lucide-react';
import { useScrollPosition } from '@/hooks/useScrollPosition';

export function FloatingButtonsWrapper() {
  const { isScrollToTopVisible, scrollProgress, isAboveFold, scrollToTop, scrollToBottom } = useScrollPosition();

  return (
    <>
      {/* Scroll Indicator Button - Shows above fold with down arrow, below fold with up arrow */}
      <button
        onClick={isAboveFold ? scrollToBottom : scrollToTop}
        className="fixed right-6 top-1/2 transform -translate-y-1/2 text-white p-3 rounded-full shadow-lg transition-all duration-300 z-30 animate-in fade-in slide-in-from-right-4"
        style={{
          background: `conic-gradient(rgb(255, 150, 0) ${scrollProgress * 3.6}deg, rgba(255, 255, 255, 0.3) ${scrollProgress * 3.6}deg)`,
          padding: '3px'
        }}
      >
        <div
          className="p-3 rounded-full transition-colors duration-300"
          style={{
            backgroundColor: 'rgb(255, 150, 0)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
          }}
        >
          {isAboveFold ? <ArrowDown size={20} /> : <ArrowUp size={20} />}
        </div>
      </button>

      {/* Chat with AI Assistant Button */}
      <button
        className="fixed bottom-6 right-6 text-white px-4 py-3 rounded-lg shadow-lg transition-all duration-200 z-30 flex items-center gap-2"
        style={{
          backgroundColor: 'rgb(255, 150, 0)'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
        }}
      >
        <MessageCircle size={20} />
        <span className="hidden sm:inline font-medium">CHAT WITH AI</span>
      </button>

      {/* Chat with AI Button */}
      <button
        className="fixed bottom-6 left-1/2 transform -translate-x-1/2 text-white px-4 py-2 rounded-lg shadow-lg transition-all duration-200 z-30 pulse-glow"
        style={{
          backgroundColor: 'rgb(255, 150, 0)'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
        }}
      >
        <span className="font-medium">CHAT WITH AI</span>
      </button>
    </>
  );
}
