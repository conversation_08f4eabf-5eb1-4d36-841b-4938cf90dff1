-- Performance Monitoring Tables Migration
-- Creates tables for performance metrics and cost tracking

-- 1. Performance Metrics Table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN ('api_response', 'database_query', 'memory_usage', 'job_processing', 'ai_provider', 'scraping')),
    metric_name VARCHAR(100) NOT NULL,
    value DECIMAL(10,3) NOT NULL,
    unit VARCHAR(20) NOT NULL CHECK (unit IN ('ms', 'mb', 'count', 'percent', 'bytes')),
    endpoint VARCHAR(255),
    operation VARCHAR(100),
    provider VARCHAR(50),
    metadata JSONB,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. Cost Tracking Table
CREATE TABLE IF NOT EXISTS cost_tracking (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    service_type VARCHAR(50) NOT NULL CHECK (service_type IN ('openai', 'openrouter', 'scrape_do', 'other')),
    service_name VARCHAR(100) NOT NULL,
    operation_type VARCHAR(50) NOT NULL CHECK (operation_type IN ('content_generation', 'web_scraping', 'image_processing', 'other')),
    cost_amount DECIMAL(10,4) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    usage_units INTEGER,
    unit_type VARCHAR(20) CHECK (unit_type IN ('tokens', 'requests', 'pages', 'images')),
    metadata JSONB,
    tool_id VARCHAR(255),
    job_id UUID,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 3. System Alerts Table
CREATE TABLE IF NOT EXISTS system_alerts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN ('performance', 'cost', 'error', 'security', 'system')),
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    source_component VARCHAR(100),
    metadata JSONB,
    is_acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_by VARCHAR(255),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON performance_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_type_timestamp ON performance_metrics(metric_type, timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_endpoint ON performance_metrics(endpoint) WHERE endpoint IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_performance_metrics_provider ON performance_metrics(provider) WHERE provider IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_cost_tracking_timestamp ON cost_tracking(timestamp);
CREATE INDEX IF NOT EXISTS idx_cost_tracking_service_timestamp ON cost_tracking(service_type, timestamp);
CREATE INDEX IF NOT EXISTS idx_cost_tracking_tool_id ON cost_tracking(tool_id) WHERE tool_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_cost_tracking_job_id ON cost_tracking(job_id) WHERE job_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_system_alerts_type_severity ON system_alerts(alert_type, severity);
CREATE INDEX IF NOT EXISTS idx_system_alerts_created_at ON system_alerts(created_at);
CREATE INDEX IF NOT EXISTS idx_system_alerts_acknowledged ON system_alerts(is_acknowledged, created_at);

-- Add foreign key constraints
ALTER TABLE cost_tracking 
ADD CONSTRAINT fk_cost_tracking_tool_id 
FOREIGN KEY (tool_id) REFERENCES tools(id) ON DELETE SET NULL;

ALTER TABLE cost_tracking 
ADD CONSTRAINT fk_cost_tracking_job_id 
FOREIGN KEY (job_id) REFERENCES enhanced_jobs(id) ON DELETE SET NULL;

-- Add comments for documentation
COMMENT ON TABLE performance_metrics IS 'Stores system performance metrics including API response times, database query performance, memory usage, and job processing times';
COMMENT ON TABLE cost_tracking IS 'Tracks costs for AI providers, scraping services, and other operational expenses';
COMMENT ON TABLE system_alerts IS 'Stores system alerts for performance issues, cost thresholds, errors, and other system events';

COMMENT ON COLUMN performance_metrics.metric_type IS 'Type of metric: api_response, database_query, memory_usage, job_processing, ai_provider, scraping';
COMMENT ON COLUMN performance_metrics.value IS 'Numeric value of the metric';
COMMENT ON COLUMN performance_metrics.unit IS 'Unit of measurement: ms, mb, count, percent, bytes';
COMMENT ON COLUMN performance_metrics.metadata IS 'Additional metric data in JSON format';

COMMENT ON COLUMN cost_tracking.service_type IS 'Service provider: openai, openrouter, scrape_do, other';
COMMENT ON COLUMN cost_tracking.operation_type IS 'Type of operation: content_generation, web_scraping, image_processing, other';
COMMENT ON COLUMN cost_tracking.cost_amount IS 'Cost amount in the specified currency';
COMMENT ON COLUMN cost_tracking.usage_units IS 'Number of units consumed (tokens, requests, etc.)';

COMMENT ON COLUMN system_alerts.alert_type IS 'Type of alert: performance, cost, error, security, system';
COMMENT ON COLUMN system_alerts.severity IS 'Alert severity: low, medium, high, critical';
COMMENT ON COLUMN system_alerts.is_acknowledged IS 'Whether the alert has been acknowledged by an admin';

-- Insert sample data for testing (optional)
-- INSERT INTO performance_metrics (metric_type, metric_name, value, unit, endpoint, metadata) VALUES
-- ('api_response', 'response_time', 250.5, 'ms', '/api/tools', '{"status_code": 200}'),
-- ('database_query', 'query_time', 45.2, 'ms', null, '{"query": "SELECT * FROM tools", "rows": 84}'),
-- ('memory_usage', 'heap_used', 128.7, 'mb', null, '{"total_heap": 256}');

-- INSERT INTO cost_tracking (service_type, service_name, operation_type, cost_amount, usage_units, unit_type, metadata) VALUES
-- ('openai', 'gpt-4o-2024-11-20', 'content_generation', 0.025, 1000, 'tokens', '{"input_tokens": 500, "output_tokens": 500}'),
-- ('scrape_do', 'scrape.do', 'web_scraping', 0.005, 1, 'requests', '{"render": true, "screenshot": false}');

-- INSERT INTO system_alerts (alert_type, severity, title, message, source_component) VALUES
-- ('performance', 'medium', 'High Response Time', 'API response time exceeded 1000ms threshold', 'performance_monitor'),
-- ('cost', 'high', 'Budget Threshold', 'Monthly cost approaching 80% of budget limit', 'cost_tracker');

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON performance_metrics TO authenticated;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON cost_tracking TO authenticated;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON system_alerts TO authenticated;
