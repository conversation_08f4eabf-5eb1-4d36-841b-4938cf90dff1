'use client';

import React from 'react';
import { Card } from '@/components/ui/Card';

interface JobMetrics {
  totalJobs: number;
  activeJobs: number;
  queuedJobs: number;
  completedToday: number;
  failedJobs: number;
  successRate: number;
  averageProcessingTime: number;
  queueHealth: 'healthy' | 'warning' | 'error';
}

interface JobMetricsCardsProps {
  metrics: JobMetrics;
  isLoading?: boolean;
  className?: string;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string;
  color: 'blue' | 'green' | 'yellow' | 'red' | 'orange' | 'purple';
  trend?: {
    value: number;
    isPositive: boolean;
  };
  isLoading?: boolean;
}

/**
 * Individual Metric Card Component
 */
function MetricCard({ 
  title, 
  value, 
  subtitle, 
  icon, 
  color, 
  trend, 
  isLoading 
}: MetricCardProps): React.JSX.Element {
  const colorClasses = {
    blue: 'text-blue-400 bg-blue-500/10 border-blue-500/20',
    green: 'text-green-400 bg-green-500/10 border-green-500/20',
    yellow: 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20',
    red: 'text-red-400 bg-red-500/10 border-red-500/20',
    orange: 'text-orange-400 bg-orange-500/10 border-orange-500/20',
    purple: 'text-purple-400 bg-purple-500/10 border-purple-500/20',
  };

  const iconColorClasses = {
    blue: 'text-blue-400',
    green: 'text-green-400',
    yellow: 'text-yellow-400',
    red: 'text-red-400',
    orange: 'text-orange-400',
    purple: 'text-purple-400',
  };

  if (isLoading) {
    return (
      <Card className="bg-zinc-800 border border-zinc-700 p-6 animate-pulse">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-4 bg-zinc-700 rounded w-24"></div>
            <div className="h-8 bg-zinc-700 rounded w-16"></div>
            <div className="h-3 bg-zinc-700 rounded w-20"></div>
          </div>
          <div className="h-12 w-12 bg-zinc-700 rounded-lg"></div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={`bg-zinc-800 border border-zinc-700 p-6 hover:border-zinc-600 transition-all duration-200 ${colorClasses[color]}`}>
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h3 className="text-sm font-medium text-gray-300 uppercase tracking-wide">
            {title}
          </h3>
          <div className="flex items-baseline space-x-2">
            <span className="text-3xl font-bold text-white">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </span>
            {trend && (
              <span className={`text-sm font-medium ${
                trend.isPositive ? 'text-green-400' : 'text-red-400'
              }`}>
                {trend.isPositive ? '↗' : '↘'} {Math.abs(trend.value)}%
              </span>
            )}
          </div>
          {subtitle && (
            <p className="text-sm text-gray-400">
              {subtitle}
            </p>
          )}
        </div>
        
        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
          <span className={`text-2xl ${iconColorClasses[color]}`}>
            {icon}
          </span>
        </div>
      </div>
    </Card>
  );
}

/**
 * Job Metrics Cards Component
 * 
 * Displays key job processing metrics in a grid of cards.
 * Features:
 * - Real-time metrics display
 * - Color-coded status indicators
 * - Loading states with skeleton animation
 * - Responsive grid layout
 * - Trend indicators for performance metrics
 */
export function JobMetricsCards({ 
  metrics, 
  isLoading = false, 
  className = '' 
}: JobMetricsCardsProps): React.JSX.Element {
  const formatProcessingTime = (milliseconds: number): string => {
    if (milliseconds < 1000) return `${milliseconds}ms`;
    if (milliseconds < 60000) return `${(milliseconds / 1000).toFixed(1)}s`;
    return `${(milliseconds / 60000).toFixed(1)}m`;
  };

  const getHealthColor = (health: string): 'green' | 'yellow' | 'red' => {
    switch (health) {
      case 'healthy': return 'green';
      case 'warning': return 'yellow';
      case 'error': return 'red';
      default: return 'yellow';
    }
  };

  const getHealthIcon = (health: string): string => {
    switch (health) {
      case 'healthy': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      default: return '❓';
    }
  };

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
      {/* Total Jobs */}
      <MetricCard
        title="Total Jobs"
        value={metrics.totalJobs}
        subtitle="All time"
        icon="📊"
        color="blue"
        isLoading={isLoading}
      />

      {/* Active Jobs */}
      <MetricCard
        title="Active Jobs"
        value={metrics.activeJobs}
        subtitle="Currently processing"
        icon="⚡"
        color="orange"
        isLoading={isLoading}
      />

      {/* Queued Jobs */}
      <MetricCard
        title="Queued Jobs"
        value={metrics.queuedJobs}
        subtitle="Waiting to process"
        icon="⏳"
        color="yellow"
        isLoading={isLoading}
      />

      {/* Completed Today */}
      <MetricCard
        title="Completed Today"
        value={metrics.completedToday}
        subtitle="Last 24 hours"
        icon="✅"
        color="green"
        isLoading={isLoading}
      />

      {/* Failed Jobs */}
      <MetricCard
        title="Failed Jobs"
        value={metrics.failedJobs}
        subtitle="Requires attention"
        icon="❌"
        color="red"
        isLoading={isLoading}
      />

      {/* Success Rate */}
      <MetricCard
        title="Success Rate"
        value={`${metrics.successRate.toFixed(1)}%`}
        subtitle="Last 24 hours"
        icon="🎯"
        color="green"
        trend={{
          value: 2.5,
          isPositive: true
        }}
        isLoading={isLoading}
      />

      {/* Average Processing Time */}
      <MetricCard
        title="Avg Processing Time"
        value={formatProcessingTime(metrics.averageProcessingTime)}
        subtitle="Per job"
        icon="⏱️"
        color="purple"
        trend={{
          value: 15,
          isPositive: false
        }}
        isLoading={isLoading}
      />

      {/* Queue Health */}
      <MetricCard
        title="Queue Health"
        value={metrics.queueHealth.charAt(0).toUpperCase() + metrics.queueHealth.slice(1)}
        subtitle="System status"
        icon={getHealthIcon(metrics.queueHealth)}
        color={getHealthColor(metrics.queueHealth)}
        isLoading={isLoading}
      />
    </div>
  );
}

export default JobMetricsCards;
