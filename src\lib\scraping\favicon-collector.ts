/**
 * Favicon Collector for Scrape.do Integration
 * Handles favicon extraction, validation, and server storage
 */

import { promises as fs } from 'fs';
import { join, dirname } from 'path';
import { FaviconResult } from './types';

export class FaviconCollector {
  private readonly storageDir: string;
  private readonly maxFileSize = 1024 * 1024; // 1MB limit for favicons

  constructor() {
    // Store favicons in public/favicons directory
    this.storageDir = join(process.cwd(), 'public', 'favicons');
    this.ensureStorageDirectory();
  }

  /**
   * Extract favicon URLs from HTML content with comprehensive fallback
   */
  extractFaviconUrls(content: string, baseUrl: string): string[] {
    const faviconUrls: string[] = [];

    // Comprehensive favicon selectors in priority order
    const faviconPatterns = [
      // Modern favicon formats
      { regex: /<link[^>]*rel=["']icon["'][^>]*href=["']([^"']+)["']/gi, priority: 1 },
      { regex: /<link[^>]*href=["']([^"']+)["'][^>]*rel=["']icon["']/gi, priority: 1 },
      
      // Legacy shortcut icon
      { regex: /<link[^>]*rel=["']shortcut icon["'][^>]*href=["']([^"']+)["']/gi, priority: 2 },
      { regex: /<link[^>]*href=["']([^"']+)["'][^>]*rel=["']shortcut icon["']/gi, priority: 2 },
      
      // Apple touch icons (high quality)
      { regex: /<link[^>]*rel=["']apple-touch-icon["'][^>]*href=["']([^"']+)["']/gi, priority: 3 },
      { regex: /<link[^>]*rel=["']apple-touch-icon-precomposed["'][^>]*href=["']([^"']+)["']/gi, priority: 4 },
      
      // Mask icon (Safari)
      { regex: /<link[^>]*rel=["']mask-icon["'][^>]*href=["']([^"']+)["']/gi, priority: 5 },
      
      // Fluid icon (Fluidapp)
      { regex: /<link[^>]*rel=["']fluid-icon["'][^>]*href=["']([^"']+)["']/gi, priority: 6 }
    ];

    // Extract using all patterns
    const foundUrls = new Map<string, number>(); // URL -> priority
    
    faviconPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.regex.exec(content)) !== null) {
        const url = this.resolveUrl(match[1], baseUrl);
        if (!foundUrls.has(url) || foundUrls.get(url)! > pattern.priority) {
          foundUrls.set(url, pattern.priority);
        }
      }
    });

    // Sort by priority and convert to array
    const sortedUrls = Array.from(foundUrls.entries())
      .sort((a, b) => a[1] - b[1])
      .map(entry => entry[0]);

    faviconUrls.push(...sortedUrls);

    // Fallback to default favicon.ico if no favicons found
    if (faviconUrls.length === 0) {
      try {
        const baseUrlObj = new URL(baseUrl);
        faviconUrls.push(`${baseUrlObj.origin}/favicon.ico`);
      } catch {
        // Invalid base URL, skip fallback
      }
    }

    return [...new Set(faviconUrls)]; // Remove duplicates
  }

  /**
   * Collect and store favicon with validation and fallback
   */
  async collectFavicon(url: string, content: string): Promise<FaviconResult> {
    try {
      const faviconUrls = this.extractFaviconUrls(content, url);
      const validatedFavicons: string[] = [];
      let primaryFavicon: string | null = null;
      let storedPath: string | null = null;

      // Validate and attempt to store each favicon
      for (const faviconUrl of faviconUrls) {
        const isValid = await this.validateFavicon(faviconUrl);
        if (isValid) {
          validatedFavicons.push(faviconUrl);
          
          // Try to store the first valid favicon
          if (!primaryFavicon) {
            const stored = await this.storeFavicon(faviconUrl, url);
            if (stored) {
              primaryFavicon = faviconUrl;
              storedPath = stored;
            }
          }
        }
      }

      return {
        faviconUrls: validatedFavicons,
        primaryFavicon,
        storedPath,
        extractedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Favicon collection failed:', error);
      return {
        faviconUrls: [],
        primaryFavicon: null,
        storedPath: null,
        extractedAt: new Date().toISOString(),
        error: (error as Error).message
      };
    }
  }

  /**
   * Validate favicon accessibility and format
   */
  async validateFavicon(faviconUrl: string): Promise<boolean> {
    try {
      const response = await fetch(faviconUrl, { 
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });
      
      if (!response.ok) return false;

      const contentType = response.headers.get('content-type') || '';
      const contentLength = parseInt(response.headers.get('content-length') || '0');
      
      // Check if it's a valid image type
      const validTypes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/png', 'image/gif', 'image/jpeg', 'image/svg+xml'];
      const isValidType = validTypes.some(type => contentType.includes(type));
      
      // Check size constraints
      const isValidSize = contentLength > 0 && contentLength <= this.maxFileSize;
      
      return isValidType && isValidSize;
    } catch {
      return false;
    }
  }

  /**
   * Download and store favicon on server
   */
  async storeFavicon(faviconUrl: string, sourceUrl: string): Promise<string | null> {
    try {
      // Generate filename based on domain
      const domain = new URL(sourceUrl).hostname.replace(/[^a-zA-Z0-9.-]/g, '_');
      const extension = this.getFileExtension(faviconUrl);
      const filename = `${domain}_${Date.now()}.${extension}`;
      const filepath = join(this.storageDir, filename);

      // Download favicon
      const response = await fetch(faviconUrl, {
        signal: AbortSignal.timeout(10000)
      });

      if (!response.ok) return null;

      const buffer = await response.arrayBuffer();
      
      // Validate size
      if (buffer.byteLength > this.maxFileSize) {
        console.warn(`Favicon too large: ${buffer.byteLength} bytes`);
        return null;
      }

      // Save to disk
      await fs.writeFile(filepath, Buffer.from(buffer));
      
      // Return relative path for web access
      return `/favicons/${filename}`;
    } catch (error) {
      console.error('Failed to store favicon:', error);
      return null;
    }
  }

  /**
   * Get file extension from URL or content type
   */
  private getFileExtension(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const extension = pathname.split('.').pop()?.toLowerCase();
      
      // Map common favicon extensions
      const extensionMap: Record<string, string> = {
        'ico': 'ico',
        'png': 'png',
        'gif': 'gif',
        'jpg': 'jpg',
        'jpeg': 'jpg',
        'svg': 'svg'
      };
      
      return extensionMap[extension || ''] || 'ico';
    } catch {
      return 'ico';
    }
  }

  /**
   * Ensure storage directory exists
   */
  private async ensureStorageDirectory(): Promise<void> {
    try {
      await fs.access(this.storageDir);
    } catch {
      await fs.mkdir(this.storageDir, { recursive: true });
    }
  }

  /**
   * Clean up old favicon files (optional maintenance)
   */
  async cleanupOldFavicons(maxAgeMs: number = 30 * 24 * 60 * 60 * 1000): Promise<number> {
    try {
      const files = await fs.readdir(this.storageDir);
      const now = Date.now();
      let deletedCount = 0;

      for (const file of files) {
        const filepath = join(this.storageDir, file);
        const stats = await fs.stat(filepath);
        
        if (now - stats.mtime.getTime() > maxAgeMs) {
          await fs.unlink(filepath);
          deletedCount++;
        }
      }

      return deletedCount;
    } catch (error) {
      console.error('Favicon cleanup failed:', error);
      return 0;
    }
  }

  /**
   * Get favicon info without downloading
   */
  async getFaviconInfo(faviconUrl: string): Promise<{
    size?: number;
    type?: string;
    lastModified?: string;
    accessible: boolean;
  }> {
    try {
      const response = await fetch(faviconUrl, { 
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });
      
      return {
        size: parseInt(response.headers.get('content-length') || '0') || undefined,
        type: response.headers.get('content-type') || undefined,
        lastModified: response.headers.get('last-modified') || undefined,
        accessible: response.ok
      };
    } catch {
      return { accessible: false };
    }
  }

  /**
   * Batch collect favicons for multiple URLs
   */
  async collectFaviconsBatch(urls: string[], contents: string[]): Promise<FaviconResult[]> {
    if (urls.length !== contents.length) {
      throw new Error('URLs and contents arrays must have the same length');
    }

    const results: FaviconResult[] = [];
    
    // Process in batches to avoid overwhelming the server
    const batchSize = 5;
    for (let i = 0; i < urls.length; i += batchSize) {
      const batch = urls.slice(i, i + batchSize);
      const batchContents = contents.slice(i, i + batchSize);
      
      const batchPromises = batch.map((url, index) => 
        this.collectFavicon(url, batchContents[index])
      );
      
      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            faviconUrls: [],
            primaryFavicon: null,
            storedPath: null,
            extractedAt: new Date().toISOString(),
            error: result.reason?.message || 'Unknown error'
          });
        }
      });
      
      // Small delay between batches
      if (i + batchSize < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  /**
   * Resolve relative URLs to absolute URLs
   */
  private resolveUrl(relativeUrl: string, baseUrl: string): string {
    try {
      return new URL(relativeUrl, baseUrl).href;
    } catch {
      return relativeUrl;
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    oldestFile?: string;
    newestFile?: string;
  }> {
    try {
      const files = await fs.readdir(this.storageDir);
      let totalSize = 0;
      let oldestTime = Infinity;
      let newestTime = 0;
      let oldestFile = '';
      let newestFile = '';

      for (const file of files) {
        const filepath = join(this.storageDir, file);
        const stats = await fs.stat(filepath);
        
        totalSize += stats.size;
        
        if (stats.mtime.getTime() < oldestTime) {
          oldestTime = stats.mtime.getTime();
          oldestFile = file;
        }
        
        if (stats.mtime.getTime() > newestTime) {
          newestTime = stats.mtime.getTime();
          newestFile = file;
        }
      }

      return {
        totalFiles: files.length,
        totalSize,
        oldestFile: oldestFile || undefined,
        newestFile: newestFile || undefined
      };
    } catch {
      return { totalFiles: 0, totalSize: 0 };
    }
  }
}

// Export singleton instance
export const faviconCollector = new FaviconCollector();
