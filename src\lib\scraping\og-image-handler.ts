/**
 * Open Graph Image Handler for Scrape.do Integration
 * Specialized extraction and processing of OG images from web content
 */

import { MediaAsset } from './types';

export class OGImageHandler {
  
  /**
   * Extract Open Graph images from HTML content with comprehensive meta tag support
   */
  extractOGImages(content: string, baseUrl: string): MediaAsset[] {
    const ogImages: MediaAsset[] = [];

    // Comprehensive meta tag patterns for image extraction
    const metaPatterns = [
      // Open Graph standard
      { regex: /<meta\s+property=["']og:image["']\s+content=["']([^"']+)["']/gi, type: 'og:image', priority: 1 },
      { regex: /<meta\s+content=["']([^"']+)["']\s+property=["']og:image["']/gi, type: 'og:image', priority: 1 },
      
      // Twitter Cards
      { regex: /<meta\s+name=["']twitter:image["']\s+content=["']([^"']+)["']/gi, type: 'twitter:image', priority: 2 },
      { regex: /<meta\s+content=["']([^"']+)["']\s+name=["']twitter:image["']/gi, type: 'twitter:image', priority: 2 },
      { regex: /<meta\s+name=["']twitter:image:src["']\s+content=["']([^"']+)["']/gi, type: 'twitter:image', priority: 2 },
      
      // Facebook specific
      { regex: /<meta\s+property=["']fb:image["']\s+content=["']([^"']+)["']/gi, type: 'facebook:image', priority: 3 },
      
      // Additional OG image variants
      { regex: /<meta\s+property=["']og:image:url["']\s+content=["']([^"']+)["']/gi, type: 'og:image', priority: 1 },
      { regex: /<meta\s+property=["']og:image:secure_url["']\s+content=["']([^"']+)["']/gi, type: 'og:image', priority: 1 },
      
      // Schema.org structured data
      { regex: /<meta\s+itemprop=["']image["']\s+content=["']([^"']+)["']/gi, type: 'og:image', priority: 4 },
      
      // Apple touch icons (fallback)
      { regex: /<link\s+rel=["']apple-touch-icon["']\s+href=["']([^"']+)["']/gi, type: 'og:image', priority: 5 },
      { regex: /<link\s+rel=["']apple-touch-icon-precomposed["']\s+href=["']([^"']+)["']/gi, type: 'og:image', priority: 5 }
    ];

    // Extract images using all patterns
    metaPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.regex.exec(content)) !== null) {
        const imageUrl = this.resolveUrl(match[1], baseUrl);
        
        // Skip if already found (avoid duplicates)
        if (!ogImages.some(img => img.url === imageUrl)) {
          ogImages.push({
            type: pattern.type as MediaAsset['type'],
            url: imageUrl,
            priority: pattern.priority,
            metadata: this.extractImageMetadata(match[0], content)
          });
        }
      }
    });

    return ogImages.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Extract additional metadata from meta tags
   */
  private extractImageMetadata(metaTag: string, fullContent: string): MediaAsset['metadata'] {
    const metadata: MediaAsset['metadata'] = {};

    // Try to find width and height from related meta tags
    const imageUrl = metaTag.match(/content=["']([^"']+)["']/)?.[1];
    if (imageUrl) {
      // Look for og:image:width and og:image:height
      const widthMatch = fullContent.match(new RegExp(`<meta\\s+property=["']og:image:width["']\\s+content=["']([^"']+)["']`, 'i'));
      const heightMatch = fullContent.match(new RegExp(`<meta\\s+property=["']og:image:height["']\\s+content=["']([^"']+)["']`, 'i'));
      
      if (widthMatch) metadata.width = parseInt(widthMatch[1]);
      if (heightMatch) metadata.height = parseInt(heightMatch[1]);

      // Try to determine format from URL
      const formatMatch = imageUrl.match(/\.([a-z]{3,4})(?:\?|$)/i);
      if (formatMatch) {
        metadata.format = formatMatch[1].toLowerCase();
      }
    }

    return metadata;
  }

  /**
   * Validate and filter OG images based on quality criteria
   */
  filterQualityOGImages(images: MediaAsset[]): MediaAsset[] {
    return images
      .filter(image => this.isValidImageUrl(image.url))
      .filter(image => this.meetsQualityCriteria(image))
      .sort((a, b) => {
        // Sort by priority first
        if (a.priority !== b.priority) {
          return a.priority - b.priority;
        }
        
        // Then by estimated quality score
        return this.calculateQualityScore(b) - this.calculateQualityScore(a);
      });
  }

  /**
   * Check if URL appears to be a valid image
   */
  private isValidImageUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname.toLowerCase();
      
      // Check for image extensions
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp'];
      const hasImageExtension = imageExtensions.some(ext => pathname.endsWith(ext));
      
      // Check for common image URL patterns
      const hasImagePattern = /\/(image|img|photo|picture|media)\//i.test(pathname);
      
      // Exclude obvious non-images
      const isNotImage = pathname.includes('logo') && pathname.includes('small');
      
      return (hasImageExtension || hasImagePattern) && !isNotImage;
    } catch {
      return false;
    }
  }

  /**
   * Check if image meets quality criteria
   */
  private meetsQualityCriteria(image: MediaAsset): boolean {
    // Prefer images with known dimensions
    if (image.metadata?.width && image.metadata?.height) {
      const width = image.metadata.width;
      const height = image.metadata.height;
      
      // Minimum size requirements
      if (width < 200 || height < 200) return false;
      
      // Prefer landscape or square images for OG
      const aspectRatio = width / height;
      if (aspectRatio < 0.5 || aspectRatio > 3) return false;
    }

    // Exclude common low-quality patterns
    const url = image.url.toLowerCase();
    const excludePatterns = [
      'avatar', 'profile', 'user', 'author',
      'icon', 'favicon', 'logo-small',
      'thumbnail', 'thumb', 'preview-small',
      'placeholder', 'default', 'fallback'
    ];

    return !excludePatterns.some(pattern => url.includes(pattern));
  }

  /**
   * Calculate quality score for image ranking
   */
  private calculateQualityScore(image: MediaAsset): number {
    let score = 0;

    // Priority bonus (lower priority number = higher score)
    score += (6 - image.priority) * 10;

    // Size bonus
    if (image.metadata?.width && image.metadata?.height) {
      const area = image.metadata.width * image.metadata.height;
      score += Math.min(area / 10000, 20); // Max 20 points for size
    }

    // Format bonus
    if (image.metadata?.format) {
      const formatScores = { 'png': 5, 'jpg': 4, 'jpeg': 4, 'webp': 3, 'gif': 2, 'svg': 1 };
      score += formatScores[image.metadata.format as keyof typeof formatScores] || 0;
    }

    // URL quality bonus
    const url = image.url.toLowerCase();
    if (url.includes('og-image') || url.includes('social')) score += 10;
    if (url.includes('large') || url.includes('big')) score += 5;
    if (url.includes('high') || url.includes('quality')) score += 5;

    return score;
  }

  /**
   * Get the best OG image from a collection
   */
  getBestOGImage(images: MediaAsset[]): MediaAsset | null {
    const qualityImages = this.filterQualityOGImages(images);
    return qualityImages.length > 0 ? qualityImages[0] : null;
  }

  /**
   * Extract OG image with fallback to other meta images
   */
  extractPrimaryOGImage(content: string, baseUrl: string): MediaAsset | null {
    const allImages = this.extractOGImages(content, baseUrl);
    return this.getBestOGImage(allImages);
  }

  /**
   * Validate OG image accessibility
   */
  async validateOGImage(imageUrl: string): Promise<boolean> {
    try {
      const response = await fetch(imageUrl, { 
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });
      
      const isAccessible = response.ok;
      const isImage = response.headers.get('content-type')?.startsWith('image/') ?? false;
      const isReasonableSize = parseInt(response.headers.get('content-length') || '0') < 10 * 1024 * 1024; // 10MB limit
      
      return isAccessible && isImage && isReasonableSize;
    } catch {
      return false;
    }
  }

  /**
   * Resolve relative URLs to absolute URLs
   */
  private resolveUrl(relativeUrl: string, baseUrl: string): string {
    try {
      return new URL(relativeUrl, baseUrl).href;
    } catch {
      return relativeUrl;
    }
  }

  /**
   * Extract structured data images (JSON-LD)
   */
  extractStructuredDataImages(content: string, baseUrl: string): MediaAsset[] {
    const images: MediaAsset[] = [];
    
    try {
      // Find JSON-LD script tags
      const jsonLdRegex = /<script[^>]*type=["']application\/ld\+json["'][^>]*>([\s\S]*?)<\/script>/gi;
      let match;
      
      while ((match = jsonLdRegex.exec(content)) !== null) {
        try {
          const jsonData = JSON.parse(match[1]);
          const extractedImages = this.extractImagesFromJsonLd(jsonData, baseUrl);
          images.push(...extractedImages);
        } catch {
          // Skip invalid JSON
        }
      }
    } catch (error) {
      console.warn('Failed to extract structured data images:', error);
    }
    
    return images;
  }

  /**
   * Extract images from JSON-LD structured data
   */
  private extractImagesFromJsonLd(data: any, baseUrl: string): MediaAsset[] {
    const images: MediaAsset[] = [];
    
    const processObject = (obj: any) => {
      if (typeof obj !== 'object' || obj === null) return;
      
      // Look for image properties
      if (obj.image) {
        const imageUrls = Array.isArray(obj.image) ? obj.image : [obj.image];
        imageUrls.forEach((img: any) => {
          const url = typeof img === 'string' ? img : img.url;
          if (url) {
            images.push({
              type: 'og:image',
              url: this.resolveUrl(url, baseUrl),
              priority: 4
            });
          }
        });
      }
      
      // Recursively process nested objects
      Object.values(obj).forEach(value => {
        if (Array.isArray(value)) {
          value.forEach(item => processObject(item));
        } else {
          processObject(value);
        }
      });
    };
    
    processObject(data);
    return images;
  }
}

// Export singleton instance
export const ogImageHandler = new OGImageHandler();
