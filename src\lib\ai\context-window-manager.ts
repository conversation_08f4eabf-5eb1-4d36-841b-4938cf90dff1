import { AIModelConfig, ModelConfig, ProcessedContent, AIProcessingConfig } from './types';

export class ContextWindowManager {
  
  /**
   * Calculate approximate token count for text
   */
  static calculateTokenCount(text: string): number {
    // Approximate token calculation (1 token ≈ 4 characters for English)
    // This is a rough estimate - for production, consider using tiktoken or similar
    return Math.ceil(text.length / 4);
  }

  /**
   * Check if content fits within model's context window
   */
  static canModelHandleContent(content: string, modelConfig: AIModelConfig): boolean {
    const tokenCount = this.calculateTokenCount(content);
    // Reserve 20% of context window for response and formatting
    const usableTokens = Math.floor(modelConfig.maxInputTokens * 0.8);
    
    return tokenCount <= usableTokens;
  }

  /**
   * Split content into chunks that fit within model's context window
   */
  static splitContentForModel(content: string, modelConfig: AIModelConfig): string[] {
    const tokenCount = this.calculateTokenCount(content);
    const maxTokensPerChunk = Math.floor(modelConfig.maxInputTokens * 0.8); // Reserve 20% for response
    
    if (tokenCount <= maxTokensPerChunk) {
      return [content];
    }
    
    // Strategy 1: Split by logical sections (headers)
    const headerSplit = this.splitByHeaders(content, maxTokensPerChunk);
    if (headerSplit.length > 1) {
      return headerSplit;
    }
    
    // Strategy 2: Split by paragraphs
    const paragraphSplit = this.splitByParagraphs(content, maxTokensPerChunk);
    if (paragraphSplit.length > 1) {
      return paragraphSplit;
    }
    
    // Strategy 3: Split by sentences (last resort)
    return this.splitBySentences(content, maxTokensPerChunk);
  }

  /**
   * Split content by markdown headers
   */
  private static splitByHeaders(content: string, maxTokensPerChunk: number): string[] {
    const sections = content.split(/\n(?=#{1,3}\s)/); // Split on headers
    const chunks = [];
    let currentChunk = '';
    
    for (const section of sections) {
      const sectionTokens = this.calculateTokenCount(section);
      const currentTokens = this.calculateTokenCount(currentChunk);
      
      if (currentTokens + sectionTokens > maxTokensPerChunk && currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = section;
      } else {
        currentChunk += (currentChunk ? '\n' : '') + section;
      }
    }
    
    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }
    
    return chunks.length > 1 ? chunks : [content];
  }

  /**
   * Split content by paragraphs
   */
  private static splitByParagraphs(content: string, maxTokensPerChunk: number): string[] {
    const paragraphs = content.split(/\n\s*\n/);
    const chunks = [];
    let currentChunk = '';
    
    for (const paragraph of paragraphs) {
      const paragraphTokens = this.calculateTokenCount(paragraph);
      const currentTokens = this.calculateTokenCount(currentChunk);
      
      if (currentTokens + paragraphTokens > maxTokensPerChunk && currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = paragraph;
      } else {
        currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
      }
    }
    
    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }
    
    return chunks.length > 1 ? chunks : [content];
  }

  /**
   * Split content by sentences (last resort)
   */
  private static splitBySentences(content: string, maxTokensPerChunk: number): string[] {
    const sentences = content.split(/(?<=[.!?])\s+/);
    const chunks = [];
    let currentChunk = '';
    
    for (const sentence of sentences) {
      const sentenceTokens = this.calculateTokenCount(sentence);
      const currentTokens = this.calculateTokenCount(currentChunk);
      
      if (currentTokens + sentenceTokens > maxTokensPerChunk && currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = sentence;
      } else {
        currentChunk += (currentChunk ? ' ' : '') + sentence;
      }
    }
    
    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }
    
    return chunks;
  }

  /**
   * Optimize content for AI processing while maintaining quality
   */
  static optimizeContentForAI(
    content: string, 
    contentQuality: number = 70, 
    scrapingCost: number = 0
  ): ProcessedContent {
    // Quality-first optimization - never compromise AI processing quality
    const optimizedContent = this.cleanAndOptimizeContent(content);
    
    // Adjust processing parameters based on content characteristics
    const processingConfig = this.getProcessingConfig(contentQuality, scrapingCost);
    
    return {
      optimizedContent,
      processingConfig,
      estimatedTokens: this.calculateTokenCount(optimizedContent),
      costOptimizationApplied: scrapingCost > 3 || contentQuality < 60
    };
  }

  /**
   * Clean and optimize content for LLM processing
   */
  private static cleanAndOptimizeContent(content: string): string {
    // Remove excessive whitespace and navigation elements
    content = content.replace(/\n{3,}/g, '\n\n');
    content = content.replace(/^(Navigation|Menu|Footer|Header)[\s\S]*?(?=\n#|\n\n|$)/gm, '');

    // Clean HTML artifacts and normalize headers
    content = content.replace(/<[^>]*>/g, '');
    content = content.replace(/^#{4,}/gm, '###');

    // Remove repetitive content that could confuse AI
    content = content.replace(/(.{50,}?)\1{2,}/g, '$1'); // Remove 3+ repetitions
    
    // Clean up excessive punctuation
    content = content.replace(/[.]{4,}/g, '...');
    
    // Normalize spacing for better AI parsing
    content = content.replace(/\s{3,}/g, ' ');

    // Maintain generous token limits for quality AI processing
    const maxLength = 60000; // Generous limit for high-quality AI generation
    if (content.length > maxLength) {
      // Smart truncation - try to keep complete sections
      const truncated = content.substring(0, maxLength);
      const lastSection = truncated.lastIndexOf('\n## ') || truncated.lastIndexOf('\n# ');

      if (lastSection > maxLength * 0.8) {
        content = truncated.substring(0, lastSection) + '\n\n[Content truncated at section boundary for optimal AI processing]';
      } else {
        content = truncated + '\n\n[Content truncated for AI processing]';
      }
    }

    return content.trim();
  }

  /**
   * Get processing configuration based on content characteristics
   */
  private static getProcessingConfig(contentQuality: number, scrapingCost: number): AIProcessingConfig {
    const isHighCostScraping = scrapingCost > 3;

    return {
      temperature: 0.7, // Maintain consistent creativity for quality output
      maxTokens: 8192, // Always use full token allowance for quality
      priority: isHighCostScraping ? 'high' : 'normal', // Higher priority for expensive scraping
      qualityThreshold: 'high', // Always maintain high quality standards
      enhancedProcessing: isHighCostScraping, // Extra processing for expensive content
      multiPassValidation: true // Always validate output quality
    };
  }

  /**
   * Estimate processing time based on content size and model
   */
  static estimateProcessingTime(
    contentSize: number, 
    modelConfig: ModelConfig
  ): { estimatedSeconds: number; confidence: 'low' | 'medium' | 'high' } {
    // Base processing time estimates (in seconds)
    const baseTime = {
      'openai': 2, // GPT-4o is generally faster
      'openrouter': 4 // Gemini may be slower but more thorough
    };

    const base = baseTime[modelConfig.provider] || 3;
    
    // Scale based on content size (tokens)
    const tokenMultiplier = Math.max(1, contentSize / 10000); // 1 second per 10K tokens
    
    const estimatedSeconds = Math.ceil(base * tokenMultiplier);
    
    // Confidence based on content size
    let confidence: 'low' | 'medium' | 'high' = 'medium';
    if (contentSize < 5000) confidence = 'high';
    if (contentSize > 50000) confidence = 'low';

    return { estimatedSeconds, confidence };
  }

  /**
   * Get optimal chunk size for a specific model
   */
  static getOptimalChunkSize(modelConfig: AIModelConfig): number {
    // Use 70% of max input tokens to leave room for system prompt and response
    return Math.floor(modelConfig.maxInputTokens * 0.7);
  }

  /**
   * Validate content chunks don't exceed model limits
   */
  static validateChunks(chunks: string[], modelConfig: AIModelConfig): {
    valid: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];
    const maxChunkSize = this.getOptimalChunkSize(modelConfig);

    for (let i = 0; i < chunks.length; i++) {
      const chunkTokens = this.calculateTokenCount(chunks[i]);
      
      if (chunkTokens > maxChunkSize) {
        issues.push(`Chunk ${i + 1} exceeds optimal size (${chunkTokens} > ${maxChunkSize} tokens)`);
      }
      
      if (chunkTokens < 100) {
        issues.push(`Chunk ${i + 1} is very small (${chunkTokens} tokens) - consider merging`);
      }
    }

    if (chunks.length > 5) {
      recommendations.push('Consider using a model with larger context window for better coherence');
    }

    if (issues.length === 0 && chunks.length > 1) {
      recommendations.push('Content successfully split into manageable chunks');
    }

    return {
      valid: issues.length === 0,
      issues,
      recommendations
    };
  }
}
