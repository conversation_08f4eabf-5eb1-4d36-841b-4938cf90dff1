#!/usr/bin/env tsx

/**
 * <PERSON>ript to inspect scraped data from completed web scraping jobs
 * Run with: npx tsx scripts/inspect-scraped-data.ts
 */

import { config } from 'dotenv';
import { getJobQueue } from '../src/lib/jobs';

// Load environment variables
config({ path: '.env.local' });

async function inspectScrapedData() {
  console.log('🔍 Inspecting Scraped Data from Background Jobs\n');

  try {
    const queue = getJobQueue();
    
    // Get all jobs
    const allJobs = await queue.getJobs();
    
    // Filter for completed web scraping jobs
    const scrapingJobs = allJobs.filter(job => 
      job.type === 'web_scraping' && job.status === 'completed'
    );

    if (scrapingJobs.length === 0) {
      console.log('❌ No completed web scraping jobs found.');
      console.log('💡 Run a scraping job first: npm run test:automation');
      return;
    }

    console.log(`✅ Found ${scrapingJobs.length} completed scraping job(s)\n`);

    for (let i = 0; i < scrapingJobs.length; i++) {
      const job = scrapingJobs[i];
      console.log(`📊 Job ${i + 1}: ${job.id}`);
      console.log(`🌐 URL: ${job.data.url}`);
      console.log(`⏰ Completed: ${job.completedAt}`);
      console.log(`🔄 Processing time: ${job.completedAt ? 
        Math.round((new Date(job.completedAt).getTime() - new Date(job.createdAt).getTime()) / 1000) : 'N/A'} seconds`);
      
      if (job.result && job.result.data) {
        const data = job.result.data;
        
        console.log('\n📋 SCRAPED DATA STRUCTURE:');
        console.log('=' .repeat(50));
        
        // Basic page info
        console.log(`📄 Title: ${data.title || 'N/A'}`);
        console.log(`🔗 Final URL: ${data.url || 'N/A'}`);
        console.log(`📝 Text Length: ${data.text ? data.text.length : 0} characters`);
        
        // Meta tags
        if (data.meta && Object.keys(data.meta).length > 0) {
          console.log('\n🏷️ META TAGS:');
          Object.entries(data.meta).forEach(([key, value]) => {
            console.log(`  ${key}: ${String(value).substring(0, 100)}${String(value).length > 100 ? '...' : ''}`);
          });
        }
        
        // Headings
        if (data.headings && data.headings.length > 0) {
          console.log('\n📑 HEADINGS:');
          data.headings.slice(0, 10).forEach((heading: any) => {
            console.log(`  ${heading.level.toUpperCase()}: ${heading.text?.substring(0, 80) || 'N/A'}`);
          });
          if (data.headings.length > 10) {
            console.log(`  ... and ${data.headings.length - 10} more headings`);
          }
        }
        
        // Images
        if (data.images && data.images.length > 0) {
          console.log('\n🖼️ IMAGES:');
          console.log(`  Total images found: ${data.images.length}`);
          data.images.slice(0, 5).forEach((img: any, idx: number) => {
            console.log(`  ${idx + 1}. ${img.src?.substring(0, 60) || 'N/A'}${img.src?.length > 60 ? '...' : ''}`);
            if (img.alt) console.log(`     Alt: ${img.alt.substring(0, 50)}${img.alt.length > 50 ? '...' : ''}`);
          });
          if (data.images.length > 5) {
            console.log(`  ... and ${data.images.length - 5} more images`);
          }
        }
        
        // Links
        if (data.links && data.links.length > 0) {
          console.log('\n🔗 LINKS:');
          console.log(`  Total links found: ${data.links.length}`);
          data.links.slice(0, 5).forEach((link: any, idx: number) => {
            console.log(`  ${idx + 1}. ${link.href?.substring(0, 50) || 'N/A'}${link.href?.length > 50 ? '...' : ''}`);
            if (link.text) console.log(`     Text: ${link.text.substring(0, 40)}${link.text.length > 40 ? '...' : ''}`);
          });
          if (data.links.length > 5) {
            console.log(`  ... and ${data.links.length - 5} more links`);
          }
        }
        
        // Favicon
        if (data.favicon) {
          console.log('\n🎯 FAVICON:');
          console.log(`  ${data.favicon}`);
        }
        
        // Pricing information
        if (data.pricing && data.pricing.length > 0) {
          console.log('\n💰 PRICING INFO:');
          data.pricing.slice(0, 5).forEach((price: any, idx: number) => {
            console.log(`  ${idx + 1}. [${price.tag}] ${price.text?.substring(0, 60) || 'N/A'}`);
          });
        }
        
        // FAQ information
        if (data.faq && data.faq.length > 0) {
          console.log('\n❓ FAQ INFO:');
          data.faq.slice(0, 3).forEach((faq: any, idx: number) => {
            console.log(`  ${idx + 1}. [${faq.tag}] ${faq.text?.substring(0, 60) || 'N/A'}`);
          });
        }
        
        // Text content preview
        if (data.text) {
          console.log('\n📖 TEXT CONTENT PREVIEW:');
          console.log(`"${data.text.substring(0, 200)}${data.text.length > 200 ? '...' : ''}"`);
        }
        
        // Screenshot info
        if (job.result.screenshot) {
          console.log('\n📸 SCREENSHOT:');
          console.log(`  Format: PNG (base64 encoded)`);
          console.log(`  Size: ${Math.round(job.result.screenshot.length / 1024)} KB`);
          console.log(`  Data URL: ${job.result.screenshot.substring(0, 50)}...`);
        }
        
        console.log('\n' + '=' .repeat(50));
      } else {
        console.log('❌ No scraped data found in job result');
      }
      
      if (i < scrapingJobs.length - 1) {
        console.log('\n' + '-' .repeat(80) + '\n');
      }
    }
    
    console.log('\n🎉 Data inspection completed!');
    
  } catch (error) {
    console.error('❌ Error inspecting data:', error);
  }
}

inspectScrapedData();
