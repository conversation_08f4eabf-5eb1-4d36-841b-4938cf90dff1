'use client';

import React, { memo, useCallback } from 'react';
import { ArrowRight } from 'lucide-react';
import { AICategory } from '@/lib/types';
import { Icon } from '@/components/ui/Icon';
import { ToolListItem } from './ToolListItem';

/**
 * CategoryCardTPD Component - ThePortnDude.com Style Implementation
 * 
 * This component replicates the exact visual design and interaction patterns
 * from theporndude.com for our AI Tools Directory.
 * 
 * Key Features:
 * - Light card background with colored borders (2.82828px thickness)
 * - Colored header and description sections
 * - Exact divider line animation (2.99874px thickness, 55px initial width)
 * - Compact button styling with pulse animation on hover
 * - Roboto font family throughout
 * - Exact measurements and animations from theporndude.com
 */

interface CategoryCardTPDProps {
  category: AICategory;
  onShowTooltip: (content: string, element: HTMLElement, triggerType?: 'title' | 'search-icon') => void;
  onHideTooltip: () => void;
}

export const CategoryCardTPD = memo<CategoryCardTPDProps>(function CategoryCardTPD({
  category,
  onShowTooltip,
  onHideTooltip
}) {
  // Extract color from button color class for both border and underline
  const getColorInfo = useCallback(() => {
    const colorClass = category.seeAllButton.colorClass;

    // Extract the base color from the background class (e.g., "bg-sky-500" -> "sky-500")
    const bgColorMatch = colorClass.match(/bg-(\w+-\d+)/);

    if (bgColorMatch) {
      const colorName = bgColorMatch[1]; // e.g., "sky-500", "green-500", "pink-500", "yellow-600"

      // Map Tailwind colors to CSS custom properties
      const colorMap: Record<string, string> = {
        'sky-500': '#0ea5e9',
        'green-500': '#22c55e',
        'pink-500': '#ec4899',
        'yellow-600': '#ca8a04',
        'purple-500': '#a855f7',
        'indigo-500': '#6366f1',
        'orange-500': '#f97316',
        'teal-500': '#14b8a6',
        'cyan-500': '#06b6d4',
        'rose-500': '#f43f5e',
        'amber-500': '#f59e0b',
        'violet-500': '#8b5cf6',
        'emerald-500': '#10b981',
        'lime-500': '#84cc16',
        'zinc-700': '#3f3f46'
      };

      return {
        tailwindClass: colorName,
        cssColor: colorMap[colorName] || colorMap['zinc-700']
      };
    }

    // Fallback to default zinc color if extraction fails
    return {
      tailwindClass: 'zinc-700',
      cssColor: '#3f3f46'
    };
  }, [category.seeAllButton.colorClass]);

  const colorInfo = getColorInfo();

  // Stable callback to prevent unnecessary re-renders
  const handleSeeAllClick = useCallback(() => {
    // In a real app, this would navigate to the category page
    console.log(`Navigate to ${category.title} category page`);
  }, [category.title]);

  // Handle tooltip for category title
  const handleTitleMouseEnter = useCallback((event: React.MouseEvent<HTMLHeadingElement>) => {
    onShowTooltip(category.description, event.currentTarget);
  }, [category.description, onShowTooltip]);

  const handleTitleMouseLeave = useCallback(() => {
    onHideTooltip();
  }, [onHideTooltip]);

  return (
    <article
      className="tpd-card h-full flex flex-col"
      style={{
        '--category-color': colorInfo.cssColor,
        '--category-border-color': colorInfo.cssColor.replace('rgb', 'rgba').replace(')', ', 0.5)'),
      } as React.CSSProperties & { 
        '--category-color': string; 
        '--category-border-color': string;
      }}
      role="region"
      aria-labelledby={`category-${category.id}-title`}
      aria-describedby={`category-${category.id}-description`}
    >
      {/* ThePortnDude.com Style Header with Exact Divider Animation */}
      <header className="tpd-header">
        <Icon
          name={category.iconName as keyof typeof import('lucide-react')}
          size={16}
          className="text-white flex-shrink-0"
          aria-hidden="true"
        />
        <h3
          id={`category-${category.id}-title`}
          className="cursor-pointer"
          onMouseEnter={handleTitleMouseEnter}
          onMouseLeave={handleTitleMouseLeave}
          role="button"
          tabIndex={0}
          aria-describedby={`category-${category.id}-description`}
          aria-label={`${category.title} - Hover to see description tooltip`}
          title={category.description}
        >
          {category.title}
        </h3>
      </header>

      {/* ThePortnDude.com Style Description */}
      <div
        id={`category-${category.id}-description`}
        className="tpd-description"
      >
        {category.description}
      </div>

      {/* ThePortnDude.com Style Content Area */}
      <div className="tpd-content">
        {/* Tool List */}
        <ul className="tpd-tool-list custom-scrollbar">
          {category.tools.map((tool, index) => (
            <li key={tool.id} className="tpd-tool-item">
              <ToolListItem
                tool={tool}
                onShowTooltip={onShowTooltip}
                onHideTooltip={onHideTooltip}
                index={index + 1}
              />
            </li>
          ))}
        </ul>
      </div>

      {/* ThePortnDude.com Style Button with Exact Pulse Animation */}
      <button
        onClick={handleSeeAllClick}
        className="tpd-button"
        aria-label={`View all ${category.totalToolsCount} tools in ${category.title} category`}
        type="button"
      >
        SEE ALL {category.totalToolsCount} TOOLS
      </button>
    </article>
  );
});

CategoryCardTPD.displayName = 'CategoryCardTPD';
