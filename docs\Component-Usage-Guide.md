# Component Usage Guide

## Overview

This guide provides detailed instructions for using and extending components in the AI Dude Directory.

## Layout Components

### Root Layout (`src/app/layout.tsx`)
The root layout provides the basic HTML structure and includes global providers.

**Features:**
- SearchProvider for global search state
- LayoutContent wrapper
- FloatingButtonsWrapper
- Global CSS and metadata

**Usage:**
```tsx
// Automatically applied to all pages in the app directory
// No direct usage required
```

### LayoutContent (`src/components/layout/LayoutContent.tsx`)
Wraps page content with consistent header and footer.

**Props:**
- `children: React.ReactNode` - Page content

**Usage:**
```tsx
// Automatically used in root layout
// Provides Header and Footer to all pages
```

### Header (`src/components/layout/Header.tsx`)
Main navigation header with search functionality.

**Props:**
- `searchTerm: string`
- `isSearchDropdownVisible: boolean`
- `onSearchChange: (value: string) => void`
- `onSearchFocus: () => void`
- `onSearchBlur: () => void`
- `onTopSearchClick: (term: string) => void`

**Features:**
- Responsive design (mobile/desktop)
- Integrated search with dropdown
- Social icons with hover effects
- Logo and branding

### Footer (`src/components/layout/Footer.tsx`)
Footer with story section and bottom navigation.

**Features:**
- AIDudeStory component
- BottomNavFooter with social links
- Orange theme with animations

## Feature Components

### CategoryGrid (`src/components/features/CategoryGrid.tsx`)
Displays AI tool categories in a responsive grid.

**Props:**
- `categories: AICategory[]`
- `onShowTooltip: (tooltip: TooltipState) => void`
- `onHideTooltip: () => void`

**Usage:**
```tsx
<CategoryGrid
  categories={AI_CATEGORIES}
  onShowTooltip={showTooltip}
  onHideTooltip={hideTooltip}
/>
```

### CategoryCard (`src/components/features/CategoryCard.tsx`)
Individual category card with tools list and animations.

**Props:**
- `category: AICategory`
- `onShowTooltip: (tooltip: TooltipState) => void`
- `onHideTooltip: () => void`

**Features:**
- Hover animations
- Tool list with scrolling
- Tooltip integration
- Responsive design

### SearchBarHeader (`src/components/features/SearchBarHeader.tsx`)
Search input with dropdown functionality.

**Props:**
- `searchTerm: string`
- `isDropdownVisible: boolean`
- `onSearchChange: (value: string) => void`
- `onSearchFocus: () => void`
- `onSearchBlur: () => void`
- `onTopSearchClick: (term: string) => void`

### Tooltip (`src/components/features/Tooltip.tsx`)
Dynamic tooltip component for enhanced UX.

**Props:**
- `tooltip: TooltipState`
- `triggerType: 'hover' | 'click'`

**Features:**
- Positioned tooltips
- Arrow indicators
- Smooth animations
- Responsive positioning

## UI Components

### Button (`src/components/ui/Button.tsx`)
Reusable button component with variants.

**Props:**
- `variant: 'primary' | 'secondary' | 'outline'`
- `size: 'sm' | 'md' | 'lg'`
- `children: React.ReactNode`
- `onClick?: () => void`
- `disabled?: boolean`

**Usage:**
```tsx
<Button variant="primary" size="md" onClick={handleClick}>
  Click Me
</Button>
```

### Card (`src/components/ui/Card.tsx`)
Base card component for consistent styling.

**Props:**
- `children: React.ReactNode`
- `className?: string`
- `hover?: boolean`

### Icon (`src/components/ui/Icon.tsx`)
Wrapper for Lucide React icons with consistent styling.

**Props:**
- `name: string` - Lucide icon name
- `size?: number`
- `className?: string`

### Tag (`src/components/ui/Tag.tsx`)
Badge component for labels like "AI", "NEW", "PREMIUM".

**Props:**
- `children: React.ReactNode`
- `variant: 'default' | 'new' | 'premium'`

## Providers

### SearchProvider (`src/providers/SearchProvider.tsx`)
Global search state management.

**Provides:**
- Search term state
- Search results
- Loading states
- Search handlers

**Usage:**
```tsx
// Wrap your app with SearchProvider
<SearchProvider>
  <App />
</SearchProvider>

// Use in components
const { searchTerm, handleSearchChange } = useSearchContext();
```

## Hooks

### useSearch (`src/hooks/useSearch.ts`)
Custom hook for search functionality.

**Returns:**
- `searchTerm: string`
- `isSearchDropdownVisible: boolean`
- `searchResults: AITool[]`
- `isLoadingSearchResults: boolean`
- `handleSearchFocus: () => void`
- `handleSearchBlur: () => void`
- `handleSearchChange: (value: string) => void`
- `handleTopSearchClick: (term: string) => void`

### useTooltip (`src/hooks/useTooltip.ts`)
Custom hook for tooltip management.

**Returns:**
- `activeTooltip: TooltipState | null`
- `triggerType: 'hover' | 'click'`
- `showTooltip: (tooltip: TooltipState) => void`
- `hideTooltip: () => void`

## Creating New Pages

### Basic Page Structure
```tsx
'use client';

import React from 'react';
// Import any specific components needed

export default function NewPage() {
  return (
    <div className="w-full">
      {/* Page content */}
      <div className="mx-auto px-4 py-8" style={{ maxWidth: 'var(--container-width)' }}>
        <h1 className="text-3xl font-bold text-white mb-6">Page Title</h1>
        {/* Page content */}
      </div>
    </div>
  );
}
```

### Page with Search Integration
```tsx
'use client';

import React from 'react';
import { useSearchContext } from '@/providers/SearchProvider';

export default function SearchablePage() {
  const { searchTerm, searchResults } = useSearchContext();

  return (
    <div className="w-full">
      {/* Use search state as needed */}
      {searchTerm && (
        <div className="mx-auto px-4 py-4" style={{ maxWidth: 'var(--container-width)' }}>
          <p className="text-white">Searching for: {searchTerm}</p>
        </div>
      )}
      {/* Rest of page content */}
    </div>
  );
}
```

## Styling Guidelines

### Consistent Container
```tsx
<div className="mx-auto px-4" style={{ maxWidth: 'var(--container-width)' }}>
  {/* Content */}
</div>
```

### Dark Theme Classes
- Background: `bg-zinc-900`
- Cards: `bg-zinc-800`
- Text: `text-white` or `text-zinc-100`
- Borders: `border-zinc-700`

### Hover Effects
```tsx
className="hover:bg-zinc-700 transition-colors duration-200"
```

### Custom Orange Hover
```tsx
onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)'}
onMouseLeave={(e) => e.currentTarget.style.backgroundColor = ''}
```

## Best Practices

1. **Always use the SearchProvider context** for search-related functionality
2. **Maintain consistent spacing** using the container width variable
3. **Use the established color scheme** for consistency
4. **Include hover effects** for interactive elements
5. **Follow the component structure** outlined in the design system
6. **Use TypeScript interfaces** for prop definitions
7. **Include accessibility features** like ARIA labels
8. **Test responsive behavior** across all breakpoints

## Common Patterns

### Loading States
```tsx
{isLoading ? (
  <div className="flex items-center justify-center py-20">
    <div className="text-white text-lg">Loading...</div>
  </div>
) : (
  // Content
)}
```

### Error States
```tsx
{error ? (
  <div className="text-red-500 text-center py-8">
    {error.message}
  </div>
) : (
  // Content
)}
```

### Empty States
```tsx
{items.length === 0 ? (
  <div className="text-zinc-400 text-center py-12">
    No items found
  </div>
) : (
  // Content
)}
```
