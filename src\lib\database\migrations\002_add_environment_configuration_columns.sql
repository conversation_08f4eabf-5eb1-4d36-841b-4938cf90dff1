-- Migration 002: Add Environment and Configuration Columns to system_configuration
-- This migration adds the missing environment and configuration columns needed by the configuration manager

-- Add environment column to system_configuration table
ALTER TABLE system_configuration 
ADD COLUMN IF NOT EXISTS environment VARCHAR(50) DEFAULT 'development';

-- Add configuration column to store complete configuration objects
ALTER TABLE system_configuration 
ADD COLUMN IF NOT EXISTS configuration JSONB;

-- Create index for environment column for better query performance
CREATE INDEX IF NOT EXISTS idx_system_configuration_environment 
ON system_configuration(environment);

-- Create unique constraint for environment (one config per environment)
CREATE UNIQUE INDEX IF NOT EXISTS idx_system_configuration_environment_unique 
ON system_configuration(environment) 
WHERE configuration IS NOT NULL;

-- Insert default configuration for current environment if not exists
INSERT INTO system_configuration (
    config_key, 
    config_value, 
    config_type, 
    environment, 
    configuration,
    description
) VALUES (
    'default_environment_config',
    '{}'::jsonb,
    'system',
    COALESCE(current_setting('app.environment', true), 'development'),
    jsonb_build_object(
        'aiGeneration', jsonb_build_object(
            'providers', jsonb_build_object(
                'openai', jsonb_build_object(
                    'enabled', true,
                    'model', 'gpt-4o-2024-11-20',
                    'maxTokens', 4000,
                    'temperature', 0.7,
                    'timeout', 30000,
                    'priority', 1
                ),
                'openrouter', jsonb_build_object(
                    'enabled', true,
                    'model', 'google/gemini-2.0-flash-exp:free',
                    'maxTokens', 8000,
                    'temperature', 0.7,
                    'implicitCaching', true,
                    'timeout', 45000,
                    'priority', 2
                )
            ),
            'modelSelection', jsonb_build_object(
                'strategy', 'auto',
                'fallbackOrder', jsonb_build_array('openai', 'openrouter'),
                'costThreshold', 0.01,
                'qualityThreshold', 0.8
            ),
            'contentGeneration', jsonb_build_object(
                'autoApproval', false,
                'qualityThreshold', 0.7,
                'editorialReviewRequired', true,
                'maxRetries', 3,
                'timeoutSeconds', 300
            )
        ),
        'scraping', jsonb_build_object(
            'scrapeDoConfig', jsonb_build_object(
                'enabled', true,
                'timeout', 30000,
                'retryAttempts', 3,
                'costOptimization', jsonb_build_object(
                    'enabled', true,
                    'neverEnhancePatterns', jsonb_build_array('simple-landing', 'basic-info'),
                    'alwaysEnhancePatterns', jsonb_build_array('complex-saas', 'feature-rich')
                )
            ),
            'mediaExtraction', jsonb_build_object(
                'ogImageExtraction', true,
                'faviconCollection', true,
                'screenshotFallback', true,
                'persistentStorage', true
            )
        ),
        'system', jsonb_build_object(
            'contentQualityThreshold', 0.7,
            'autoApprovalEnabled', false,
            'debugMode', false,
            'maintenanceMode', false,
            'security', jsonb_build_object(
                'apiKeyRotationDays', 90,
                'sessionTimeoutMinutes', 60,
                'maxLoginAttempts', 5,
                'auditLogging', true
            ),
            'performance', jsonb_build_object(
                'cacheEnabled', true,
                'cacheTTL', 3600,
                'rateLimiting', true,
                'requestsPerMinute', 100
            )
        ),
        'editorial', jsonb_build_object(
            'workflow', jsonb_build_object(
                'autoAssignment', false,
                'reviewTimeoutHours', 24,
                'escalationEnabled', true,
                'qualityChecks', true
            ),
            'contentStandards', jsonb_build_object(
                'minDescriptionLength', 50,
                'maxDescriptionLength', 500,
                'requiredFields', jsonb_build_array('name', 'description', 'url'),
                'bannedWords', jsonb_build_array()
            )
        )
    ),
    'Default configuration for environment-based configuration management'
) ON CONFLICT (config_key) DO UPDATE SET
    environment = EXCLUDED.environment,
    configuration = EXCLUDED.configuration,
    updated_at = CURRENT_TIMESTAMP;

-- Update existing records to have environment set
UPDATE system_configuration 
SET environment = COALESCE(current_setting('app.environment', true), 'development')
WHERE environment IS NULL;

-- Log migration completion
INSERT INTO system_configuration (config_key, config_value, config_type, description) VALUES
('migration_002_completed', json_build_object('completed_at', CURRENT_TIMESTAMP, 'version', '1.0.1')::jsonb, 'system', 'Environment and configuration columns migration completion marker')
ON CONFLICT (config_key) DO UPDATE SET
    config_value = EXCLUDED.config_value,
    updated_at = CURRENT_TIMESTAMP;
