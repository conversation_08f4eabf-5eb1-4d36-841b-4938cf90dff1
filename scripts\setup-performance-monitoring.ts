#!/usr/bin/env tsx

/**
 * Performance Monitoring Setup Script
 * 
 * Sets up the performance monitoring infrastructure:
 * 1. Creates database tables for metrics and cost tracking
 * 2. Initializes monitoring services
 * 3. Validates the setup
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';
import { performanceMonitor } from '../src/lib/monitoring/performance-monitor';
import { costTracker } from '../src/lib/monitoring/cost-tracker';
import { databaseOptimizer } from '../src/lib/monitoring/database-optimizer';

interface SetupResult {
  success: boolean;
  message: string;
  details?: any;
}

class PerformanceMonitoringSetup {
  private supabase: any;

  constructor() {
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing required environment variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
    }

    this.supabase = createClient(supabaseUrl, supabaseServiceKey);
  }

  /**
   * Execute the complete setup process
   */
  async setup(): Promise<void> {
    console.log('🚀 Starting Performance Monitoring Setup...\n');

    try {
      // Step 1: Create database tables
      const dbResult = await this.createDatabaseTables();
      this.logResult('Database Tables', dbResult);

      // Step 2: Validate table creation
      const validationResult = await this.validateTables();
      this.logResult('Table Validation', validationResult);

      // Step 3: Initialize monitoring services
      const servicesResult = await this.initializeServices();
      this.logResult('Service Initialization', servicesResult);

      // Step 4: Test monitoring functionality
      const testResult = await this.testMonitoring();
      this.logResult('Monitoring Test', testResult);

      // Step 5: Setup database optimization
      const optimizationResult = await this.setupDatabaseOptimization();
      this.logResult('Database Optimization', optimizationResult);

      console.log('\n✅ Performance Monitoring Setup Complete!');
      console.log('\nNext steps:');
      console.log('1. Access the performance dashboard at /admin/performance');
      console.log('2. Configure budget limits and alert thresholds');
      console.log('3. Start monitoring with the dashboard controls');

    } catch (error: any) {
      console.error('\n❌ Setup failed:', error.message);
      process.exit(1);
    }
  }

  /**
   * Create database tables for performance monitoring
   */
  private async createDatabaseTables(): Promise<SetupResult> {
    try {
      // Read SQL file
      const sqlPath = join(__dirname, 'create-performance-monitoring-tables.sql');
      const sqlContent = readFileSync(sqlPath, 'utf-8');

      // Note: Supabase doesn't support direct SQL execution via the client
      // This would need to be run manually in the Supabase SQL editor
      console.log('📝 SQL commands to run in Supabase SQL editor:');
      console.log('---');
      console.log(sqlContent);
      console.log('---');

      return {
        success: true,
        message: 'SQL commands generated. Please run them in Supabase SQL editor.',
        details: { sqlPath }
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to read SQL file: ${error.message}`
      };
    }
  }

  /**
   * Validate that tables were created successfully
   */
  private async validateTables(): Promise<SetupResult> {
    try {
      const requiredTables = ['performance_metrics', 'cost_tracking', 'system_alerts'];
      const validationResults = [];

      for (const table of requiredTables) {
        try {
          // Try to query the table to check if it exists
          const { data, error } = await this.supabase
            .from(table)
            .select('*')
            .limit(1);

          if (error && error.code === '42P01') {
            // Table doesn't exist
            validationResults.push({ table, exists: false, error: 'Table not found' });
          } else if (error) {
            // Other error
            validationResults.push({ table, exists: false, error: error.message });
          } else {
            // Table exists
            validationResults.push({ table, exists: true });
          }
        } catch (err: any) {
          validationResults.push({ table, exists: false, error: err.message });
        }
      }

      const allTablesExist = validationResults.every(result => result.exists);

      return {
        success: allTablesExist,
        message: allTablesExist 
          ? 'All required tables exist' 
          : 'Some tables are missing. Please run the SQL commands in Supabase.',
        details: validationResults
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Table validation failed: ${error.message}`
      };
    }
  }

  /**
   * Initialize monitoring services
   */
  private async initializeServices(): Promise<SetupResult> {
    try {
      // Start performance monitoring
      await performanceMonitor.startMonitoring(60000); // 1 minute intervals

      // Start cost tracking
      await costTracker.startTracking();

      // Set default budget limits
      costTracker.setBudgetLimits({
        openai: 100,      // $100/month
        openrouter: 50,   // $50/month
        scrape_do: 200,   // $200/month
        total: 500        // $500/month total
      });

      return {
        success: true,
        message: 'Monitoring services initialized successfully',
        details: {
          performanceMonitoring: 'Started with 1-minute intervals',
          costTracking: 'Started with default budget limits',
          budgetLimits: costTracker.getBudgetLimits()
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Service initialization failed: ${error.message}`
      };
    }
  }

  /**
   * Test monitoring functionality
   */
  private async testMonitoring(): Promise<SetupResult> {
    try {
      // Test performance metric recording
      await performanceMonitor.recordMetric({
        metric_type: 'api_response',
        metric_name: 'setup_test',
        value: 100,
        unit: 'ms',
        endpoint: '/api/test',
        metadata: { test: true }
      });

      // Test cost recording
      await costTracker.recordCost({
        service_type: 'other',
        service_name: 'setup_test',
        operation_type: 'other',
        cost_amount: 0.001,
        currency: 'USD',
        metadata: { test: true }
      });

      // Test database query monitoring
      await databaseOptimizer.monitorQuery('setup_test', async () => {
        const { data } = await this.supabase
          .from('tools')
          .select('id')
          .limit(1);
        return data;
      });

      return {
        success: true,
        message: 'Monitoring functionality tested successfully',
        details: {
          performanceMetric: 'Recorded test API response metric',
          costTracking: 'Recorded test cost entry',
          queryMonitoring: 'Monitored test database query'
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Monitoring test failed: ${error.message}`
      };
    }
  }

  /**
   * Setup database optimization
   */
  private async setupDatabaseOptimization(): Promise<SetupResult> {
    try {
      // Get index recommendations
      const recommendations = await databaseOptimizer.getIndexRecommendations();

      // Get database health analysis
      const health = await databaseOptimizer.analyzeDatabase();

      return {
        success: true,
        message: 'Database optimization analysis completed',
        details: {
          indexRecommendations: recommendations.length,
          databaseHealth: health,
          recommendations: recommendations.slice(0, 3) // Show first 3 recommendations
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Database optimization setup failed: ${error.message}`
      };
    }
  }

  /**
   * Log setup result
   */
  private logResult(step: string, result: SetupResult): void {
    const icon = result.success ? '✅' : '❌';
    console.log(`${icon} ${step}: ${result.message}`);
    
    if (result.details && Object.keys(result.details).length > 0) {
      console.log('   Details:', JSON.stringify(result.details, null, 2));
    }
    console.log('');
  }
}

// Main execution
async function main() {
  try {
    const setup = new PerformanceMonitoringSetup();
    await setup.setup();
  } catch (error: any) {
    console.error('❌ Setup initialization failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { PerformanceMonitoringSetup };
