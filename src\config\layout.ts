/**
 * Layout Configuration for AI Tools Directory
 * 
 * This file contains configurable layout settings that can be easily modified
 * for different design requirements or admin panel integration.
 */

export interface LayoutConfig {
  // Grid Configuration
  grid: {
    // Gap between cards (0 = no gap, 1-8 = Tailwind gap classes)
    gap: number;
    // Responsive breakpoints for grid columns
    columns: {
      mobile: number;    // grid-cols-{mobile}
      tablet: number;    // md:grid-cols-{tablet}
      desktop: number;   // lg:grid-cols-{desktop}
      wide: number;      // xl:grid-cols-{wide}
    };
  };
  
  // Card Configuration
  card: {
    // Card height settings
    height: {
      // Fixed height mode: all cards have same height
      mode: 'fixed' | 'auto' | 'min-height';
      // Minimum height in pixels (when mode is 'fixed' or 'min-height')
      minHeight: number;
      // Fixed height in pixels (when mode is 'fixed')
      fixedHeight?: number;
    };
    
    // Card spacing and borders
    spacing: {
      // Internal padding (1-8 = Tailwind p-{padding} classes)
      padding: number;
      // Border width (0-8 = Tailwind border-{borderWidth} classes)
      borderWidth: number;
      // Border radius (none, sm, md, lg, xl, 2xl, 3xl, full)
      borderRadius: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | 'full';
    };
    
    // Visual effects
    effects: {
      // Shadow intensity (none, sm, md, lg, xl, 2xl)
      shadow: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
      // Hover shadow intensity
      hoverShadow: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
      // Transition duration in milliseconds
      transitionDuration: number;
    };
  };
  
  // Scrollable Content Configuration
  scrollableContent: {
    // Maximum height for scrollable area in rem
    maxHeight: number;
    // Scrollbar styling
    scrollbar: {
      // Auto-hide scrollbars
      autoHide: boolean;
      // Scrollbar width in pixels
      width: number;
    };
  };
}

// Default configuration - traditional card layout with gaps and shadows
export const defaultLayoutConfig: LayoutConfig = {
  grid: {
    gap: 4, // Gaps between cards for traditional layout
    columns: {
      mobile: 1,
      tablet: 2,
      desktop: 3,
      wide: 4,
    },
  },

  card: {
    height: {
      mode: 'min-height',
      minHeight: 400, // Consistent minimum height
    },

    spacing: {
      padding: 4, // p-4
      borderWidth: 2, // border-2
      borderRadius: 'lg', // Rounded corners for traditional card appearance
    },

    effects: {
      shadow: 'lg', // Card shadows for depth
      hoverShadow: 'xl', // Enhanced shadow on hover
      transitionDuration: 200,
    },
  },

  scrollableContent: {
    maxHeight: 16, // 16rem
    scrollbar: {
      autoHide: true,
      width: 6,
    },
  },
};

// Alternative configurations for different layouts

// Configuration for seamless grid (no gaps, no rounded corners, no shadows)
export const seamlessLayoutConfig: LayoutConfig = {
  ...defaultLayoutConfig,
  grid: {
    ...defaultLayoutConfig.grid,
    gap: 0, // No gaps for seamless grid
  },
  card: {
    ...defaultLayoutConfig.card,
    spacing: {
      ...defaultLayoutConfig.card.spacing,
      borderRadius: 'none', // No rounded corners for seamless grid
    },
    effects: {
      ...defaultLayoutConfig.card.effects,
      shadow: 'none', // No shadows for seamless grid
      hoverShadow: 'none',
    },
  },
};

// Configuration with traditional spaced cards (same as default now)
export const spacedLayoutConfig: LayoutConfig = defaultLayoutConfig;

// Configuration for compact layout
export const compactLayoutConfig: LayoutConfig = {
  ...defaultLayoutConfig,
  card: {
    ...defaultLayoutConfig.card,
    height: {
      mode: 'min-height',
      minHeight: 300,
    },
    spacing: {
      ...defaultLayoutConfig.card.spacing,
      padding: 3, // p-3
    },
  },
  scrollableContent: {
    ...defaultLayoutConfig.scrollableContent,
    maxHeight: 12, // 12rem
  },
};

// Configuration for tall layout
export const tallLayoutConfig: LayoutConfig = {
  ...defaultLayoutConfig,
  card: {
    ...defaultLayoutConfig.card,
    height: {
      mode: 'min-height',
      minHeight: 500,
    },
  },
  scrollableContent: {
    ...defaultLayoutConfig.scrollableContent,
    maxHeight: 20, // 20rem
  },
};

// Helper function to get Tailwind classes from config
export function getLayoutClasses(config: LayoutConfig = defaultLayoutConfig) {
  const { grid, card } = config;
  
  return {
    // Grid classes
    gridContainer: `grid grid-cols-${grid.columns.mobile} md:grid-cols-${grid.columns.tablet} lg:grid-cols-${grid.columns.desktop} xl:grid-cols-${grid.columns.wide} gap-${grid.gap}`,
    
    // Card classes
    cardContainer: [
      'bg-zinc-800',
      `border-${card.spacing.borderWidth}`,
      card.spacing.borderRadius !== 'none' ? `rounded-${card.spacing.borderRadius}` : '',
      `p-${card.spacing.padding}`,
      card.height.mode === 'fixed' ? `h-[${card.height.fixedHeight}px]` : 
      card.height.mode === 'min-height' ? `h-full min-h-[${card.height.minHeight}px]` : 'h-fit',
      card.effects.shadow !== 'none' ? `shadow-${card.effects.shadow}` : '',
      card.effects.hoverShadow !== 'none' ? `hover:shadow-${card.effects.hoverShadow}` : '',
      `transition-all duration-${card.effects.transitionDuration}`,
      'flex flex-col'
    ].filter(Boolean).join(' '),
    
    // Scrollable content classes
    scrollableContent: `flex-1 overflow-y-auto custom-scrollbar mb-4`,
    scrollableContentStyle: { maxHeight: `${config.scrollableContent.maxHeight}rem` },
  };
}

// Export current active configuration
export const activeLayoutConfig = defaultLayoutConfig;
