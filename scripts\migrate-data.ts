import { createClient } from '@supabase/supabase-js';
import { AI_CATEGORIES } from '../src/lib/constants';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

async function migrateData() {
  console.log('🚀 Starting data migration...');

  try {
    // 1. Migrate categories
    console.log('📁 Migrating categories...');
    for (const category of AI_CATEGORIES) {
      const { error: categoryError } = await supabase
        .from('categories')
        .upsert({
          id: category.id,
          title: category.title,
          icon_name: category.iconName,
          description: category.description,
          color_class: category.seeAllButton.colorClass,
          text_color_class: category.seeAllButton.textColorClass,
          meta_title: `${category.title} - AI Tools Directory`,
          meta_description: `Discover the best ${category.title.toLowerCase()} for your projects. ${category.description}`,
        });

      if (categoryError) {
        console.error(`❌ Error migrating category ${category.id}:`, categoryError);
        continue;
      }

      console.log(`✅ Migrated category: ${category.title}`);

      // 2. Migrate tools for this category
      console.log(`🔧 Migrating tools for ${category.title}...`);
      for (const tool of category.tools) {
        const slug = tool.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
        
        const toolData = {
          id: tool.id,
          name: tool.name,
          slug,
          logo_url: tool.logoUrl,
          description: tool.description,
          short_description: tool.description?.substring(0, 150),
          detailed_description: tool.detailedDescription,
          link: tool.link,
          website: tool.website,
          category_id: category.id,
          subcategory: tool.subcategory,
          company: tool.company,
          is_verified: tool.isVerified || false,
          is_claimed: tool.isClaimed || false,
          features: tool.features ? JSON.stringify(tool.features) : null,
          screenshots: tool.screenshots ? JSON.stringify(tool.screenshots) : null,
          pricing: tool.pricing ? JSON.stringify(tool.pricing) : null,
          social_links: tool.socialLinks ? JSON.stringify(tool.socialLinks) : null,
          pros_and_cons: tool.prosAndCons ? JSON.stringify(tool.prosAndCons) : null,
          releases: tool.releases ? JSON.stringify(tool.releases) : null,
          claim_info: tool.claimInfo ? JSON.stringify(tool.claimInfo) : null,
          meta_title: `${tool.name} - AI Tool Review & Features`,
          meta_description: tool.description?.substring(0, 160),
          content_status: 'published',
          published_at: new Date().toISOString(),
        };

        const { error: toolError } = await supabase
          .from('tools')
          .upsert(toolData);

        if (toolError) {
          console.error(`❌ Error migrating tool ${tool.name}:`, toolError);
          continue;
        }

        console.log(`  ✅ Migrated tool: ${tool.name}`);

        // 3. Migrate tags for this tool
        if (tool.tags && tool.tags.length > 0) {
          for (const tag of tool.tags) {
            // First, ensure the tag exists
            const { error: tagError } = await supabase
              .from('tags')
              .upsert({
                name: tag.type,
                type: tag.type,
                color: getTagColor(tag.type),
              });

            if (tagError) {
              console.error(`❌ Error creating tag ${tag.type}:`, tagError);
              continue;
            }

            // Then, link the tool to the tag
            const { data: tagData } = await supabase
              .from('tags')
              .select('id')
              .eq('name', tag.type)
              .single();

            if (tagData) {
              const { error: linkError } = await supabase
                .from('tool_tags')
                .upsert({
                  tool_id: tool.id,
                  tag_id: tagData.id,
                });

              if (linkError) {
                console.error(`❌ Error linking tag ${tag.type} to tool ${tool.name}:`, linkError);
              }
            }
          }
        }

        // 4. Migrate reviews if they exist
        if (tool.reviews && tool.reviews.totalReviews > 0) {
          // Create sample reviews based on highlights
          const highlights = tool.reviews.highlights || [];
          for (let i = 0; i < Math.min(highlights.length, 3); i++) {
            const { error: reviewError } = await supabase
              .from('reviews')
              .insert({
                tool_id: tool.id,
                user_name: `User${i + 1}`,
                rating: Math.floor(tool.reviews.rating),
                title: `Great ${tool.name} experience`,
                content: highlights[i],
                is_approved: true,
              });

            if (reviewError) {
              console.error(`❌ Error creating review for ${tool.name}:`, reviewError);
            }
          }
        }
      }
    }

    console.log('🎉 Data migration completed successfully!');
    
    // Print summary
    const { count: categoriesCount } = await supabase
      .from('categories')
      .select('*', { count: 'exact', head: true });
    
    const { count: toolsCount } = await supabase
      .from('tools')
      .select('*', { count: 'exact', head: true });
    
    const { count: tagsCount } = await supabase
      .from('tags')
      .select('*', { count: 'exact', head: true });

    console.log('\n📊 Migration Summary:');
    console.log(`Categories: ${categoriesCount}`);
    console.log(`Tools: ${toolsCount}`);
    console.log(`Tags: ${tagsCount}`);

  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

function getTagColor(tagType: string): string {
  const colorMap: Record<string, string> = {
    'Trending': '#ff6b6b',
    'New': '#4ecdc4',
    'Premium': '#ffd93d',
    'AI': '#6c5ce7',
    'HOT': '#fd79a8',
    'Featured': '#00b894',
  };
  
  return colorMap[tagType] || '#74b9ff';
}

// Run migration if this file is executed directly
if (require.main === module) {
  migrateData();
}

export { migrateData };
