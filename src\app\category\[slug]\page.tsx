import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { CategoryToolsPage } from '@/components/features/CategoryToolsPage';
import { findCategoryBySlug, getToolsForCategory } from '@/lib/categoryUtils';
import { getCategories } from '@/lib/supabase';

interface CategoryPageProps {
  params: {
    slug: string;
  };
}

// Generate metadata for SEO
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { slug } = await params;
  const category = await findCategoryBySlug(slug);

  if (!category) {
    return {
      title: 'Category Not Found - AI Dude Directory',
      description: 'The requested AI tools category could not be found.',
    };
  }

  // Get tools count for this category
  const tools = await getToolsForCategory(category.id);
  const toolCount = tools.length;
  const title = `${category.title} - ${toolCount} AI Tools | AI Dude Directory`;
  const description = `Discover ${toolCount} ${category.title.toLowerCase()} tools. ${category.description}. Find the best AI tools for your needs.`;

  return {
    title,
    description,
    keywords: `${category.title}, AI tools, artificial intelligence, ${category.id}, AI directory`,
    openGraph: {
      title,
      description,
      type: 'website',
      url: `/category/${slug}`,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    },
    alternates: {
      canonical: `/category/${slug}`,
    },
  };
}

// Generate static params for all categories (for static generation)
export async function generateStaticParams() {
  // Disable static generation for now to avoid build issues
  return [];
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { slug } = await params;
  const category = await findCategoryBySlug(slug);

  if (!category) {
    notFound();
  }

  // Get all tools for this category
  const tools = await getToolsForCategory(category.id);

  return (
    <CategoryToolsPage
      category={category}
      initialTools={tools}
    />
  );
}
