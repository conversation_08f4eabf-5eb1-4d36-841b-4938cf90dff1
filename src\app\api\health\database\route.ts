import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * Database Health Check API
 * Tests database connectivity and basic operations
 */
export async function GET(request: NextRequest) {
  try {
    const startTime = Date.now();

    // Test basic database connectivity
    const { data, error } = await supabase
      .from('tools')
      .select('id')
      .limit(1);

    const responseTime = Date.now() - startTime;

    if (error) {
      return NextResponse.json(
        {
          status: 'unhealthy',
          error: error.message,
          responseTime,
          timestamp: new Date().toISOString()
        },
        { status: 503 }
      );
    }

    // Test write capability (optional)
    const writeTest = await supabase
      .from('system_configuration')
      .select('id')
      .limit(1);

    return NextResponse.json({
      status: 'healthy',
      responseTime,
      timestamp: new Date().toISOString(),
      details: {
        readTest: 'passed',
        writeTest: writeTest.error ? 'failed' : 'passed',
        recordsAccessible: data?.length || 0
      }
    });

  } catch (error: any) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 503 }
    );
  }
}
