/**
 * TypeScript interfaces for the enhanced scrape.do API integration
 * Supports cost optimization, multi-page scraping, and media collection
 */

// Core scrape.do API interfaces

/**
 * Configuration for the scrape.do API client
 * @interface ScrapeDoConfig
 */
export interface ScrapeDoConfig {
  /** API key for scrape.do service */
  apiKey: string;
  /** Base URL for the scrape.do API */
  baseUrl: string;
  /** Request timeout in milliseconds */
  timeout: number;
  /** Maximum number of retry attempts */
  retryAttempts: number;
  /** Delay between retries in milliseconds */
  retryDelay: number;
}

/**
 * Options for configuring scrape.do API requests
 * @interface ScrapeOptions
 */
export interface ScrapeOptions {
  // Proxy Configuration
  /** Use residential/mobile proxy (super=true) - costs 10x more but better for difficult sites */
  useResidentialProxy?: boolean;
  /** Country code for geo-targeting (e.g., 'us', 'uk') */
  geoTargeting?: string;
  /** Session ID for consistent IP across requests */
  stickySession?: number;

  // Browser Configuration
  /** Enable headless browser rendering (5x cost multiplier) */
  enableJSRendering?: boolean;
  /** Device type for viewport simulation */
  deviceType?: 'desktop' | 'mobile' | 'tablet';
  /** Wait condition for page loading */
  waitCondition?: 'domcontentloaded' | 'load' | 'networkidle0' | 'networkidle2';
  /** Additional wait time in milliseconds after page load */
  customWaitTime?: number;
  /** CSS selector to wait for before considering page loaded */
  waitForSelector?: string;

  // Output Configuration
  /** Output format - 'markdown' is optimized for AI processing */
  outputFormat?: 'raw' | 'markdown';
  /** Capture standard viewport screenshot */
  captureScreenshot?: boolean;
  /** Capture full page screenshot (more expensive) */
  fullPageScreenshot?: boolean;
  /** Include network request data in response */
  includeNetworkRequests?: boolean;
  /** Return JSON response format (required for screenshots) */
  returnJSON?: boolean;

  // Performance Optimization
  /** Block CSS/images for faster scraping and cost reduction */
  blockResources?: boolean;
  /** Request timeout in milliseconds */
  timeout?: number;
  /** Retry timeout in milliseconds */
  retryTimeout?: number;
}

/**
 * Result from a scrape.do API request
 * @interface ScrapeResult
 */
export interface ScrapeResult {
  /** Whether the scraping operation was successful */
  success: boolean;
  /** Scraped content (markdown format for AI processing) */
  content: string;
  /** Metadata about the scraping operation */
  metadata?: {
    /** Number of credits consumed */
    creditsUsed?: number;
    /** Type of request made (e.g., 'Datacenter Proxy', 'Residential + Browser') */
    requestType?: string;
    /** Type of proxy used */
    proxyType?: string;
    /** Whether browser rendering was enabled */
    browserEnabled?: boolean;
    /** Type of page scraped (for multi-page scraping) */
    pageType?: string;
    /** Method used to discover the page */
    foundMethod?: string;
    /** Confidence score for page discovery */
    confidence?: number;
    /** Processing time in milliseconds */
    processingTime?: number;
    /** Whether content passed validation */
    validation?: boolean;
    /** Quality score of the scraped content */
    qualityScore?: number;
    /** Remaining credits in account */
    remainingCredits?: number;
    /** Final resolved URL after redirects */
    resolvedUrl?: string;
    /** HTTP status code from target website */
    statusCode?: number;
    /** Whether screenshot was captured */
    hasScreenshot?: boolean;
    /** Number of network requests captured */
    networkRequestCount?: number;
    /** Number of frames detected */
    frameCount?: number;
    /** Number of websocket connections */
    websocketCount?: number;
  };
  /** ISO timestamp of when the scraping was performed */
  timestamp: string;
  /** Error message if scraping failed */
  error?: string;
  /** URL that was scraped */
  url?: string;
  /** Base64 encoded screenshot (when returnJSON=true) */
  screenshot?: string;
  /** Base64 encoded full screenshot (when returnJSON=true) */
  fullScreenshot?: string;
  /** Array of screenshot objects (actual API field) */
  screenShots?: ScrapeDoScreenshot[];
  /** Network requests captured (when returnJSON=true) */
  networkRequests?: NetworkRequest[];
  /** Frames detected (when returnJSON=true) */
  frames?: FrameInfo[];
  /** Websocket connections (when returnJSON=true) */
  websockets?: WebSocketInfo[];
}

// Content analysis interfaces

/**
 * Analysis result for scraped content quality and enhancement needs
 * @interface ContentAnalysis
 */
export interface ContentAnalysis {
  /** Whether the page has substantial meta tag presence */
  hasMetaTags: boolean;
  /** Whether loading indicators (spinners, "Loading..." text) were detected */
  hasLoadingIndicators: boolean;
  /** Whether the page has meaningful content beyond meta tags and loading indicators */
  hasSubstantialContent: boolean;
  /** Whether the content has proper structure (headings, paragraphs, lists) */
  hasStructure: boolean;
  /** Ratio of clean content to total content (0-1) */
  contentRatio: number;
  /** Final decision whether enhanced scraping is needed */
  needsEnhancedScraping: boolean;
  /** Confidence score for the analysis (0-100) */
  confidence: number;
  /** Detected scenario for debugging purposes */
  scenario?: string;
}

export interface CostBenefitAnalysis {
  enhancementProbability: number;
  expectedImprovement: number;
  worthEnhancing: boolean;
  reasoning: string;
}

// Media collection interfaces
export interface MediaAsset {
  type: 'og:image' | 'twitter:image' | 'facebook:image' | 'favicon' | 'screenshot';
  url: string;
  priority: number;
  metadata?: {
    width?: number;
    height?: number;
    format?: string;
    size?: number;
  };
}

export interface ImageCollection {
  favicon: string[] | null;
  ogImages: MediaAsset[];
  screenshot: ScreenshotResult | null;
}

export interface ScreenshotResult {
  success: boolean;
  screenshot?: string; // Base64 encoded image
  metadata?: {
    width: number;
    height: number;
    fullPage: boolean;
    capturedAt: string;
  };
  error?: string;
  timestamp: string;
}

export interface FaviconResult {
  faviconUrls: string[];
  primaryFavicon: string | null;
  storedPath?: string | null;
  extractedAt: string;
  error?: string;
}

// Multi-page scraping interfaces

/**
 * Configuration for multi-page scraping functionality
 * @interface MultiPageScrapingConfig
 */
export interface MultiPageScrapingConfig {
  /** Whether multi-page scraping is enabled */
  enabled: boolean;
  /** Scraping mode - immediate, queue for later, or conditional based on credits */
  mode: 'immediate' | 'queue_for_later' | 'conditional';
  /** Maximum number of additional pages to scrape per tool */
  maxPagesPerTool: number;
  /** Minimum credits required before attempting multi-page scraping */
  creditThreshold: number;

  /** Configuration for different page types */
  pageTypes: {
    pricing: PageTypeConfig;
    faq: PageTypeConfig;
    features: PageTypeConfig;
    about: PageTypeConfig;
  };

  /** Fallback strategies when pages are not found */
  fallbackStrategy: {
    /** Look for content in main page first before scraping separate pages */
    searchInMainPage: boolean;
    /** Follow navigation links to discover pages */
    useNavigation: boolean;
    /** Check sitemap.xml for page discovery */
    useSitemap: boolean;
  };
}

export interface PageTypeConfig {
  enabled: boolean;
  priority: 'high' | 'medium' | 'low';
  patterns: string[]; // URL patterns to detect pages
  selectors: string[]; // CSS selectors for content
  required: boolean; // Whether to fail if not found
}

export interface PageDiscoveryResult {
  pageType: 'pricing' | 'faq' | 'features' | 'about';
  url: string;
  confidence: number; // 0-100 confidence score
  foundMethod: 'navigation' | 'pattern' | 'content' | 'sitemap';
  priority: 'high' | 'medium' | 'low';
  estimatedCredits: number;
}

export interface ScrapingDecision {
  scrapeNow: PageDiscoveryResult[];
  queueForLater: PageDiscoveryResult[];
  skipPages: PageDiscoveryResult[];
  reason: string;
}

// Cost optimization interfaces
export interface CostSavingsEstimate {
  neverEnhanceSavings: number;
  alwaysEnhanceSavings: number;
  totalEstimatedSavings: number;
  estimatedTotalCost: number;
  savingsPercentage: number;
}

export interface CategorizedUrls {
  neverEnhance: string[];      // 1 credit each - 80% cost savings
  alwaysEnhance: string[];     // 5 credits each - skip basic attempt
  unknown: string[];           // Variable cost - intelligent detection
}

export interface BatchResult {
  totalUrls: number;
  successfulScrapes: number;
  failedScrapes: number;
  totalCreditsUsed: number;
  estimatedSavings: number;
  results: ScrapeResult[];
  processingTime: number;
}

// Usage monitoring interfaces
export interface UsageStats {
  isActive: boolean;
  concurrentRequests: number;
  maxMonthlyRequests: number;
  remainingConcurrentRequests: number;
  remainingMonthlyRequests: number;
  lastUpdated: string;
}

// Validation interfaces
export interface ValidationResult {
  isValid: boolean;
  issues: string[];
  contentLength: number;
  url: string;
  qualityScore: number;
}

// Network monitoring interfaces
export interface NetworkRequest {
  url: string;
  method: string;
  status: number;
  headers: Record<string, string>;
  timestamp: string;
}

export interface FrameInfo {
  id: string;
  url: string;
  name?: string;
}

export interface WebSocketInfo {
  url: string;
  readyState: number;
  protocol?: string;
}

export interface NetworkScrapeResult {
  content: string;
  networkRequests?: NetworkRequest[];
  frames?: FrameInfo[];
  websockets?: WebSocketInfo[];
  headers?: Record<string, string>;
}

export interface WebhookResponse {
  success: boolean;
  message: string;
  webhookUrl: string;
  timestamp: string;
}

// Model selection interfaces for AI integration
export interface ModelSelectionCriteria {
  contentSize: number;
  complexity: 'simple' | 'moderate' | 'complex';
  priority: 'speed' | 'quality' | 'cost';
}

export interface ModelConfig {
  provider: 'openai' | 'openrouter';
  model: string;
  maxTokens: number;
  reasoning: string;
}

// Enhanced scraping workflow interfaces
export interface EnhancedScrapeRequest {
  url: string;
  options: ScrapeOptions;
  multiPageConfig?: MultiPageScrapingConfig;
  mediaCollection?: boolean;
  costOptimization?: boolean;
  persistentStorage?: boolean;
}

export interface EnhancedScrapeResult extends ScrapeResult {
  mediaAssets?: ImageCollection;
  additionalPages?: ScrapeResult[];
  costAnalysis?: {
    creditsUsed: number;
    estimatedSavings: number;
    optimizationStrategy: string;
  };
  contentAnalysis?: ContentAnalysis;
}

// Error handling interfaces
export interface ScrapeError {
  code: string;
  message: string;
  url: string;
  timestamp: string;
  retryable: boolean;
  context?: Record<string, unknown>;
}

export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

// Persistent data storage interfaces
export interface StorageResult {
  success: boolean;
  filePath?: string;
  error?: string;
  timestamp: string;
}

export interface ScrapedDataRecord {
  url: string;
  content: string;
  mediaAssets?: ImageCollection;
  additionalPages?: ScrapeResult[];
  contentAnalysis?: ContentAnalysis;
  costAnalysis?: {
    creditsUsed: number;
    estimatedSavings: number;
    optimizationStrategy: string;
  };
  scrapedAt: string;
  storedAt: string;
}

export interface DataStorageConfig {
  baseDirectory: string;
  fileFormat: 'markdown' | 'json';
  includeMetadata: boolean;
  organizationStrategy: 'flat' | 'by_domain' | 'by_date';
  maxFileSize: number;
  compressionEnabled: boolean;
}

// Scrape.do screenshot object structure
export interface ScrapeDoScreenshot {
  type: 'ScreenShot' | 'FullScreenShot';
  image: string; // Base64 encoded image data
  error?: string; // Error message if screenshot failed
}

// Scrape.do JSON response interfaces (when returnJSON=true)
export interface ScrapeDoJSONResponse {
  content: string;
  screenshot?: string; // Base64 encoded screenshot (legacy field)
  fullScreenshot?: string; // Base64 encoded full screenshot (legacy field)
  screenShots?: ScrapeDoScreenshot[]; // Array of screenshot objects (actual API field)
  networkRequests?: NetworkRequest[];
  websocketRequests?: WebSocketInfo[];
  actionResults?: any[];
  frames?: FrameInfo[];
  websockets?: WebSocketInfo[];
  headers?: Record<string, string>;
  cookies?: string;
  resolvedUrl?: string;
  statusCode?: number;
}
