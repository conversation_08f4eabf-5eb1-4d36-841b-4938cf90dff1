'use client';

import { PerformanceDashboard } from '@/components/admin/PerformanceDashboard';

export default function PerformanceMonitoringPage() {
  return (
    <div className="min-h-screen bg-zinc-900 text-white font-roboto">
      <div className="container mx-auto px-4 py-8" style={{ maxWidth: 'var(--container-width)' }}>
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Performance Monitoring</h1>
            <p className="text-gray-300">System performance optimization and monitoring dashboard</p>
          </div>
          
          <div className="flex items-center space-x-4">
            <a
              href="/admin"
              className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
            >
              ← Back to Admin
            </a>
          </div>
        </div>

        {/* Performance Dashboard */}
        <PerformanceDashboard />
      </div>
    </div>
  );
}
