'use client';

import { useState, useCallback } from 'react';
import { TooltipData } from '@/lib/types';

export function useTooltip() {
  const [activeTooltip, setActiveTooltip] = useState<TooltipData | null>(null);
  const [triggerType, setTriggerType] = useState<'title' | 'search-icon'>('title');

  const showTooltip = useCallback((content: string, targetElement: HTMLElement, triggerType: 'title' | 'search-icon' = 'title') => {
    const rect = targetElement.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Determine optimal position based on available space
    let position: TooltipData['position'] = 'top';

    if (rect.top < 100) {
      position = 'bottom';
    } else if (rect.left < 100) {
      position = 'right';
    } else if (rect.right > viewportWidth - 100) {
      position = 'left';
    }

    setTriggerType(triggerType);
    setActiveTooltip({
      content,
      targetRect: rect,
      position,
    });
  }, []);

  const hideTooltip = useCallback(() => {
    setActiveTooltip(null);
  }, []);

  return {
    activeTooltip,
    triggerType,
    showTooltip,
    hideTooltip,
  };
}
