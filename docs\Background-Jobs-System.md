# Background Jobs System

## Overview

The AI Dude Directory uses a custom background job processing system built with Next.js API routes. This system replaces the previous n8n automation platform with a simpler, more maintainable solution that runs entirely within the Next.js application.

## Architecture

### Components

1. **Job Queue** (`src/lib/jobs/queue.ts`)
   - In-memory job storage
   - Configurable concurrency limits
   - Automatic retry with exponential backoff
   - Priority-based processing

2. **Job Handlers** (`src/lib/jobs/handlers/`)
   - Modular job processing logic
   - Type-safe job data handling
   - Error handling and logging

3. **API Endpoints** (`src/app/api/automation/`)
   - Job creation and management
   - Status monitoring
   - Admin controls

## Job Types

### 1. Tool Submission (`TOOL_SUBMISSION`)
**Purpose**: Process new tool submissions end-to-end

**Data Structure**:
```typescript
{
  url: string;
  name: string;
  description?: string;
  category?: string;
  submitterEmail: string;
  submitterName?: string;
}
```

**Process Flow**:
1. Scrape the tool website
2. Generate AI content
3. Create tool draft in database
4. Send notification emails

### 2. Content Generation (`CONTENT_GENERATION`)
**Purpose**: Generate AI-powered content for tools

**Data Structure**:
```typescript
{
  url: string;
  scrapedData: any;
  pricingData?: any;
  faqData?: any;
  toolId?: string;
}
```

**Features**:
- GPT-4 powered content creation
- Irreverent, witty writing style
- SEO optimization
- Structured JSON output

### 3. Web Scraping (`WEB_SCRAPING`)
**Purpose**: Extract data from tool websites

**Data Structure**:
```typescript
{
  url: string;
  options?: {
    timeout?: number;
    waitForSelector?: string;
    extractImages?: boolean;
    extractLinks?: boolean;
  };
}
```

**Capabilities**:
- Puppeteer-based scraping
- Screenshot capture
- Metadata extraction
- Pricing and FAQ detection

### 4. Email Notification (`EMAIL_NOTIFICATION`)
**Purpose**: Send automated emails

**Data Structure**:
```typescript
{
  to: string | string[];
  subject: string;
  template: string;
  data: any;
  priority?: 'low' | 'normal' | 'high';
}
```

**Templates**:
- `tool-submission-received`
- `admin-tool-submission`
- `admin-processing-error`
- `tool-approved`
- `tool-rejected`

## Configuration

### Environment Variables

```bash
# Background Jobs & Automation
JOB_QUEUE_ENABLED=true
MAX_CONCURRENT_JOBS=3
JOB_RETRY_ATTEMPTS=3

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
ADMIN_EMAIL=<EMAIL>
```

### Job Options

```typescript
interface JobOptions {
  priority?: JobPriority; // LOW, NORMAL, HIGH, URGENT
  delay?: number;         // Delay in milliseconds
  maxAttempts?: number;   // Override default retry attempts
  scheduledFor?: Date;    // Schedule for future execution
}
```

## API Usage

### Create a Job

```bash
POST /api/automation/jobs
Content-Type: application/json
x-api-key: your-admin-api-key

{
  "type": "TOOL_SUBMISSION",
  "data": {
    "url": "https://example.com",
    "name": "Example Tool",
    "submitterEmail": "<EMAIL>"
  },
  "options": {
    "priority": "NORMAL"
  }
}
```

### List Jobs

```bash
GET /api/automation/jobs?status=pending&limit=20&offset=0
x-api-key: your-admin-api-key
```

### Get Job Details

```bash
GET /api/automation/jobs/job_123456789_abc123def
x-api-key: your-admin-api-key
```

### Retry Failed Job

```bash
POST /api/automation/jobs/job_123456789_abc123def
Content-Type: application/json
x-api-key: your-admin-api-key

{
  "action": "retry"
}
```

## Error Handling

### Retry Logic
- Exponential backoff: 2^attempt seconds
- Configurable max attempts (default: 3)
- Failed jobs remain in queue for manual retry

### Error Notifications
- Admin email notifications for processing failures
- Detailed error logging
- Job status tracking

## Monitoring

### Job Status
- `PENDING` - Waiting to be processed
- `PROCESSING` - Currently being executed
- `COMPLETED` - Successfully finished
- `FAILED` - Failed after all retry attempts
- `RETRYING` - Failed but will retry
- `CANCELLED` - Manually cancelled

### Metrics
- Job completion rates
- Processing times
- Error frequencies
- Queue depth

## Migration from n8n

### What Changed
1. **Removed n8n dependency** - No separate automation platform needed
2. **Simplified deployment** - Everything runs in Next.js
3. **Better error handling** - Integrated with application logging
4. **Type safety** - Full TypeScript support
5. **Easier debugging** - Standard Node.js debugging tools

### Benefits
- **Reduced complexity** - One less service to manage
- **Better performance** - No network overhead between services
- **Easier maintenance** - Single codebase
- **Cost effective** - No additional hosting costs
- **Better monitoring** - Integrated with application metrics

## Development

### Adding New Job Types

1. **Define the job type**:
```typescript
// src/lib/jobs/types.ts
export enum JobType {
  // ... existing types
  NEW_JOB_TYPE = 'new_job_type',
}
```

2. **Create job handler**:
```typescript
// src/lib/jobs/handlers/new-job-handler.ts
export class NewJobHandler implements JobHandler {
  async handle(job: Job): Promise<any> {
    // Implementation
  }
}
```

3. **Register handler**:
```typescript
// src/lib/jobs/handlers/index.ts
const handlers: Record<JobType, JobHandler> = {
  // ... existing handlers
  [JobType.NEW_JOB_TYPE]: new NewJobHandler(),
};
```

### Testing

```bash
# Test job creation
curl -X POST http://localhost:3000/api/automation/jobs \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-admin-api-key" \
  -d '{"type":"WEB_SCRAPING","data":{"url":"https://example.com"}}'

# Monitor job status
curl http://localhost:3000/api/automation/jobs \
  -H "x-api-key: your-admin-api-key"
```

## Production Considerations

### Scaling
- Consider Redis-based queue for multiple instances
- Implement job persistence for reliability
- Add job result caching

### Monitoring
- Set up alerts for failed jobs
- Monitor queue depth and processing times
- Track job completion rates

### Security
- Validate job data thoroughly
- Implement rate limiting
- Secure admin API endpoints

## Troubleshooting

### Common Issues

**Jobs not processing**:
- Check `JOB_QUEUE_ENABLED` environment variable
- Verify job queue is started
- Check for JavaScript errors in logs

**Email notifications failing**:
- Verify SMTP configuration
- Check email credentials
- Test with simple email job

**High memory usage**:
- Monitor job queue size
- Implement job cleanup
- Consider external queue for production
