# AI Dude Directory - UI Design System

## Overview

This document outlines the comprehensive design system for the AI Dude Directory, ensuring consistency across all pages and components.

## Color Scheme

### Dark Theme (Primary)
- **Background**: `#18181b` (zinc-900)
- **Text Primary**: `#f4f4f5` (zinc-100)
- **Text Secondary**: `#a1a1aa` (zinc-400)
- **Card Background**: `#27272a` (zinc-800)
- **Card Hover**: `#3f3f46` (zinc-700)
- **Border**: `#3f3f46` (zinc-700)

### Accent Colors
- **Primary Orange**: `rgb(255, 150, 0)` (Custom orange for default state)
- **Orange Hover**: `rgb(255, 170, 30)` (Custom orange for hover state)
- **Orange Background**: `#fb923c` (orange-400) for footer
- **Success**: `#22c55e` (green-500)
- **Warning**: `#f59e0b` (amber-500)
- **Error**: `#ef4444` (red-500)
- **Black Borders**: `#000000` (black) for featured cards and enhanced contrast

## Typography

### Font Family
- **Primary**: 'Roboto', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif

### Font Weights
- **Light**: 300
- **Regular**: 400
- **Medium**: 500
- **Bold**: 700

### Text Sizes
- **Heading 1**: `text-3xl` (30px)
- **Heading 2**: `text-2xl` (24px)
- **Heading 3**: `text-xl` (20px)
- **Body**: `text-base` (16px)
- **Small**: `text-sm` (14px)
- **Extra Small**: `text-xs` (12px)

## Layout Dimensions

### CSS Custom Properties
```css
:root {
  --top-bar-height: 34px;
  --header-mob-height: 70px;
  --header-desk-height: 210px;
  --container-width: 1150px;
}
```

### Header Specifications
- **Left Column**: 460px × 146px
- **Middle Column**: 410px × 146px
- **Mascot**: 260px × 227px
- **Max Header Height**: 232px
- **Social Icons**: 40px × 40px
- **Callout Component**: 410px × 90px

### Responsive Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px - 1280px
- **Wide**: > 1280px

## Component Patterns

### Cards
- **Background**: `bg-zinc-800`
- **Border**: `border border-zinc-700` (standard cards) or `border-2 border-black` (featured cards)
- **Padding**: `p-4`
- **Border Radius**: `rounded-lg`
- **Shadow**: `shadow-lg`
- **Hover Shadow**: `hover:shadow-xl`
- **Transition**: `transition-colors duration-200`

#### Featured Cards (Sidebar)
- **Border**: `border-2 border-black` for enhanced definition
- **Background**: Glassmorphism with `rgba(39, 39, 42, 0.7)` and backdrop blur
- **Text Colors**: White for titles, gray for descriptions
- **Content**: Tool name, verification badge, tags (max 2), description
- **Removed Elements**: Rating/review information, category information

### Layout Alignment Standards
- **Vertical Alignment**: All cards in the same row must start at the same vertical level
- **Grid Consistency**: Use consistent negative margins (`-mt-4`) for page containers
- **Card Positioning**: Avoid additional top margins on individual cards within grid layouts
- **Hero Sections**: Should align with sidebar components at the same grid level
- **Container Width**: Use `--container-width` CSS custom property (1150px) for consistent content width

### Buttons
- **Primary**: Custom orange `rgb(255, 150, 0)` background with white text
- **Primary Hover**: Custom orange `rgb(255, 170, 30)` background
- **Secondary**: Zinc-800 background with white text
- **Secondary Hover**: Zinc-600 background
- **Transition**: `200ms ease-in-out`
- **Implementation**: Use inline styles for custom orange colors instead of Tailwind classes

#### Interactive Elements with Custom Orange
- **Navigation Tabs**: Active state uses `rgb(255, 150, 0)` with `rgb(255, 170, 30)` hover
- **Scroll Button**: Custom orange with progress indicator
- **Chat with AI Buttons**: Both floating buttons use custom orange colors
- **CTA Buttons**: Primary actions use custom orange color scheme

### Grid Layout
- **Gap**: `gap-4` (16px)
- **Columns**: 
  - Mobile: 1 column
  - Tablet: 2 columns
  - Desktop: 3 columns
  - Wide: 4 columns

## Animation Standards

### Transitions
- **Default Duration**: 200ms
- **Easing**: `ease-in-out`
- **Hover Effects**: Scale, color, shadow changes

### Specific Animations
- **Underline Expansion**: Center-outward using `transform: scaleX()`
- **Icon Hover**: Rotation and scale effects
- **Card Hover**: Shadow enhancement and slight color shift

### Custom Animations
```css
.wave-animation {
  animation: wave 3s ease-in-out infinite;
}

.shine-effect {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: shine 2s infinite;
}
```

## Icon Standards

### Size Guidelines
- **Small Icons**: 16px
- **Medium Icons**: 20px
- **Large Icons**: 24px
- **Social Icons**: 40px × 40px

### Icon Library
- **Primary**: Lucide React icons
- **Style**: Consistent stroke width and style

## Spacing System

### Padding/Margin Scale
- **xs**: 4px (`p-1`)
- **sm**: 8px (`p-2`)
- **md**: 16px (`p-4`)
- **lg**: 24px (`p-6`)
- **xl**: 32px (`p-8`)
- **2xl**: 48px (`p-12`)

## Interactive States

### Hover Effects
- **Cards**: Background color change + shadow enhancement
- **Buttons**: Background color to custom orange `rgb(255, 170, 30)`
- **Icons**: Rotation + scale effects
- **Links**: Color change + underline animation
- **Navigation Tabs**: Active tabs change to hover orange color `rgb(255, 170, 30)`
- **Floating Buttons**: All floating UI elements use custom orange hover states

### Focus States
- **Inputs**: Border color change + outline
- **Buttons**: Outline with custom orange color
- **Links**: Underline + color change

## Floating UI Elements

### Chat with AI Buttons
- **Bottom Right**: Fixed positioned with MessageCircle icon
- **Bottom Center**: Fixed positioned with pulse-glow animation
- **Colors**: Custom orange `rgb(255, 150, 0)` default, `rgb(255, 170, 30)` hover
- **Positioning**: `fixed bottom-6 right-6` and `fixed bottom-6 left-1/2 transform -translate-x-1/2`
- **Z-Index**: `z-30` for proper layering

### Scroll Progress Button
- **Position**: `fixed right-6 top-1/2 transform -translate-y-1/2`
- **Progress Indicator**: Conic gradient showing scroll progress
- **Colors**: Custom orange `rgb(255, 150, 0)` with progress visualization
- **Behavior**: Shows down arrow above fold, up arrow below fold
- **Animation**: Smooth transitions and fade-in effects

### Section Navigation Tabs
- **Layout**: Horizontal flex layout with gap-2
- **Active State**: Custom orange `rgb(255, 150, 0)` background
- **Active Hover**: Custom orange `rgb(255, 170, 30)` background
- **Inactive State**: Gray text with zinc-700 hover background
- **Border**: Bottom border accent with `rgb(255, 170, 30)` for active tabs
- **Icons**: 16px Lucide icons with consistent spacing

## Accessibility

### Color Contrast
- Ensure minimum 4.5:1 contrast ratio for normal text
- Ensure minimum 3:1 contrast ratio for large text

### Focus Indicators
- Visible focus indicators for all interactive elements
- Custom orange color for focus states

### Screen Reader Support
- Proper ARIA labels and descriptions
- Semantic HTML structure

## Component Usage Guidelines

### Header Component
- Consistent across all pages
- Responsive design with mobile/desktop variants
- Search functionality integrated
- Social icons with hover animations

### Footer Component
- Orange background with enhanced contrast
- Social icons with rotation effects
- Wave animations for text elements
- Consistent across all pages

### Category Cards
- Minimum height for consistency
- Hover effects with tooltips
- Responsive grid layout
- Auto-hiding scrollbars for content

### Tool Detail Pages
- **Layout**: Left content (3/4 width) + right sidebar (1/4 width)
- **Grid System**: `lg:grid-cols-4` with main content spanning 3 columns
- **Hero Section**: Must align with featured tools sidebar (no additional top margins)
- **Sidebar**: Sticky positioned for better UX
- **Spacing**: Consistent `gap-4` between grid items
- **Responsive**: Stacks vertically on mobile devices

### Featured Tools Sidebar
- **Border**: Black borders (`border-2 border-black`) for enhanced definition
- **Background**: Glassmorphism effect with backdrop blur
- **Content Structure**:
  - Tool logo (46x46px)
  - Tool name (white text, orange hover)
  - Verification badge (if applicable)
  - Tags (maximum 2, original colors)
  - Description (gray text, 2-line clamp)
  - External link icon (gray, orange hover)
- **Removed Elements**:
  - Rating/review information
  - Category information
- **Hover Effects**: Orange color transitions for interactive elements

### Search Components
- Dropdown with top searches
- Loading states
- Results display with consistent card styling

## File Organization

### Component Structure
```
src/
├── components/
│   ├── layout/          # Header, Footer, Layout components
│   ├── features/        # Feature-specific components
│   ├── ui/             # Reusable UI components
│   └── admin/          # Admin panel components
├── providers/          # Context providers
├── hooks/             # Custom React hooks
├── lib/               # Constants and utilities
├── config/            # Configuration files
└── styles/            # Global styles
```

This design system ensures consistency and maintainability across the entire AI Dude Directory application.
