# Layout Configuration Guide

## Overview

The AI Tools Directory now includes a flexible layout configuration system that allows for easy customization of the grid layout, card appearance, and spacing. This system is designed to be easily integrated into an admin panel for dynamic layout management.

## Configuration Files

### `src/config/layout.ts`

This is the main configuration file that contains:

- **Layout interfaces** - TypeScript definitions for all configuration options
- **Preset configurations** - Pre-defined layout styles
- **Helper functions** - Utilities to generate Tailwind classes from configuration
- **Active configuration** - The currently active layout settings

## Available Layout Modes

### 1. Spaced Cards (Default)
```typescript
// Traditional card layout with gaps and shadows
gap: 4
borderRadius: 'lg'
shadow: 'lg'
hoverShadow: 'xl'
minHeight: 400px
```

**Features:**
- Gaps between cards
- Rounded corners
- Drop shadows
- Hover effects
- Traditional card appearance

### 2. Seamless Grid
```typescript
// Perfect grid with no gaps, like a tile layout
gap: 0
borderRadius: 'none'
shadow: 'none'
minHeight: 400px
```

**Features:**
- No gaps between cards
- No rounded corners
- No shadows
- Consistent card heights
- Perfect grid alignment

### 3. Compact Layout
```typescript
// Smaller cards for more content density
minHeight: 300px
padding: 3
maxHeight: 12rem
// Inherits gaps and shadows from default
```

### 4. Tall Layout
```typescript
// Taller cards for more content
minHeight: 500px
maxHeight: 20rem
// Inherits gaps and shadows from default
```

## Configuration Options

### Grid Settings
```typescript
grid: {
  gap: 0-8,                    // Tailwind gap classes
  columns: {
    mobile: 1,                 // grid-cols-1
    tablet: 2,                 // md:grid-cols-2
    desktop: 3,                // lg:grid-cols-3
    wide: 4,                   // xl:grid-cols-4
  }
}
```

### Card Settings
```typescript
card: {
  height: {
    mode: 'fixed' | 'auto' | 'min-height',
    minHeight: 400,            // pixels
    fixedHeight?: 500,         // pixels (when mode is 'fixed')
  },
  spacing: {
    padding: 4,                // p-4
    borderWidth: 2,            // border-2
    borderRadius: 'none',      // rounded-{value}
  },
  effects: {
    shadow: 'none',            // shadow-{value}
    hoverShadow: 'none',       // hover:shadow-{value}
    transitionDuration: 200,   // duration-{value}
  }
}
```

### Scrollable Content Settings
```typescript
scrollableContent: {
  maxHeight: 16,               // rem units
  scrollbar: {
    autoHide: true,
    width: 6,                  // pixels
  }
}
```

## How to Change Layouts

### Method 1: Update Active Configuration
```typescript
// In src/config/layout.ts
export const activeLayoutConfig = seamlessLayoutConfig; // Switch to seamless grid
// or
export const activeLayoutConfig = compactLayoutConfig; // Switch to compact layout
```

### Method 2: Create Custom Configuration
```typescript
// Create a new configuration
const customConfig: LayoutConfig = {
  grid: {
    gap: 2,
    columns: { mobile: 1, tablet: 2, desktop: 4, wide: 6 }
  },
  card: {
    height: { mode: 'min-height', minHeight: 350 },
    spacing: { padding: 3, borderWidth: 1, borderRadius: 'md' },
    effects: { shadow: 'md', hoverShadow: 'lg', transitionDuration: 300 }
  },
  scrollableContent: {
    maxHeight: 14,
    scrollbar: { autoHide: true, width: 8 }
  }
};

// Use it
export const activeLayoutConfig = customConfig;
```

### Method 3: Dynamic Configuration (Admin Panel)
```typescript
// In a React component
import { useState } from 'react';
import { LayoutConfig, getLayoutClasses } from '@/config/layout';

function AdminPanel() {
  const [config, setConfig] = useState<LayoutConfig>(defaultLayoutConfig);
  
  const handleConfigChange = (newConfig: LayoutConfig) => {
    setConfig(newConfig);
    // Save to database or localStorage
    localStorage.setItem('layoutConfig', JSON.stringify(newConfig));
  };
  
  return (
    <LayoutConfigPanel onConfigChange={handleConfigChange} />
  );
}
```

## Admin Panel Integration

The `LayoutConfigPanel` component provides a complete UI for configuring layouts:

```typescript
import { LayoutConfigPanel } from '@/components/admin/LayoutConfigPanel';

// Use in admin interface
<LayoutConfigPanel 
  onConfigChange={(config) => {
    // Save configuration
    saveLayoutConfig(config);
  }} 
/>
```

## Implementation Details

### How It Works

1. **Configuration Object** - All layout settings are stored in a TypeScript interface
2. **Class Generation** - The `getLayoutClasses()` function converts config to Tailwind classes
3. **Component Integration** - Components use the generated classes instead of hardcoded ones
4. **Dynamic Updates** - Changing the configuration automatically updates all components

### Key Benefits

- **Type Safety** - Full TypeScript support with interfaces
- **Consistency** - All components use the same configuration source
- **Flexibility** - Easy to add new configuration options
- **Performance** - Classes are generated once and reused
- **Maintainability** - Centralized configuration management

## Examples

### Traditional Card Layout (Current Implementation)
```typescript
// Standard card design with spacing and shadows
const spacedConfig = {
  grid: { gap: 4 },
  card: {
    height: { mode: 'min-height', minHeight: 400 },
    spacing: { borderRadius: 'lg' },
    effects: { shadow: 'lg', hoverShadow: 'xl' }
  }
};
```

### Seamless Grid Layout
```typescript
// Perfect grid with no gaps or shadows
const seamlessConfig = {
  grid: { gap: 0 },
  card: {
    spacing: { borderRadius: 'none' },
    effects: { shadow: 'none' }
  }
};
```

### Responsive Columns
```typescript
// Different column counts for different screen sizes
const responsiveConfig = {
  grid: {
    columns: {
      mobile: 1,    // 1 column on mobile
      tablet: 2,    // 2 columns on tablet
      desktop: 3,   // 3 columns on desktop
      wide: 5       // 5 columns on wide screens
    }
  }
};
```

## Future Enhancements

The configuration system is designed to be easily extensible:

- **Database Integration** - Save configurations to database
- **User Preferences** - Per-user layout preferences
- **A/B Testing** - Test different layouts
- **Theme Integration** - Coordinate with color themes
- **Animation Settings** - Configure transition effects
- **Accessibility Options** - High contrast, reduced motion, etc.

This system provides the foundation for a powerful, flexible layout management system that can grow with your application's needs.
