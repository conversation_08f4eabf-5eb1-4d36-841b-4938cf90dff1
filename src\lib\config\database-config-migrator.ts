/**
 * Database Configuration Migrator
 * 
 * Handles migration of configuration from environment variables to database storage
 * Provides utilities for managing configuration in the system_configuration table
 */

import { createClient } from '@supabase/supabase-js';
import { ConfigurationEncryption } from './encryption';

interface ConfigurationEntry {
  config_key: string;
  config_value: any;
  config_type: string;
  description: string;
  is_sensitive: boolean;
  is_active: boolean;
}

interface MigrationResult {
  success: boolean;
  migratedCount: number;
  skippedCount: number;
  errors: string[];
  entries: ConfigurationEntry[];
}

export class DatabaseConfigMigrator {
  private supabase: ReturnType<typeof createClient>;
  private encryption: ConfigurationEncryption;

  constructor(supabaseUrl: string, supabaseServiceKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseServiceKey);
    this.encryption = new ConfigurationEncryption();
  }

  /**
   * Migrate environment variables to database configuration
   */
  async migrateEnvironmentToDatabase(): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: false,
      migratedCount: 0,
      skippedCount: 0,
      errors: [],
      entries: []
    };

    try {
      // Define configuration entries that should be stored in database
      const configEntries: ConfigurationEntry[] = [
        // AI Provider Configuration
        {
          config_key: 'ai_provider_openai_enabled',
          config_value: process.env.OPENAI_API_KEY ? true : false,
          config_type: 'ai_provider',
          description: 'Enable OpenAI GPT-4o integration',
          is_sensitive: false,
          is_active: true
        },
        {
          config_key: 'ai_provider_openrouter_enabled',
          config_value: process.env.OPENROUTER_API_KEY ? true : false,
          config_type: 'ai_provider',
          description: 'Enable OpenRouter Gemini integration',
          is_sensitive: false,
          is_active: true
        },
        {
          config_key: 'ai_provider_openai_model_default',
          config_value: 'gpt-4o-2024-11-20',
          config_type: 'ai_provider',
          description: 'Default OpenAI model for content generation',
          is_sensitive: false,
          is_active: true
        },
        {
          config_key: 'ai_provider_openrouter_model_default',
          config_value: 'google/gemini-2.0-flash-exp:free',
          config_type: 'ai_provider',
          description: 'Default OpenRouter model for content generation',
          is_sensitive: false,
          is_active: true
        },

        // Scraping Configuration
        {
          config_key: 'scraping_default_timeout',
          config_value: parseInt(process.env.SCRAPING_TIMEOUT || '30000'),
          config_type: 'scraping',
          description: 'Default timeout for scraping operations (ms)',
          is_sensitive: false,
          is_active: true
        },
        {
          config_key: 'scraping_max_retries',
          config_value: parseInt(process.env.SCRAPING_MAX_RETRIES || '3'),
          config_type: 'scraping',
          description: 'Maximum retry attempts for failed scraping',
          is_sensitive: false,
          is_active: true
        },
        {
          config_key: 'scraping_cost_optimization_enabled',
          config_value: process.env.SCRAPING_COST_OPTIMIZATION !== 'false',
          config_type: 'scraping',
          description: 'Enable cost optimization for scraping operations',
          is_sensitive: false,
          is_active: true
        },

        // Job Processing Configuration
        {
          config_key: 'job_processing_max_concurrent',
          config_value: parseInt(process.env.JOB_CONCURRENCY || '3'),
          config_type: 'job_processing',
          description: 'Maximum concurrent job processing',
          is_sensitive: false,
          is_active: true
        },
        {
          config_key: 'job_processing_timeout',
          config_value: parseInt(process.env.JOB_TIMEOUT || '300000'),
          config_type: 'job_processing',
          description: 'Default job timeout (ms)',
          is_sensitive: false,
          is_active: true
        },
        {
          config_key: 'job_processing_retry_attempts',
          config_value: parseInt(process.env.JOB_RETRY_ATTEMPTS || '3'),
          config_type: 'job_processing',
          description: 'Default retry attempts for failed jobs',
          is_sensitive: false,
          is_active: true
        },

        // Content Generation Configuration
        {
          config_key: 'content_generation_enabled',
          config_value: process.env.CONTENT_GENERATION_ENABLED === 'true',
          config_type: 'content_generation',
          description: 'Enable AI content generation features',
          is_sensitive: false,
          is_active: true
        },
        {
          config_key: 'content_generation_max_tokens',
          config_value: parseInt(process.env.CONTENT_GENERATION_MAX_TOKENS || '4000'),
          config_type: 'content_generation',
          description: 'Maximum tokens for content generation',
          is_sensitive: false,
          is_active: true
        },

        // Editorial Workflow Configuration
        {
          config_key: 'editorial_auto_approve_threshold',
          config_value: parseFloat(process.env.EDITORIAL_AUTO_APPROVE_THRESHOLD || '0.9'),
          config_type: 'editorial',
          description: 'Quality threshold for auto-approval (0-1)',
          is_sensitive: false,
          is_active: true
        },
        {
          config_key: 'editorial_review_required',
          config_value: process.env.EDITORIAL_REVIEW_REQUIRED !== 'false',
          config_type: 'editorial',
          description: 'Require editorial review for all content',
          is_sensitive: false,
          is_active: true
        },

        // System Configuration
        {
          config_key: 'system_maintenance_mode',
          config_value: process.env.MAINTENANCE_MODE === 'true',
          config_type: 'system',
          description: 'Enable maintenance mode',
          is_sensitive: false,
          is_active: true
        },
        {
          config_key: 'system_debug_mode',
          config_value: process.env.DEBUG_MODE === 'true',
          config_type: 'system',
          description: 'Enable debug mode for detailed logging',
          is_sensitive: false,
          is_active: true
        }
      ];

      // Migrate each configuration entry
      for (const entry of configEntries) {
        try {
          // Check if entry already exists
          const { data: existing } = await this.supabase
            .from('system_configuration')
            .select('config_key')
            .eq('config_key', entry.config_key)
            .single();

          if (existing) {
            result.skippedCount++;
            continue;
          }

          // Encrypt sensitive values
          let configValue = entry.config_value;
          if (entry.is_sensitive) {
            configValue = this.encryption.encrypt(String(configValue));
          }

          // Insert new configuration entry
          const { error } = await this.supabase
            .from('system_configuration')
            .insert({
              config_key: entry.config_key,
              config_value: configValue,
              config_type: entry.config_type,
              description: entry.description,
              is_sensitive: entry.is_sensitive,
              is_active: entry.is_active,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (error) {
            result.errors.push(`Failed to migrate ${entry.config_key}: ${error.message}`);
          } else {
            result.migratedCount++;
            result.entries.push(entry);
          }

        } catch (error: any) {
          result.errors.push(`Error migrating ${entry.config_key}: ${error.message}`);
        }
      }

      result.success = result.errors.length === 0;

    } catch (error: any) {
      result.errors.push(`Migration failed: ${error.message}`);
    }

    return result;
  }

  /**
   * Get all configuration from database
   */
  async getDatabaseConfiguration(): Promise<Record<string, any>> {
    try {
      const { data, error } = await this.supabase
        .from('system_configuration')
        .select('config_key, config_value, config_type, is_sensitive')
        .eq('is_active', true);

      if (error) {
        throw error;
      }

      const config: Record<string, any> = {};

      for (const entry of data || []) {
        let value = entry.config_value;
        
        // Decrypt sensitive values
        if (entry.is_sensitive) {
          try {
            value = this.encryption.decrypt(value);
          } catch (decryptError) {
            console.warn(`Failed to decrypt ${entry.config_key}:`, decryptError);
            continue;
          }
        }

        config[entry.config_key] = value;
      }

      return config;

    } catch (error: any) {
      console.error('Failed to get database configuration:', error);
      return {};
    }
  }

  /**
   * Update a configuration value in the database
   */
  async updateConfiguration(key: string, value: any, description?: string): Promise<boolean> {
    try {
      // Check if the configuration is marked as sensitive
      const { data: existing } = await this.supabase
        .from('system_configuration')
        .select('is_sensitive')
        .eq('config_key', key)
        .single();

      let configValue = value;
      if (existing?.is_sensitive) {
        configValue = this.encryption.encrypt(String(value));
      }

      const updateData: any = {
        config_value: configValue,
        updated_at: new Date().toISOString()
      };

      if (description) {
        updateData.description = description;
      }

      const { error } = await this.supabase
        .from('system_configuration')
        .update(updateData)
        .eq('config_key', key);

      if (error) {
        console.error(`Failed to update configuration ${key}:`, error);
        return false;
      }

      return true;

    } catch (error: any) {
      console.error(`Error updating configuration ${key}:`, error);
      return false;
    }
  }

  /**
   * Validate database configuration table exists and is accessible
   */
  async validateConfigurationTable(): Promise<{ exists: boolean; accessible: boolean; error?: string }> {
    try {
      const { data, error } = await this.supabase
        .from('system_configuration')
        .select('config_key')
        .limit(1);

      if (error) {
        if (error.message.includes('does not exist')) {
          return { exists: false, accessible: false, error: 'Table does not exist' };
        }
        return { exists: true, accessible: false, error: error.message };
      }

      return { exists: true, accessible: true };

    } catch (error: any) {
      return { exists: false, accessible: false, error: error.message };
    }
  }

  /**
   * Create default configuration entries if table is empty
   */
  async initializeDefaultConfiguration(): Promise<MigrationResult> {
    const validation = await this.validateConfigurationTable();
    
    if (!validation.exists) {
      return {
        success: false,
        migratedCount: 0,
        skippedCount: 0,
        errors: ['Configuration table does not exist'],
        entries: []
      };
    }

    if (!validation.accessible) {
      return {
        success: false,
        migratedCount: 0,
        skippedCount: 0,
        errors: [`Configuration table not accessible: ${validation.error}`],
        entries: []
      };
    }

    // Check if configuration already exists
    const { data: existingConfig } = await this.supabase
      .from('system_configuration')
      .select('config_key')
      .limit(1);

    if (existingConfig && existingConfig.length > 0) {
      return {
        success: true,
        migratedCount: 0,
        skippedCount: existingConfig.length,
        errors: [],
        entries: []
      };
    }

    // Initialize with default configuration
    return await this.migrateEnvironmentToDatabase();
  }
}

// Export utility functions
export async function createDatabaseConfigMigrator(supabaseUrl?: string, supabaseServiceKey?: string): Promise<DatabaseConfigMigrator> {
  const url = supabaseUrl || process.env.NEXT_PUBLIC_SUPABASE_URL;
  const key = supabaseServiceKey || process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!url || !key) {
    throw new Error('Supabase URL and Service Role Key are required for database configuration migration');
  }

  return new DatabaseConfigMigrator(url, key);
}

export type { ConfigurationEntry, MigrationResult };
