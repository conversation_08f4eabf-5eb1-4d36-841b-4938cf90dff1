import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';

export async function validateApi<PERSON><PERSON>(request: NextRequest): Promise<boolean> {
  const apiKey = request.headers.get('x-api-key') ||
                 request.headers.get('x-admin-api-key') ||
                 request.headers.get('authorization')?.replace('Bearer ', '');

  if (!apiKey) {
    return false;
  }

  // Check against admin API key
  if (apiKey === process.env.ADMIN_API_KEY) {
    return true;
  }

  // Temporary development key for job monitoring dashboard
  if (apiKey === 'admin-dashboard-access') {
    return true;
  }

  // Validate JWT token for user authentication
  try {
    if (process.env.JWT_SECRET) {
      jwt.verify(apiKey, process.env.JWT_SECRET);
      return true;
    }
  } catch {
    return false;
  }

  return false;
}

export function generateApiKey(userId: string, expiresIn: string = '30d'): string {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET is not configured');
  }
  
  return jwt.sign({ userId }, process.env.JWT_SECRET!, { expiresIn } as jwt.SignOptions);
}

export function rateLimit(identifier: string, maxRequests: number = 100, windowMs: number = 60000) {
  // Implementation would use Redis or in-memory store
  // This is a simplified version for now
  const key = `rate_limit:${identifier}`;
  // Store and check request count
  return true; // Placeholder
}

/**
 * Validate admin access for client-side components
 * This function checks if the current user has admin privileges
 */
export async function validateAdminAccess(): Promise<boolean> {
  try {
    // Make a test request to verify admin access
    const response = await fetch('/api/health/jobs', {
      method: 'GET',
      headers: {
        'x-admin-api-key': 'admin-dashboard-access', // Temporary key for development
        'Content-Type': 'application/json',
      },
    });

    return response.ok;
  } catch (error) {
    console.error('Admin access validation failed:', error);
    return false;
  }
}

/**
 * Check admin authentication for API routes
 * Supports both x-admin-api-key and Authorization headers
 */
export function checkAdminAuth(request: NextRequest): boolean {
  const adminApiKey = request.headers.get('x-admin-api-key') ||
                     request.headers.get('authorization')?.replace('Bearer ', '');
  const expectedKey = process.env.ADMIN_API_KEY;

  return adminApiKey === expectedKey && expectedKey !== undefined;
}
