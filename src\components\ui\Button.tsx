'use client';

import React from 'react';
import { ArrowRight } from 'lucide-react';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'see-all';
  size?: 'sm' | 'md' | 'lg';
  colorClass?: string;
  textColorClass?: string;
  className?: string;
  showArrow?: boolean;
  disabled?: boolean;
}

export function Button({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  colorClass,
  textColorClass = 'text-white',
  className = '',
  showArrow = false,
  disabled = false,
}: ButtonProps) {
  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2',
    lg: 'px-6 py-3 text-lg',
  };

  const baseClasses = `${sizeClasses[size]} rounded-lg font-medium transition-all duration-200 flex items-center gap-2`;

  let variantClasses = '';

  switch (variant) {
    case 'primary':
      variantClasses = colorClass || 'bg-orange-500 hover:bg-orange-400';
      break;
    case 'secondary':
      variantClasses = 'bg-zinc-700 hover:bg-zinc-600 text-gray-300';
      break;
    case 'outline':
      variantClasses = 'border border-zinc-600 hover:border-zinc-500 bg-transparent hover:bg-zinc-800 text-gray-300';
      break;
    case 'see-all':
      variantClasses = colorClass || 'bg-sky-500 hover:bg-sky-400';
      break;
  }

  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${variantClasses} ${textColorClass} ${disabledClasses} ${className}`}
    >
      {children}
      {showArrow && <ArrowRight size={16} />}
    </button>
  );
}
