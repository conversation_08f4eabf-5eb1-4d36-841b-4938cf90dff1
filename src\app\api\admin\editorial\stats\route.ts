import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { validateApi<PERSON>ey } from '@/lib/auth';

/**
 * GET /api/admin/editorial/stats
 * Get editorial workflow statistics and metrics
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get current date for today's stats
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate()).toISOString();
    const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1).toISOString();

    // Parallel queries for different statistics
    const [
      totalSubmissionsResult,
      pendingReview<PERSON><PERSON><PERSON>,
      underR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
      approved<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
      rejected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
      recentReviewsResult
    ] = await Promise.all([
      // Total submissions count
      supabase
        .from('tool_submissions')
        .select('id', { count: 'exact', head: true }),

      // Pending review count
      supabase
        .from('tool_submissions')
        .select('id', { count: 'exact', head: true })
        .eq('status', 'pending'),

      // Under review count
      supabase
        .from('tool_submissions')
        .select('id', { count: 'exact', head: true })
        .eq('status', 'under_review'),

      // Approved today count
      supabase
        .from('tool_submissions')
        .select('id', { count: 'exact', head: true })
        .eq('status', 'approved')
        .gte('reviewed_at', todayStart)
        .lt('reviewed_at', todayEnd),

      // Rejected today count
      supabase
        .from('tool_submissions')
        .select('id', { count: 'exact', head: true })
        .eq('status', 'rejected')
        .gte('reviewed_at', todayStart)
        .lt('reviewed_at', todayEnd),

      // Recent reviews for average time calculation
      supabase
        .from('tool_submissions')
        .select('submitted_at, reviewed_at')
        .not('reviewed_at', 'is', null)
        .order('reviewed_at', { ascending: false })
        .limit(100)
    ]);

    // Check for errors
    const errors = [
      totalSubmissionsResult.error,
      pendingReviewResult.error,
      underReviewResult.error,
      approvedTodayResult.error,
      rejectedTodayResult.error,
      recentReviewsResult.error
    ].filter(Boolean);

    if (errors.length > 0) {
      console.error('Failed to fetch editorial stats:', errors);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch statistics' },
        { status: 500 }
      );
    }

    // Calculate average review time
    let averageReviewTime = 0;
    if (recentReviewsResult.data && recentReviewsResult.data.length > 0) {
      const reviewTimes = recentReviewsResult.data
        .filter(review => review.submitted_at && review.reviewed_at)
        .map(review => {
          const submitted = new Date(review.submitted_at!);
          const reviewed = new Date(review.reviewed_at!);
          return (reviewed.getTime() - submitted.getTime()) / (1000 * 60 * 60); // Convert to hours
        });

      if (reviewTimes.length > 0) {
        averageReviewTime = reviewTimes.reduce((sum, time) => sum + time, 0) / reviewTimes.length;
      }
    }

    // Get additional editorial review stats
    const [
      editorialReviewsResult,
      featuredToolsResult
    ] = await Promise.all([
      // Editorial reviews count
      supabase
        .from('editorial_reviews')
        .select('id', { count: 'exact', head: true }),

      // Featured tools count
      supabase
        .from('editorial_reviews')
        .select('id', { count: 'exact', head: true })
        .not('featured_date', 'is', null)
    ]);

    // Compile statistics
    const stats = {
      totalSubmissions: totalSubmissionsResult.count || 0,
      pendingReview: pendingReviewResult.count || 0,
      underReview: underReviewResult.count || 0,
      approvedToday: approvedTodayResult.count || 0,
      rejectedToday: rejectedTodayResult.count || 0,
      averageReviewTime: Math.round(averageReviewTime * 10) / 10, // Round to 1 decimal place
      totalEditorialReviews: editorialReviewsResult.count || 0,
      featuredTools: featuredToolsResult.count || 0
    };

    // Get workflow health metrics
    const workflowHealth = {
      queueHealth: stats.pendingReview < 50 ? 'healthy' : stats.pendingReview < 100 ? 'warning' : 'critical',
      reviewEfficiency: averageReviewTime < 24 ? 'excellent' : averageReviewTime < 72 ? 'good' : 'needs_improvement',
      approvalRate: stats.totalSubmissions > 0 ? 
        Math.round(((stats.totalSubmissions - stats.pendingReview - stats.underReview) / stats.totalSubmissions) * 100) : 0
    };

    // Get category breakdown
    const categoryStatsResult = await supabase
      .from('tool_submissions')
      .select('category')
      .not('category', 'is', null);

    let categoryBreakdown: Record<string, number> = {};
    if (categoryStatsResult.data) {
      categoryBreakdown = categoryStatsResult.data.reduce((acc, item) => {
        const category = item.category;
        acc[category] = (acc[category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
    }

    // Get recent activity timeline
    const recentActivityResult = await supabase
      .from('tool_submissions')
      .select('id, name, status, reviewed_at, submitted_at')
      .order('submitted_at', { ascending: false })
      .limit(10);

    const recentActivity = recentActivityResult.data?.map(item => ({
      id: item.id,
      name: item.name,
      status: item.status,
      timestamp: item.reviewed_at || item.submitted_at,
      action: item.reviewed_at ? 'reviewed' : 'submitted'
    })) || [];

    return NextResponse.json({
      success: true,
      stats,
      workflowHealth,
      categoryBreakdown,
      recentActivity,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Editorial stats API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/editorial/stats
 * Refresh or recalculate editorial statistics
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // This endpoint can be used to trigger manual recalculation of stats
    // For now, it just returns the current stats (same as GET)
    // In the future, this could trigger background jobs to update cached statistics

    const body = await request.json();
    const { action } = body;

    if (action === 'recalculate') {
      // Trigger recalculation logic here
      console.log('Manual stats recalculation triggered');
    }

    // Return current stats
    const getResponse = await GET(request);
    return getResponse;

  } catch (error) {
    console.error('Editorial stats refresh API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
