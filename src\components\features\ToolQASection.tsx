'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronUp, HelpCircle } from 'lucide-react';
import { AITool } from '@/lib/types';

interface ToolQASectionProps {
  tool: AITool;
}

// Sample Q&A data for AI tools
const generateSampleQA = (tool: AITool) => [
  {
    id: 'what-is',
    question: `What is ${tool.name}?`,
    answer: tool.detailedDescription || tool.description || `${tool.name} is an AI-powered tool designed to help users with various tasks. It offers innovative features and capabilities that leverage artificial intelligence to improve productivity and efficiency.`
  },
  {
    id: 'how-to-use',
    question: `How do I get started with ${tool.name}?`,
    answer: `Getting started with ${tool.name} is simple. Visit their website, create an account, and follow the onboarding process. Most AI tools offer tutorials or guided tours to help new users understand the features and capabilities.`
  },
  {
    id: 'pricing',
    question: `Is ${tool.name} free to use?`,
    answer: tool.pricing?.type === 'free' 
      ? `Yes, ${tool.name} offers a free plan that includes basic features.`
      : tool.pricing?.type === 'freemium'
      ? `${tool.name} offers both free and paid plans. The free plan includes basic features, while premium plans unlock advanced capabilities.`
      : `${tool.name} is a paid service. Check their pricing page for current plans and features.`
  },
  {
    id: 'features',
    question: `What are the main features of ${tool.name}?`,
    answer: tool.features?.length 
      ? `${tool.name} offers several key features including: ${tool.features.slice(0, 3).join(', ')}${tool.features.length > 3 ? ', and more' : ''}.`
      : `${tool.name} provides AI-powered capabilities designed to enhance productivity and streamline workflows. Visit their website for a complete list of features.`
  },
  {
    id: 'support',
    question: `Does ${tool.name} offer customer support?`,
    answer: `Most AI tools, including ${tool.name}, provide customer support through various channels such as email, chat, or help documentation. Check their website for specific support options and contact information.`
  }
];

export function ToolQASection({ tool }: ToolQASectionProps) {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const qaItems = generateSampleQA(tool);

  const toggleItem = (id: string) => {
    setExpandedItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <section className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
          <HelpCircle size={16} className="text-white" />
        </div>
        <h3 className="text-xl font-bold text-white">Frequently Asked Questions</h3>
      </div>

      <div className="space-y-4">
        {qaItems.map((item) => {
          const isExpanded = expandedItems.includes(item.id);
          
          return (
            <div key={item.id} className="border border-zinc-600 rounded-lg overflow-hidden">
              <button
                onClick={() => toggleItem(item.id)}
                className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-zinc-700 transition-colors duration-200"
              >
                <h4 className="text-white font-medium pr-4">
                  {item.question}
                </h4>
                {isExpanded ? (
                  <ChevronUp size={20} className="text-gray-400 flex-shrink-0" />
                ) : (
                  <ChevronDown size={20} className="text-gray-400 flex-shrink-0" />
                )}
              </button>
              
              {isExpanded && (
                <div className="px-4 pb-3 border-t border-zinc-600 bg-zinc-750">
                  <div className="pt-3">
                    <p className="text-gray-300 leading-relaxed">
                      {item.answer}
                    </p>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      <div className="mt-6 pt-4 border-t border-zinc-600">
        <p className="text-gray-400 text-sm">
          Have more questions about {tool.name}? Visit their official website or contact their support team for detailed information.
        </p>
      </div>
    </section>
  );
}
