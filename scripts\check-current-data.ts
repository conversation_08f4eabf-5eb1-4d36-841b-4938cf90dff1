#!/usr/bin/env tsx

/**
 * Check Current Data Status
 *
 * This script analyzes the current state of data in the database
 * to understand what needs to be migrated for Task 4.1.
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing required environment variables');
  console.error('   NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl);
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', !!supabaseKey);
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

interface DataAnalysis {
  totalTools: number;
  toolsWithScrapedData: number;
  toolsWithAIGeneration: number;
  pendingAIGeneration: number;
  completedAIGeneration: number;
  failedAIGeneration: number;
  totalJobs: number;
  totalMediaAssets: number;
  totalEditorialReviews: number;
  totalBulkJobs: number;
  systemConfigEntries: number;
}

async function analyzeCurrentData(): Promise<DataAnalysis> {
  console.log('🔍 Analyzing current database state...\n');

  // Check tools table
  const { data: tools, error: toolsError } = await supabase
    .from('tools')
    .select('id, name, scraped_data, ai_generation_status, submission_type, content_status');

  if (toolsError) {
    console.error('❌ Error fetching tools:', toolsError);
    throw toolsError;
  }

  // Check AI generation jobs
  const { data: jobs, error: jobsError } = await supabase
    .from('ai_generation_jobs')
    .select('id, status, job_type');

  if (jobsError) {
    console.error('❌ Error fetching AI jobs:', jobsError);
    throw jobsError;
  }

  // Check media assets
  const { data: media, error: mediaError } = await supabase
    .from('media_assets')
    .select('id, asset_type');

  if (mediaError) {
    console.error('❌ Error fetching media assets:', mediaError);
    throw mediaError;
  }

  // Check editorial reviews
  const { data: reviews, error: reviewsError } = await supabase
    .from('editorial_reviews')
    .select('id, review_status');

  if (reviewsError) {
    console.error('❌ Error fetching editorial reviews:', reviewsError);
    throw reviewsError;
  }

  // Check bulk processing jobs
  const { data: bulkJobs, error: bulkError } = await supabase
    .from('bulk_processing_jobs')
    .select('id, status');

  if (bulkError) {
    console.error('❌ Error fetching bulk jobs:', bulkError);
    throw bulkError;
  }

  // Check system configuration
  const { data: config, error: configError } = await supabase
    .from('system_configuration')
    .select('id, config_key, config_type');

  if (configError) {
    console.error('❌ Error fetching system config:', configError);
    throw configError;
  }

  // Analyze the data
  const analysis: DataAnalysis = {
    totalTools: tools?.length || 0,
    toolsWithScrapedData: tools?.filter(t => t.scraped_data).length || 0,
    toolsWithAIGeneration: tools?.filter(t => t.ai_generation_status !== 'pending').length || 0,
    pendingAIGeneration: tools?.filter(t => t.ai_generation_status === 'pending').length || 0,
    completedAIGeneration: tools?.filter(t => t.ai_generation_status === 'completed').length || 0,
    failedAIGeneration: tools?.filter(t => t.ai_generation_status === 'failed').length || 0,
    totalJobs: jobs?.length || 0,
    totalMediaAssets: media?.length || 0,
    totalEditorialReviews: reviews?.length || 0,
    totalBulkJobs: bulkJobs?.length || 0,
    systemConfigEntries: config?.length || 0
  };

  return analysis;
}

async function displayDataAnalysis(analysis: DataAnalysis): Promise<void> {
  console.log('📊 DATABASE DATA ANALYSIS');
  console.log('=' .repeat(60));
  
  console.log('\n🔧 TOOLS TABLE:');
  console.log(`   Total Tools: ${analysis.totalTools}`);
  console.log(`   Tools with Scraped Data: ${analysis.toolsWithScrapedData}`);
  console.log(`   Tools with AI Generation: ${analysis.toolsWithAIGeneration}`);
  console.log(`   Pending AI Generation: ${analysis.pendingAIGeneration}`);
  console.log(`   Completed AI Generation: ${analysis.completedAIGeneration}`);
  console.log(`   Failed AI Generation: ${analysis.failedAIGeneration}`);

  console.log('\n🤖 AI GENERATION JOBS:');
  console.log(`   Total Jobs: ${analysis.totalJobs}`);

  console.log('\n🖼️  MEDIA ASSETS:');
  console.log(`   Total Media Assets: ${analysis.totalMediaAssets}`);

  console.log('\n📝 EDITORIAL REVIEWS:');
  console.log(`   Total Editorial Reviews: ${analysis.totalEditorialReviews}`);

  console.log('\n📦 BULK PROCESSING JOBS:');
  console.log(`   Total Bulk Jobs: ${analysis.totalBulkJobs}`);

  console.log('\n⚙️  SYSTEM CONFIGURATION:');
  console.log(`   Configuration Entries: ${analysis.systemConfigEntries}`);

  console.log('\n' + '=' .repeat(60));
  
  // Migration recommendations
  console.log('\n🎯 MIGRATION RECOMMENDATIONS:');
  
  if (analysis.totalTools > 0) {
    console.log(`✅ Found ${analysis.totalTools} existing tools - ready for data migration`);
  } else {
    console.log('⚠️  No existing tools found - fresh installation');
  }

  if (analysis.pendingAIGeneration > 0) {
    console.log(`🔄 ${analysis.pendingAIGeneration} tools pending AI generation - migration should preserve status`);
  }

  if (analysis.totalJobs > 0) {
    console.log(`📋 ${analysis.totalJobs} existing jobs - job history migration needed`);
  }

  if (analysis.systemConfigEntries > 0) {
    console.log(`⚙️  ${analysis.systemConfigEntries} config entries - configuration migration needed`);
  }

  console.log('\n🚀 MIGRATION STATUS: Database schema is ready, data migration can proceed');
}

async function main(): Promise<void> {
  try {
    const analysis = await analyzeCurrentData();
    await displayDataAnalysis(analysis);
    
    console.log('\n✅ Data analysis completed successfully');
    
  } catch (error) {
    console.error('❌ Data analysis failed:', error);
    process.exit(1);
  }
}

// Run the analysis
main();
