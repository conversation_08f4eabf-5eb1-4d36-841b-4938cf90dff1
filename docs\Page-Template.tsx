/**
 * Page Template for AI Dude Directory
 * 
 * This template provides a starting point for creating new pages
 * with consistent styling and functionality.
 * 
 * Copy this file to src/app/[page-name]/page.tsx and customize as needed.
 */

'use client';

import React from 'react';
import { useSearchContext } from '@/providers/SearchProvider';
import { useTooltip } from '@/hooks/useTooltip';
import { Tooltip } from '@/components/features/Tooltip';

// Example: If you need specific types, import them
// import { AITool, AICategory } from '@/lib/types';

export default function NewPage() {
  // Access global search state if needed
  const {
    searchTerm,
    searchResults,
    isLoadingSearchResults,
    // Add other search properties as needed
  } = useSearchContext();

  // Tooltip functionality if needed
  const { activeTooltip, triggerType, showTooltip, hideTooltip } = useTooltip();

  // Local state for page-specific functionality
  // const [localState, setLocalState] = useState(initialValue);

  // Event handlers
  const handleExampleAction = () => {
    // Handle page-specific actions
    console.log('Example action triggered');
  };

  return (
    <div className="w-full">
      {/* Page Header Section */}
      <div className="mx-auto px-4 py-8" style={{ maxWidth: 'var(--container-width)' }}>
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">
            Page Title
          </h1>
          <p className="text-zinc-400 text-lg max-w-2xl mx-auto">
            Page description or subtitle goes here. Keep it concise and informative.
          </p>
        </div>

        {/* Search Results Section (if applicable) */}
        {searchTerm && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-white mb-4">
              Search Results for "{searchTerm}"
            </h2>
            {isLoadingSearchResults ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-white text-lg">Searching...</div>
              </div>
            ) : searchResults && searchResults.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {searchResults.map((result) => (
                  <div
                    key={result.id}
                    className="bg-zinc-800 border border-zinc-700 p-4 rounded-lg hover:bg-zinc-700 transition-colors duration-200 cursor-pointer shadow-lg"
                    onClick={() => window.open(result.link, '_blank')}
                  >
                    <h3 className="text-white font-medium mb-2">{result.name}</h3>
                    <p className="text-zinc-400 text-sm">{result.description}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-zinc-400 text-center py-12">
                No results found for "{searchTerm}"
              </div>
            )}
          </div>
        )}

        {/* Main Content Section */}
        <div className="space-y-12">
          {/* Content Block 1 */}
          <section>
            <h2 className="text-2xl font-bold text-white mb-6">Section Title</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Example cards */}
              <div className="bg-zinc-800 border border-zinc-700 p-6 rounded-lg hover:bg-zinc-700 transition-colors duration-200 shadow-lg">
                <h3 className="text-white font-medium mb-3">Card Title</h3>
                <p className="text-zinc-400 text-sm mb-4">
                  Card description goes here. Keep it informative and concise.
                </p>
                <button
                  onClick={handleExampleAction}
                  className="bg-zinc-700 hover:bg-orange-500 text-white px-4 py-2 rounded transition-colors duration-200"
                  style={{
                    transition: 'background-color 0.2s ease-in-out'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)'}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = ''}
                >
                  Action Button
                </button>
              </div>

              {/* Add more cards as needed */}
            </div>
          </section>

          {/* Content Block 2 */}
          <section>
            <h2 className="text-2xl font-bold text-white mb-6">Another Section</h2>
            <div className="bg-zinc-800 border border-zinc-700 p-8 rounded-lg">
              <p className="text-zinc-300 leading-relaxed">
                This is an example of a content block. You can add any type of content here,
                such as text, images, forms, or other components. The styling follows the
                established design system for consistency.
              </p>
            </div>
          </section>

          {/* List Section Example */}
          <section>
            <h2 className="text-2xl font-bold text-white mb-6">List Example</h2>
            <div className="space-y-4">
              {[1, 2, 3].map((item) => (
                <div
                  key={item}
                  className="bg-zinc-800 border border-zinc-700 p-4 rounded-lg hover:bg-zinc-700 transition-colors duration-200 flex items-center justify-between"
                >
                  <div>
                    <h3 className="text-white font-medium">List Item {item}</h3>
                    <p className="text-zinc-400 text-sm">Description for item {item}</p>
                  </div>
                  <button
                    className="text-zinc-400 hover:text-white transition-colors duration-200"
                    onMouseEnter={(e) => e.currentTarget.style.color = 'rgb(255, 150, 0)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = ''}
                  >
                    →
                  </button>
                </div>
              ))}
            </div>
          </section>
        </div>

        {/* Call to Action Section */}
        <div className="text-center mt-16 py-12 bg-zinc-800 rounded-lg border border-zinc-700">
          <h2 className="text-2xl font-bold text-white mb-4">Ready to Get Started?</h2>
          <p className="text-zinc-400 mb-6 max-w-md mx-auto">
            Add a compelling call to action that encourages user engagement.
          </p>
          <button
            className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200"
            style={{
              transition: 'background-color 0.2s ease-in-out'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = ''}
            onClick={handleExampleAction}
          >
            Get Started
          </button>
        </div>
      </div>

      {/* Global Tooltip (include if using tooltips) */}
      {activeTooltip && <Tooltip tooltip={activeTooltip} triggerType={triggerType} />}
    </div>
  );
}

/**
 * Usage Instructions:
 * 
 * 1. Copy this file to src/app/[page-name]/page.tsx
 * 2. Replace "NewPage" with your actual page name
 * 3. Update the page title and description
 * 4. Customize the content sections as needed
 * 5. Add any page-specific imports and functionality
 * 6. Remove unused sections or add new ones
 * 7. Test responsive behavior across all breakpoints
 * 
 * Key Features Included:
 * - Consistent container width and spacing
 * - Dark theme styling
 * - Search integration (optional)
 * - Tooltip support (optional)
 * - Responsive grid layouts
 * - Hover effects with custom orange color
 * - Loading and empty states
 * - Call to action section
 * 
 * Remember to:
 * - Follow the established design system
 * - Use TypeScript for type safety
 * - Include accessibility features
 * - Test across different screen sizes
 * - Maintain consistent spacing and colors
 */
