'use client';

import React, { useState } from 'react';
import {
  Mail,
  ShoppingBag,
  Twitter,
  X,
  Search,
  Settings,
  ChevronDown,
  PlusCircle,
  Newspaper,
  MessageSquare,
  Moon,
  Sun
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

interface HeaderProps {
  searchTerm: string;
  isSearchDropdownVisible: boolean;
  onSearchChange: (value: string) => void;
  onSearchFocus: () => void;
  onSearchBlur: () => void;
  onTopSearchClick: (term: string) => void;
}

export function Header({
  searchTerm,
  isSearchDropdownVisible,
  onSearchChange,
  onSearchFocus,
  onSearchBlur,
  onTopSearchClick,
}: HeaderProps) {
  const [showNotificationBanner, setShowNotificationBanner] = useState(true);
  const [isDarkMode, setIsDarkMode] = useState(true);

  // Helper components
  const TopBar = () => (
    <div className={`text-sm lg:text-base dark-theme-top-bar`} style={{
      height: 'var(--top-bar-height)',
      borderBottomWidth: 'var(--top-bar-border-width)',
      borderBottomStyle: 'solid'
    }}>
      <div className="mx-auto flex justify-between items-center px-4" style={{
        maxWidth: 'var(--container-width)',
        height: 'var(--top-bar-height)'
      }}>
        <div className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
          <h1 className="font-normal text-xs sm:text-sm">
            AI Dude reviews the best AI tools of 2025.{' '}
            <span className="hidden sm:inline">Find safe free <b className="font-medium">AI tools</b> & premium AI websites all sorted by quality!</span>
          </h1>
        </div>
        <div className="hidden lg:flex items-center flex-shrink-0">
          <button className="flex items-center hover:text-white">
            <Image src="https://picsum.photos/16/12?random=flag" alt="Language" width={16} height={12} className="mr-1"/>
            <ChevronDown size={16} />
          </button>
        </div>
      </div>
    </div>
  );

  const AIDudeLogoDesktop = () => (
    <Link href="/" className="block">
      <div
        className="relative font-extrabold tracking-tighter flex items-center"
        style={{
          fontSize: '2.8rem',
          lineHeight: '1',
          height: '90px'
        }}
      >
        <span
          className="block text-transparent bg-clip-text"
          style={{
            background: 'linear-gradient(to bottom, #fde049, #ff9701, #7f3e13)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            WebkitTextStroke: '3px #404040',
          }}
        >
          AIDUDE
        </span>
        <span
          className="absolute top-0 left-0 w-full h-full flex items-center text-transparent bg-clip-text"
          style={{
            background: 'linear-gradient(to bottom, white, #e5e5e5, #a3a3a3)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            WebkitTextStroke: '1px #171717',
            transform: 'translate(0.5px, 0.5px)',
          }}
        >
          AIDUDE
        </span>
         <span
          className="absolute top-0 left-0 w-full h-full flex items-center text-transparent bg-clip-text"
          style={{
            background: 'linear-gradient(to bottom, #fde049, #ff9701, #ea580c)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          AIDUDE
        </span>
      </div>
    </Link>
  );

  const AIDudeLogoMobile = () => (
    <Link href="/" className="flex items-center">
      <span
        className="text-2xl font-bold text-transparent bg-clip-text"
        style={{
          background: 'linear-gradient(to right, #fde049, #ff9701)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
        }}
      >
        AIDUDE
      </span>
    </Link>
  );

  return (
    <header className="relative">
      {/* Mobile Header - Fixed */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-30">
        <div className="flex items-center justify-between px-4 shadow-md bg-zinc-900" style={{
          height: 'var(--header-mob-height)'
        }}>
          <button className="text-white p-2">
            <Search size={24} />
          </button>
          <AIDudeLogoMobile />
          <button className="text-white p-2">
            <Settings size={24} />
          </button>
        </div>
      </div>

      {/* Spacer for fixed mobile header */}
      <div className="lg:hidden" style={{ height: 'var(--header-mob-height)' }}></div>

      {/* Desktop Header */}
      <div className="hidden lg:block">
        <TopBar />
        <div className="relative bg-zinc-900" style={{
          height: 'var(--header-desk-height)'
        }}>
          <div className="mx-auto px-4" style={{ maxWidth: 'var(--container-width)' }}>
            <div className="flex justify-between items-start py-4">
              {/* Left Column - Logo and Search */}
              <div className="flex flex-col space-y-3 flex-shrink-0" style={{ width: '460px', height: '146px' }}>
                <AIDudeLogoDesktop />

                {/* Search Bar */}
                <div className="relative w-full">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => onSearchChange(e.target.value)}
                  onFocus={onSearchFocus}
                  onBlur={onSearchBlur}
                  placeholder="Search best 1000+ AI tools..."
                  className="w-full h-10 pl-4 pr-10 text-white placeholder-white border-2 rounded-lg focus:ring-2 transition-all"
                  style={{
                    backgroundColor: '#27272a',
                    borderColor: '#000000'
                  }}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <Search className="text-orange-500" size={20} />
                </div>

                {/* Search Dropdown */}
                {isSearchDropdownVisible && (
                  <div
                    className="absolute top-full left-0 right-0 mt-2 rounded-lg shadow-xl border-2 z-50"
                    style={{
                      backgroundColor: '#333333',
                      borderColor: '#6b6b6b'
                    }}
                  >
                    <div className="p-4">
                      <h3 className="text-sm font-semibold mb-2" style={{ color: '#ff9701' }}>
                        Popular AI Tools
                      </h3>
                      <div className="space-y-2">
                        {['ChatGPT', 'Midjourney', 'Claude', 'DALL-E', 'Stable Diffusion'].map((tool) => (
                          <button
                            key={tool}
                            onClick={() => onTopSearchClick(tool)}
                            className="block w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 rounded transition-colors"
                          >
                            {tool}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

              {/* Middle Column - Callout and Social Icons */}
              <div className="flex flex-col items-center flex-shrink-0 mx-8 space-y-4" style={{ width: '410px', height: '146px' }}>
                {/* AI Callout Box */}
                <div className="w-full relative">
                  <div
                    className="p-px rounded-lg shadow-lg border-2 relative"
                    style={{
                      backgroundColor: '#3a3a3a',
                      borderColor: '#ff9701',
                      height: '90px',
                      width: '410px'
                    }}
                  >
                    <div className="p-3 rounded-lg h-full" style={{ backgroundColor: '#2a2a2a' }}>
                      <div className="flex items-start space-x-2 h-full">
                        <div className="flex-1">
                          <h3 className="text-xs font-semibold mb-1" style={{ color: '#ff9701' }}>
                            Stay up to date with the best AI tools!
                          </h3>
                          <p className="text-xs text-neutral-300 leading-tight">
                            Sign up for our mailing list to be notified of new tools, exclusive offers and the most popular categories.
                          </p>
                        </div>
                        <div className="flex flex-col items-center space-y-1 flex-shrink-0">
                          <div
                            className="w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold"
                            style={{ backgroundColor: '#ff9701', color: '#1a1a1a' }}
                          >
                            1
                          </div>
                          <div
                            className="w-0 h-0 border-l-3 border-r-3 border-t-4"
                            style={{
                              borderLeftColor: 'transparent',
                              borderRightColor: 'transparent',
                              borderTopColor: '#ff9701'
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>

                    {/* Right-pointing arrow towards mascot */}
                    <div
                      className="absolute top-1/2 -translate-y-1/2"
                      style={{
                        right: '-12px',
                        width: '0',
                        height: '0',
                        borderTop: '12px solid transparent',
                        borderBottom: '12px solid transparent',
                        borderLeft: '12px solid #ff9701'
                      }}
                    ></div>
                  </div>
                </div>

                {/* Social Icons - Horizontal List */}
                <ul className="flex space-x-2">
                  <li className="relative group">
                    <Link
                      href="/contact"
                      className="w-10 h-10 flex items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out transform group-hover:rotate-6 group-hover:scale-110"
                      style={{
                        backgroundColor: '#27272a',
                        borderColor: '#000000',
                        transition: 'all 0.2s ease-in-out'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#27272a'}
                      aria-label="Contact AI Dude"
                    >
                      <Mail size={18} className="text-white transition-colors duration-200" />
                    </Link>
                    <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-2 py-1 bg-zinc-800 border border-black text-white text-xs rounded-md shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10 pointer-events-none">
                      I am here for you, write me
                    </span>
                  </li>

                  <li className="relative group">
                    <Link
                      href="/submit"
                      className="w-10 h-10 flex items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out transform group-hover:rotate-6 group-hover:scale-110"
                      style={{
                        backgroundColor: '#ff9701',
                        borderColor: '#000000',
                        transition: 'all 0.2s ease-in-out'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(235, 130, 0)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#ff9701'}
                      aria-label="Submit your AI Tool"
                    >
                      <PlusCircle size={18} className="text-white transition-colors duration-200" />
                    </Link>
                    <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-2 py-1 bg-zinc-800 border border-black text-white text-xs rounded-md shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10 pointer-events-none">
                      Submit your AI Tool
                    </span>
                  </li>

                  <li className="relative group">
                    <Link
                      href="https://x.com/aidude_com"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-10 h-10 flex items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out transform group-hover:rotate-6 group-hover:scale-110"
                      style={{
                        backgroundColor: '#27272a',
                        borderColor: '#000000',
                        transition: 'all 0.2s ease-in-out'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#27272a'}
                      aria-label="Follow my Twitter"
                    >
                      <Twitter size={18} className="text-white transition-colors duration-200" />
                    </Link>
                    <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-2 py-1 bg-zinc-800 border border-black text-white text-xs rounded-md shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10 pointer-events-none">
                      Follow my Twitter
                    </span>
                  </li>

                  <li className="relative group">
                    <Link
                      href="/blog"
                      className="w-10 h-10 flex flex-col items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out transform group-hover:rotate-6 group-hover:scale-110"
                      style={{
                        backgroundColor: '#27272a',
                        borderColor: '#000000',
                        transition: 'all 0.2s ease-in-out'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#27272a'}
                      aria-label="AI Dude's blog isn't boring"
                    >
                      <Newspaper size={14} className="text-white transition-colors duration-200" />
                      <span className="text-[9px] font-medium leading-tight mt-px text-white transition-colors duration-200">BLOG</span>
                    </Link>
                    <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-2 py-1 bg-zinc-800 border border-black text-white text-xs rounded-md shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10 pointer-events-none">
                      AI Dude's blog isn't boring
                    </span>
                  </li>

                  <li className="relative group">
                    <Link
                      href="https://aidude.shop"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-10 h-10 flex flex-col items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out transform group-hover:rotate-6 group-hover:scale-110"
                      style={{
                        backgroundColor: '#27272a',
                        borderColor: '#000000',
                        transition: 'all 0.2s ease-in-out'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#27272a'}
                      aria-label="AI Dude's shop"
                    >
                      <MessageSquare size={14} className="text-white transition-colors duration-200" />
                      <span className="text-[9px] font-medium leading-tight mt-px text-white transition-colors duration-200">SHOP</span>
                    </Link>
                    <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-2 py-1 bg-zinc-800 border border-black text-white text-xs rounded-md shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10 pointer-events-none">
                      AI Dude's shop
                    </span>
                  </li>

                  <li className="relative group">
                    <button
                      onClick={() => setIsDarkMode(!isDarkMode)}
                      className="w-10 h-10 flex items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out transform group-hover:rotate-6 group-hover:scale-110"
                      style={{
                        backgroundColor: '#27272a',
                        borderColor: '#000000',
                        transition: 'all 0.2s ease-in-out'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#27272a'}
                      aria-label="Theme Mode"
                    >
                      <Sun size={18} className="text-white transition-colors duration-200" />
                    </button>
                    <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-2 py-1 bg-zinc-800 border border-black text-white text-xs rounded-md shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10 pointer-events-none">
                      Light Mode
                    </span>
                  </li>
                </ul>
              </div>

              {/* Right Column - Mascot */}
              <div className="flex-shrink-0 relative" style={{ width: '260px' }}>
                <Link href="/" aria-label="AI Dude">
                  <div className="relative" style={{ width: '260px', height: '146px' }}>
                    <Image
                      src="/mascot.png"
                      alt="AI Dude Mascot"
                      width={260}
                      height={227}
                      className="object-contain absolute optimized-image"
                      quality={100}
                      priority
                      unoptimized={false}
                      style={{
                        transform: 'translateY(-40px)',
                        zIndex: 1,
                        maxWidth: '260px',
                        filter: 'contrast(1.05) saturate(1.05) brightness(1.02)',
                        WebkitBackfaceVisibility: 'hidden',
                        backfaceVisibility: 'hidden'
                      }}
                    />
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
