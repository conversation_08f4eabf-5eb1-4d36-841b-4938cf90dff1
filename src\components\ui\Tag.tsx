'use client';

import React, { memo } from 'react';
import { Star, Flame } from 'lucide-react';

/**
 * Tag Component - Badge/label for categorizing AI tools
 *
 * Features:
 * - Pixel-perfect color schemes matching design system
 * - Icon support for PREMIUM (star) and HOT (flame) tags
 * - Consistent typography and spacing
 * - Performance optimized with React.memo
 * - Accessibility support with proper contrast ratios
 */

interface TagProps {
  type: 'Trending' | 'New' | 'Premium' | 'AI' | 'HOT' | 'NEW' | 'PREMIUM';
  label?: string;
  className?: string;
}

export const Tag = memo<TagProps>(function Tag({ type, label, className = '' }) {
  const getTagStyles = () => {
    switch (type) {
      case 'Trending':
        return 'bg-red-500 text-white border-red-400';
      case 'New':
      case 'NEW':
        return 'bg-green-500 text-white border-green-400';
      case 'Premium':
      case 'PREMIUM':
        return 'bg-yellow-500 text-black border-yellow-400';
      case 'AI':
        return 'bg-blue-500 text-white border-blue-400';
      case 'HOT':
        return 'bg-orange-500 text-white border-orange-400';
      default:
        return 'bg-gray-600 text-white border-gray-500';
    }
  };

  const getAriaLabel = () => {
    switch (type) {
      case 'Trending':
        return 'Trending tool';
      case 'New':
      case 'NEW':
        return 'New tool';
      case 'Premium':
      case 'PREMIUM':
        return 'Premium tool';
      case 'AI':
        return 'AI-powered tool';
      case 'HOT':
        return 'Hot tool';
      default:
        return `${type} tool`;
    }
  };

  const renderContent = () => {
    if (type === 'Premium' || type === 'PREMIUM') {
      return (
        <>
          <Star size={10} className="text-yellow-400 flex-shrink-0" aria-hidden="true" />
          {label && <span className="sr-only">{label}</span>}
        </>
      );
    }
    if (type === 'Trending' || type === 'HOT') {
      return (
        <>
          <Flame size={10} className="flex-shrink-0" aria-hidden="true" />
          {label && <span className="sr-only">{label}</span>}
        </>
      );
    }
    return label || type;
  };

  return (
    <span
      className={`
        inline-flex items-center gap-0.5
        text-xs px-1.5 py-0.5
        rounded-sm font-medium
        border border-opacity-20
        transition-all duration-200
        ${getTagStyles()}
        ${className}
      `}
      role="img"
      aria-label={getAriaLabel()}
      title={getAriaLabel()}
    >
      {renderContent()}
    </span>
  );
});
