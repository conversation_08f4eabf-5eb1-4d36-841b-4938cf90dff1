'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface PerformanceData {
  performance: {
    timeRange: string;
    totalRequests: number;
    stats: {
      averageResponseTime: number;
      p95ResponseTime: number;
      p99ResponseTime: number;
      throughput: number;
      errorRate: number;
      memoryUsage: number;
    };
    topSlowEndpoints: Array<{
      endpoint: string;
      averageTime: number;
      requestCount: number;
    }>;
    trends: {
      responseTimeTrend: 'improving' | 'degrading' | 'stable';
      throughputTrend: 'increasing' | 'decreasing' | 'stable';
      errorRateTrend: 'improving' | 'degrading' | 'stable';
    };
    recommendations: string[];
  };
  cost: {
    timeRange: string;
    totalCost: number;
    costByService: Record<string, number>;
    costByOperation: Record<string, number>;
    projectedMonthlyCost: number;
    budgetUtilization?: number;
    costTrend: 'increasing' | 'decreasing' | 'stable';
  };
  costOptimization: {
    totalSavings: number;
    savingsPercentage: number;
    optimizationStrategies: Array<{
      strategy: string;
      savingsAmount: number;
      usageCount: number;
    }>;
    recommendations: string[];
  };
  database: {
    health: {
      slowQueries: number;
      indexEfficiency: number;
      tableStats: Array<{
        table: string;
        rowCount: number;
        size: string;
        indexCount: number;
      }>;
      recommendations: string[];
    };
    queryReport: {
      totalQueries: number;
      averageQueryTime: number;
      slowQueries: number;
      fastestQuery: { query: string; duration: number } | null;
      slowestQuery: { query: string; duration: number } | null;
    };
  };
}

interface PerformanceDashboardProps {
  className?: string;
}

export function PerformanceDashboard({ className = '' }: PerformanceDashboardProps) {
  const [data, setData] = useState<PerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('1h');
  const [isMonitoring, setIsMonitoring] = useState(false);

  useEffect(() => {
    loadPerformanceData();
  }, [timeRange]);

  const loadPerformanceData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/admin/performance?timeRange=${timeRange}`, {
        headers: {
          'x-admin-api-key': 'admin-dashboard-access'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load performance data');
      }

      const result = await response.json();
      setData(result.data);
    } catch (err) {
      console.error('Failed to load performance data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleStartMonitoring = async () => {
    try {
      const response = await fetch('/api/admin/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': 'admin-dashboard-access'
        },
        body: JSON.stringify({
          action: 'start_monitoring',
          data: { interval: 60000 }
        })
      });

      if (response.ok) {
        setIsMonitoring(true);
      }
    } catch (error) {
      console.error('Failed to start monitoring:', error);
    }
  };

  const handleStopMonitoring = async () => {
    try {
      const response = await fetch('/api/admin/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': 'admin-dashboard-access'
        },
        body: JSON.stringify({
          action: 'stop_monitoring'
        })
      });

      if (response.ok) {
        setIsMonitoring(false);
      }
    } catch (error) {
      console.error('Failed to stop monitoring:', error);
    }
  };

  const handleOptimizeDatabase = async () => {
    try {
      const response = await fetch('/api/admin/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': 'admin-dashboard-access'
        },
        body: JSON.stringify({
          action: 'optimize_database'
        })
      });

      if (response.ok) {
        await loadPerformanceData(); // Refresh data
      }
    } catch (error) {
      console.error('Failed to optimize database:', error);
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'improving':
      case 'increasing':
        return 'text-green-400';
      case 'degrading':
      case 'decreasing':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
      case 'increasing':
        return '↗️';
      case 'degrading':
      case 'decreasing':
        return '↘️';
      default:
        return '→';
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-300">Loading performance data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-red-900/20 border border-red-500 rounded-lg p-6 ${className}`}>
        <h3 className="text-red-400 font-semibold mb-2">Error Loading Performance Data</h3>
        <p className="text-red-300 mb-4">{error}</p>
        <Button onClick={loadPerformanceData} variant="outline">
          Retry
        </Button>
      </div>
    );
  }

  if (!data) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <p className="text-gray-400">No performance data available</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Performance Dashboard</h2>
          <p className="text-gray-300">System performance monitoring and optimization</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
          
          <Button
            onClick={isMonitoring ? handleStopMonitoring : handleStartMonitoring}
            variant={isMonitoring ? "outline" : "primary"}
          >
            {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
          </Button>
          
          <Button onClick={loadPerformanceData} variant="outline">
            Refresh
          </Button>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Avg Response Time</p>
              <p className="text-2xl font-bold text-white">
                {data.performance.stats.averageResponseTime.toFixed(0)}ms
              </p>
            </div>
            <span className={getTrendColor(data.performance.trends.responseTimeTrend)}>
              {getTrendIcon(data.performance.trends.responseTimeTrend)}
            </span>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Error Rate</p>
              <p className="text-2xl font-bold text-white">
                {data.performance.stats.errorRate.toFixed(1)}%
              </p>
            </div>
            <span className={getTrendColor(data.performance.trends.errorRateTrend)}>
              {getTrendIcon(data.performance.trends.errorRateTrend)}
            </span>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Memory Usage</p>
              <p className="text-2xl font-bold text-white">
                {data.performance.stats.memoryUsage.toFixed(0)}MB
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Total Requests</p>
              <p className="text-2xl font-bold text-white">
                {data.performance.totalRequests.toLocaleString()}
              </p>
            </div>
            <span className={getTrendColor(data.performance.trends.throughputTrend)}>
              {getTrendIcon(data.performance.trends.throughputTrend)}
            </span>
          </div>
        </Card>
      </div>

      {/* Cost Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <div>
            <p className="text-sm text-gray-400">Total Cost ({data.cost.timeRange})</p>
            <p className="text-2xl font-bold text-white">
              ${data.cost.totalCost.toFixed(2)}
            </p>
            <p className={`text-sm ${getTrendColor(data.cost.costTrend)}`}>
              {getTrendIcon(data.cost.costTrend)} {data.cost.costTrend}
            </p>
          </div>
        </Card>

        <Card className="p-4">
          <div>
            <p className="text-sm text-gray-400">Projected Monthly</p>
            <p className="text-2xl font-bold text-white">
              ${data.cost.projectedMonthlyCost.toFixed(2)}
            </p>
            {data.cost.budgetUtilization && (
              <p className="text-sm text-gray-400">
                {data.cost.budgetUtilization.toFixed(1)}% of budget
              </p>
            )}
          </div>
        </Card>

        <Card className="p-4">
          <div>
            <p className="text-sm text-gray-400">Potential Savings</p>
            <p className="text-2xl font-bold text-green-400">
              ${data.costOptimization.totalSavings.toFixed(2)}
            </p>
            <p className="text-sm text-gray-400">
              {data.costOptimization.savingsPercentage.toFixed(1)}% optimization
            </p>
          </div>
        </Card>
      </div>

      {/* Database Health */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold text-white">Database Performance</h3>
          <Button onClick={handleOptimizeDatabase} variant="outline">
            Optimize Database
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <p className="text-sm text-gray-400">Index Efficiency</p>
            <p className="text-2xl font-bold text-white">
              {data.database.health.indexEfficiency.toFixed(1)}%
            </p>
          </div>
          
          <div>
            <p className="text-sm text-gray-400">Slow Queries</p>
            <p className="text-2xl font-bold text-white">
              {data.database.health.slowQueries}
            </p>
          </div>
          
          <div>
            <p className="text-sm text-gray-400">Avg Query Time</p>
            <p className="text-2xl font-bold text-white">
              {data.database.queryReport.averageQueryTime.toFixed(0)}ms
            </p>
          </div>
        </div>

        {data.database.health.recommendations.length > 0 && (
          <div className="bg-zinc-800 rounded-lg p-4">
            <h4 className="text-lg font-semibold text-white mb-2">Database Recommendations</h4>
            <ul className="space-y-1">
              {data.database.health.recommendations.map((rec, index) => (
                <li key={index} className="text-gray-300 text-sm">• {rec}</li>
              ))}
            </ul>
          </div>
        )}
      </Card>

      {/* Slow Endpoints */}
      {data.performance.topSlowEndpoints.length > 0 && (
        <Card className="p-6">
          <h3 className="text-xl font-bold text-white mb-4">Slowest Endpoints</h3>
          <div className="space-y-3">
            {data.performance.topSlowEndpoints.map((endpoint, index) => (
              <div key={index} className="flex justify-between items-center bg-zinc-800 rounded-lg p-3">
                <div>
                  <p className="text-white font-medium">{endpoint.endpoint}</p>
                  <p className="text-gray-400 text-sm">{endpoint.requestCount} requests</p>
                </div>
                <div className="text-right">
                  <p className="text-orange-400 font-bold">{endpoint.averageTime.toFixed(0)}ms</p>
                  <p className="text-gray-400 text-sm">avg response</p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Recommendations */}
      {data.performance.recommendations.length > 0 && (
        <Card className="p-6">
          <h3 className="text-xl font-bold text-white mb-4">Performance Recommendations</h3>
          <ul className="space-y-2">
            {data.performance.recommendations.map((rec, index) => (
              <li key={index} className="text-gray-300 flex items-start">
                <span className="text-orange-400 mr-2">•</span>
                {rec}
              </li>
            ))}
          </ul>
        </Card>
      )}
    </div>
  );
}
