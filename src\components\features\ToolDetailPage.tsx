'use client';

import React, { useState } from 'react';
import { AITool } from '@/lib/types';
import { ToolHeroSection } from './ToolHeroSection';
import { ToolFeaturesList } from './ToolFeaturesList';
import { ToolScreenshots } from './ToolScreenshots';
import { ToolPricing } from './ToolPricing';
import { ToolReviews } from './ToolReviews';
import { RelatedTools } from './RelatedTools';
import { ToolProsAndCons } from './ToolProsAndCons';
import { ToolReleases } from './ToolReleases';
import { ToolClaimSection } from './ToolClaimSection';
import { FeaturedTools } from './FeaturedTools';
import { SectionNavigation } from './SectionNavigation';
import { EditorialVetting } from './EditorialVetting';
import { ToolQASection } from './ToolQASection';
import { ToolTagsCard } from './ToolTagsCard';

interface ToolDetailPageProps {
  tool: AITool;
}

export function ToolDetailPage({ tool }: ToolDetailPageProps) {
  const [activeSection, setActiveSection] = useState('overview');

  return (
    <div className="w-full -mt-4">
      {/* Main Content Container */}
      <div className="mx-auto px-4 pb-6" style={{ width: 'var(--container-width)' }}>

        {/* Single Layout Grid - Hero, Navigation, and All Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">

          {/* LEFT SIDE - Hero, Navigation, and Main Content (3/4 width) */}
          <div className="lg:col-span-3 space-y-4">

            {/* Hero Section */}
            <ToolHeroSection tool={tool} />

            {/* Section Navigation */}
            <SectionNavigation
              activeSection={activeSection}
              onSectionClick={setActiveSection}
            />

            {/* Main Content Sections */}

            {/* Overview Section */}
            <div id="overview">
              {/* Features Section */}
              {tool.features && tool.features.length > 0 && (
                <div>
                  <ToolFeaturesList features={tool.features} />
                </div>
              )}

              {/* Screenshots Section */}
              {tool.screenshots && tool.screenshots.length > 0 && (
                <div className="mb-4">
                  <ToolScreenshots screenshots={tool.screenshots} toolName={tool.name} />
                </div>
              )}

              {/* Detailed Description */}
              {tool.detailedDescription && (
                <section className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-200 mb-4">
                  <h2 className="text-2xl font-bold text-white mb-4">About {tool.name}</h2>
                  <div className="prose prose-invert max-w-none">
                    <p className="text-gray-300 leading-relaxed">
                      {tool.detailedDescription}
                    </p>
                  </div>
                </section>
              )}

              {/* Editorial Vetting Section */}
              <EditorialVetting toolName={tool.name} />
            </div>

            {/* Pros and Cons Section */}
            {tool.prosAndCons && (
              <div id="pros-cons">
                <ToolProsAndCons prosAndCons={tool.prosAndCons} />
              </div>
            )}

            {/* Releases Section */}
            {tool.releases && tool.releases.length > 0 && (
              <div id="releases">
                <ToolReleases releases={tool.releases} toolName={tool.name} />
              </div>
            )}

            {/* Pricing Section */}
            {tool.pricing && (
              <div id="pricing">
                <ToolPricing pricing={tool.pricing} />
              </div>
            )}

            {/* Reviews Section */}
            {tool.reviews && (
              <div id="reviews">
                <ToolReviews reviews={tool.reviews} />
              </div>
            )}

            {/* Q&A Section */}
            <div id="qa">
              <ToolQASection tool={tool} />
            </div>

            {/* Tags Section */}
            <ToolTagsCard tool={tool} />

            {/* Claim Tool Section */}
            <ToolClaimSection tool={tool} />

          </div>

          {/* RIGHT SIDEBAR - Featured Tools (1/4 width) - Starts from top */}
          <div className="lg:col-span-1">
            <div className="sticky top-0">
              <FeaturedTools currentTool={tool} />
            </div>
          </div>

        </div>

        {/* Related Tools Section - Full Width */}
        <div className="mt-6">
          <div id="alternatives">
            <RelatedTools currentTool={tool} />
          </div>
        </div>

      </div>
    </div>
  );
}
