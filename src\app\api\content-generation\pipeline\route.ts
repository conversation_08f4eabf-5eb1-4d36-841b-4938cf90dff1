/**
 * Content Generation Pipeline API
 * 
 * Main API endpoint for triggering and managing the content generation pipeline
 * with editorial controls and workflow management.
 */

import { NextRequest, NextResponse } from 'next/server';
import { ContentGenerationPipeline } from '../../../../lib/content-generation/pipeline';
import { ContentGenerationRequest } from '../../../../lib/types';

const pipeline = new ContentGenerationPipeline();

/**
 * POST /api/content-generation/pipeline
 * Execute content generation pipeline for a tool
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const { toolId, scrapedContent, toolUrl } = body;
    if (!toolId || !scrapedContent || !toolUrl) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: toolId, scrapedContent, toolUrl' 
        },
        { status: 400 }
      );
    }

    // Build content generation request
    const generationRequest: ContentGenerationRequest = {
      toolId,
      scrapedContent,
      toolUrl,
      complexity: body.complexity || 'medium',
      priority: body.priority || 'quality',
      contentQuality: body.contentQuality || 70,
      scrapingCost: body.scrapingCost || 0
    };

    // Pipeline options
    const options = {
      skipValidation: body.skipValidation || false,
      requireEditorialReview: body.requireEditorialReview || false,
      autoApprove: body.autoApprove || false,
      qualityThreshold: body.qualityThreshold || 80,
      priority: body.priority || 'normal',
      customPrompts: body.customPrompts
    };

    console.log(`Starting content generation pipeline for tool: ${toolId}`);
    console.log(`Options:`, options);

    // Execute pipeline
    const result = await pipeline.execute(generationRequest, options);

    if (result.success) {
      console.log(`Pipeline completed successfully for tool: ${toolId}`);
      console.log(`Status: ${result.status}, Workflow: ${result.workflowState}`);
      console.log(`Quality Score: ${result.qualityScore?.overall || 'N/A'}`);
    } else {
      console.error(`Pipeline failed for tool: ${toolId}`, result.error);
    }

    return NextResponse.json({
      success: result.success,
      data: {
        toolId: result.toolId,
        status: result.status,
        workflowState: result.workflowState,
        generatedContent: result.generatedContent,
        validation: result.validation,
        qualityScore: result.qualityScore,
        editorialReview: result.editorialReview,
        metadata: result.metadata
      },
      error: result.error
    });

  } catch (error: any) {
    console.error('Content generation pipeline API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/content-generation/pipeline?toolId=xxx
 * Get pipeline status for a tool
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const toolId = searchParams.get('toolId');

    if (!toolId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required parameter: toolId' 
        },
        { status: 400 }
      );
    }

    const status = await pipeline.getStatus(toolId);

    if (!status) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Tool not found or no pipeline data available' 
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: status
    });

  } catch (error: any) {
    console.error('Pipeline status API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/content-generation/pipeline
 * Update pipeline configuration or retry failed pipeline
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { toolId, action } = body;

    if (!toolId || !action) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: toolId, action' 
        },
        { status: 400 }
      );
    }

    switch (action) {
      case 'retry':
        // Retry failed pipeline
        const retryRequest: ContentGenerationRequest = {
          toolId,
          scrapedContent: body.scrapedContent,
          toolUrl: body.toolUrl,
          complexity: body.complexity || 'medium',
          priority: body.priority || 'quality',
          contentQuality: body.contentQuality || 70,
          scrapingCost: body.scrapingCost || 0
        };

        const retryResult = await pipeline.execute(retryRequest, body.options || {});
        
        return NextResponse.json({
          success: retryResult.success,
          data: retryResult,
          message: 'Pipeline retry completed'
        });

      default:
        return NextResponse.json(
          { 
            success: false, 
            error: `Unknown action: ${action}` 
          },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Pipeline update API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
