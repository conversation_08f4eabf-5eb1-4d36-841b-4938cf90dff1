# Web Scraping Data Structure

## Overview

The web scraping job handler captures comprehensive data from target websites using Puppeteer. This document details the complete data structure and how to access it.

## Complete Data Structure

### Job Result Format
```typescript
interface ScrapingJobResult {
  success: boolean;
  data: ScrapedData;
  screenshot: string; // Base64 encoded PNG
  scrapedAt: string; // ISO timestamp
}
```

### Scraped Data Structure
```typescript
interface ScrapedData {
  // Basic Page Information
  title: string;           // Page title from <title> tag
  url: string;            // Final URL after redirects
  text: string;           // Main text content (up to 5000 chars)
  
  // Meta Information
  meta: Record<string, string>; // All meta tags (name/property -> content)
  
  // Page Structure
  headings: Array<{
    level: string;        // 'h1', 'h2', 'h3', etc.
    text: string;         // Heading text content
  }>;
  
  // Media and Links
  images: Array<{
    src: string;          // Image source URL
    alt?: string;         // Alt text if available
  }>;
  
  links: Array<{
    href: string;         // Link URL
    text: string;         // Link text content
  }>;
  
  // Site Assets
  favicon: string | null; // Favicon URL
  
  // Business Information
  pricing: Array<{
    text: string;         // Pricing-related text
    tag: string;          // HTML tag name
  }>;
  
  faq: Array<{
    text: string;         // FAQ-related text
    tag: string;          // HTML tag name
  }>;
}
```

## Data Extraction Details

### 1. Basic Page Information
- **Title**: Extracted from `<title>` tag
- **URL**: Final URL after any redirects
- **Text**: Main content from `<main>` or `<body>`, limited to 5000 characters

### 2. Meta Tags
Captures all meta tags including:
- `description` - Page description
- `keywords` - SEO keywords
- `og:title` - Open Graph title
- `og:description` - Open Graph description
- `og:image` - Open Graph image
- `twitter:card` - Twitter card type
- And any other meta tags with `name` or `property` attributes

### 3. Headings Structure
- Extracts all heading tags (H1-H6)
- Preserves hierarchy and text content
- Useful for understanding page structure

### 4. Images and Media
- All images with `src` attributes
- Alt text when available
- Relative URLs are preserved as-is

### 5. Links
- All anchor tags with `href` attributes
- Link text content
- Both internal and external links

### 6. Business Intelligence
- **Pricing**: Text containing keywords like 'price', 'pricing', 'cost', 'plan', 'subscription'
- **FAQ**: Text containing keywords like 'faq', 'frequently asked', 'questions', 'help'

### 7. Screenshot
- Full-page PNG screenshot (clipped to 1200x800)
- Base64 encoded data URL format
- Useful for visual verification

## Access Methods

### Method 1: Direct Job Queue Access
```typescript
import { getJobQueue } from '@/lib/jobs';

const queue = getJobQueue();
const job = await queue.getJob('job_id_here');

if (job?.result?.data) {
  const scrapedData = job.result.data;
  console.log('Title:', scrapedData.title);
  console.log('Images found:', scrapedData.images.length);
  // Access any field from the structure above
}
```

### Method 2: API Endpoint
```bash
# Get specific job details
curl http://localhost:3000/api/automation/jobs/JOB_ID \
  -H "x-api-key: your-admin-api-key"

# List all completed scraping jobs
curl "http://localhost:3000/api/automation/jobs?status=completed" \
  -H "x-api-key: your-admin-api-key"
```

### Method 3: Inspection Scripts
```bash
# View scraped data from all completed jobs
npm run inspect:data

# Or use PowerShell API inspector
powershell -ExecutionPolicy Bypass -File scripts/api-data-inspector.ps1
```

## Common Use Cases

### 1. Content Analysis
```typescript
// Analyze page structure
const headings = scrapedData.headings;
const h1Count = headings.filter(h => h.level === 'h1').length;
const hasProperStructure = h1Count === 1;
```

### 2. SEO Analysis
```typescript
// Check SEO elements
const hasMetaDescription = 'description' in scrapedData.meta;
const titleLength = scrapedData.title.length;
const isOptimized = hasMetaDescription && titleLength >= 30 && titleLength <= 60;
```

### 3. Business Intelligence
```typescript
// Detect pricing information
const hasPricing = scrapedData.pricing.length > 0;
const pricingText = scrapedData.pricing.map(p => p.text).join(' ');
const isFree = pricingText.toLowerCase().includes('free');
```

### 4. Media Inventory
```typescript
// Count media assets
const imageCount = scrapedData.images.length;
const hasLogo = scrapedData.images.some(img => 
  img.alt?.toLowerCase().includes('logo') || 
  img.src.toLowerCase().includes('logo')
);
```

## Debugging and Troubleshooting

### 1. Check Job Status
```typescript
const job = await queue.getJob(jobId);
console.log('Status:', job.status);
console.log('Error:', job.error);
console.log('Attempts:', job.attempts);
```

### 2. Validate Scraped Data
```typescript
if (!job.result?.data) {
  console.log('No scraped data - check for errors');
} else {
  const data = job.result.data;
  console.log('Data completeness:');
  console.log('- Has title:', !!data.title);
  console.log('- Has text:', !!data.text);
  console.log('- Meta tags:', Object.keys(data.meta).length);
  console.log('- Images:', data.images.length);
  console.log('- Links:', data.links.length);
}
```

### 3. Screenshot Analysis
```typescript
// Save screenshot for visual inspection
if (job.result?.screenshot) {
  const base64Data = job.result.screenshot.replace(/^data:image\/png;base64,/, '');
  const buffer = Buffer.from(base64Data, 'base64');
  require('fs').writeFileSync('debug-screenshot.png', buffer);
}
```

## Performance Considerations

### Data Size Limits
- Text content: Limited to 5000 characters
- Images/Links: No hard limit, but large pages may have many
- Screenshot: Typically 50-200KB when base64 encoded

### Processing Time
- Simple pages: 2-5 seconds
- Complex pages: 5-15 seconds
- Pages with heavy JavaScript: 10-30 seconds

### Memory Usage
- Each job result: ~100KB - 1MB depending on content
- Screenshots: ~50-200KB each
- Consider cleanup for long-running applications

## Error Handling

Common issues and solutions:

### 1. Timeout Errors
```typescript
// Increase timeout in job data
{
  url: 'https://example.com',
  options: {
    timeout: 30000 // 30 seconds instead of default 15
  }
}
```

### 2. Missing Content
- Some sites require JavaScript execution time
- Use `waitForSelector` option for dynamic content
- Check if site blocks automated browsers

### 3. Large Pages
- Content may be truncated at limits
- Consider multiple scraping passes for different sections
- Use specific selectors for targeted extraction
