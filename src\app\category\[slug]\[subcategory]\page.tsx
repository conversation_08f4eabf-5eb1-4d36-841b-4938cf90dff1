import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { CategoryToolsPage } from '@/components/features/CategoryToolsPage';
import {
  findCategoryBySlug,
  getToolsForCategory,
  getSubcategoriesForCategory
} from '@/lib/categoryUtils';
import { getCategories } from '@/lib/supabase';

interface SubcategoryPageProps {
  params: {
    slug: string;
    subcategory: string;
  };
}

// Generate metadata for SEO
export async function generateMetadata({ params }: SubcategoryPageProps): Promise<Metadata> {
  const { slug, subcategory: subcategorySlug } = await params;
  const category = await findCategoryBySlug(slug);

  if (!category) {
    return {
      title: 'Category Not Found - AI Dude Directory',
      description: 'The requested AI tools category could not be found.',
    };
  }

  // Convert slug back to display name
  const subcategoryName = subcategorySlug
    .replace(/-/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());

  // Get tools for this subcategory
  const tools = await getToolsForCategory(category.id, subcategoryName);

  if (tools.length === 0) {
    return {
      title: 'Subcategory Not Found - AI Dude Directory',
      description: 'The requested AI tools subcategory could not be found.',
    };
  }

  const toolCount = tools.length;
  const title = `${subcategoryName} ${category.title} - ${toolCount} AI Tools | AI Dude Directory`;
  const description = `Discover ${toolCount} ${subcategoryName.toLowerCase()} tools in ${category.title.toLowerCase()}. Find the best AI tools for ${subcategoryName.toLowerCase()}.`;

  return {
    title,
    description,
    keywords: `${subcategoryName}, ${category.title}, AI tools, artificial intelligence, ${category.id}`,
    openGraph: {
      title,
      description,
      type: 'website',
      url: `/category/${slug}/${subcategorySlug}`,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    },
    alternates: {
      canonical: `/category/${slug}/${subcategorySlug}`,
    },
  };
}

// Generate static params for all category/subcategory combinations
export async function generateStaticParams() {
  // Disable static generation for now to avoid build issues
  return [];
}

export default async function SubcategoryPage({ params }: SubcategoryPageProps) {
  const { slug, subcategory: subcategorySlug } = await params;
  const category = await findCategoryBySlug(slug);

  if (!category) {
    notFound();
  }

  // Convert slug back to display name
  const subcategoryName = subcategorySlug
    .replace(/-/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());

  // Get tools for this subcategory
  const tools = await getToolsForCategory(category.id, subcategoryName);

  if (tools.length === 0) {
    notFound();
  }

  return (
    <CategoryToolsPage
      category={category}
      subcategory={subcategoryName}
      initialTools={tools}
    />
  );
}
