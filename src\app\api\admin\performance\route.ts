import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { performanceMonitor } from '@/lib/monitoring/performance-monitor';
import { costTracker } from '@/lib/monitoring/cost-tracker';
import { databaseOptimizer } from '@/lib/monitoring/database-optimizer';

/**
 * GET /api/admin/performance
 * Get performance metrics and monitoring data
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '1h';
    const metricType = searchParams.get('metricType') as any;

    // Get performance summary
    const performanceSummary = await performanceMonitor.getPerformanceSummary(timeRange);
    
    // Get cost summary
    const costSummary = await costTracker.getCostSummary(timeRange);
    
    // Get cost optimization metrics
    const costOptimization = await costTracker.getCostOptimizationMetrics(timeRange);
    
    // Get database health
    const databaseHealth = await databaseOptimizer.analyzeDatabase();
    
    // Get query performance report
    const queryReport = databaseOptimizer.getQueryPerformanceReport();

    // Get specific metrics if requested
    let metrics: any[] = [];
    if (metricType) {
      metrics = await performanceMonitor.getMetrics(timeRange, metricType);
    }

    return NextResponse.json({
      success: true,
      data: {
        performance: performanceSummary,
        cost: costSummary,
        costOptimization,
        database: {
          health: databaseHealth,
          queryReport
        },
        metrics: metricType ? metrics : undefined
      }
    });

  } catch (error: any) {
    console.error('Performance monitoring API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/performance
 * Record a performance metric or trigger optimization
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case 'record_metric':
        await performanceMonitor.recordMetric(data);
        return NextResponse.json({
          success: true,
          message: 'Metric recorded successfully'
        });

      case 'record_cost':
        await costTracker.recordCost(data);
        return NextResponse.json({
          success: true,
          message: 'Cost recorded successfully'
        });

      case 'start_monitoring':
        await performanceMonitor.startMonitoring(data?.interval);
        await costTracker.startTracking();
        return NextResponse.json({
          success: true,
          message: 'Monitoring started'
        });

      case 'stop_monitoring':
        performanceMonitor.stopMonitoring();
        costTracker.stopTracking();
        return NextResponse.json({
          success: true,
          message: 'Monitoring stopped'
        });

      case 'optimize_database':
        const recommendations = await databaseOptimizer.getIndexRecommendations();
        const optimizationResult = await databaseOptimizer.applyOptimizations(recommendations);
        return NextResponse.json({
          success: true,
          data: optimizationResult,
          message: 'Database optimization completed'
        });

      case 'clear_query_log':
        databaseOptimizer.clearQueryLog();
        return NextResponse.json({
          success: true,
          message: 'Query log cleared'
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Performance monitoring POST error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/performance
 * Update performance monitoring configuration
 */
export async function PUT(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { budgetLimits, alertThresholds } = body;

    if (budgetLimits) {
      costTracker.setBudgetLimits(budgetLimits);
    }

    // Note: Alert thresholds would be implemented in a configuration system
    // For now, we'll just acknowledge the update

    return NextResponse.json({
      success: true,
      message: 'Performance monitoring configuration updated',
      data: {
        budgetLimits: budgetLimits ? costTracker.getBudgetLimits() : undefined
      }
    });

  } catch (error: any) {
    console.error('Performance monitoring PUT error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
