'use client';

import React from 'react';
import { Calendar, Tag as TagIcon, Clock, Download } from 'lucide-react';
import { AITool } from '@/lib/types';

interface ToolReleasesProps {
  releases: NonNullable<AITool['releases']>;
  toolName: string;
}

export function ToolReleases({ releases, toolName }: ToolReleasesProps) {
  // Sort releases by date (newest first)
  const sortedReleases = [...releases].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getTimeSince = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 30) return `${diffDays} days ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  };

  return (
    <section className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
      <div className="flex items-center gap-2 mb-6">
        <Download size={20} className="text-blue-400" />
        <h2 className="text-2xl font-bold text-white">Releases & Updates</h2>
      </div>
      
      <div className="space-y-4">
        {sortedReleases.map((release, index) => (
          <div
            key={index}
            className={`border rounded-lg p-4 transition-colors duration-200 hover:bg-zinc-700/50 ${
              release.isLatest 
                ? 'border-blue-500 bg-blue-500/10' 
                : 'border-zinc-600 bg-zinc-700/30'
            }`}
          >
            
            {/* Release Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <TagIcon size={16} className="text-gray-400" />
                  <span className="font-semibold text-white">
                    {release.version}
                  </span>
                  {release.isLatest && (
                    <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                      Latest
                    </span>
                  )}
                </div>
              </div>
              
              <div className="text-right">
                <div className="flex items-center gap-1 text-gray-400 text-sm">
                  <Calendar size={14} />
                  <span>{formatDate(release.date)}</span>
                </div>
                <div className="flex items-center gap-1 text-gray-500 text-xs mt-1">
                  <Clock size={12} />
                  <span>{getTimeSince(release.date)}</span>
                </div>
              </div>
            </div>
            
            {/* Release Notes */}
            <div className="text-gray-300 text-sm leading-relaxed">
              {release.notes}
            </div>
            
          </div>
        ))}
      </div>
      
      {/* Release Stats */}
      <div className="mt-6 pt-6 border-t border-zinc-600">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="p-3">
            <div className="text-lg font-bold text-blue-400 mb-1">
              {releases.length}
            </div>
            <div className="text-gray-400 text-xs">Total Releases</div>
          </div>
          <div className="p-3">
            <div className="text-lg font-bold text-green-400 mb-1">
              {sortedReleases[0] ? formatDate(sortedReleases[0].date) : 'N/A'}
            </div>
            <div className="text-gray-400 text-xs">Latest Update</div>
          </div>
          <div className="p-3">
            <div className="text-lg font-bold text-purple-400 mb-1">
              {sortedReleases[0] ? getTimeSince(sortedReleases[0].date) : 'N/A'}
            </div>
            <div className="text-gray-400 text-xs">Last Updated</div>
          </div>
        </div>
      </div>
    </section>
  );
}
