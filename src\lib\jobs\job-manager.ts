import { 
  Job, 
  JobType, 
  JobStatus, 
  JobPriority, 
  JobOptions,
  JobControlOptions,
  JobHistoryFilter,
  QueueStats,
  JobEventListener
} from './types';
import { getEnhancedJobQueue } from './enhanced-queue';
import { getProgressTracker } from './progress-tracker';
import { getWebSocketManager } from './websocket-manager';

/**
 * Job Manager - High-level interface for job lifecycle management
 * 
 * Provides a unified API for job operations with enhanced features:
 * - Job creation and management
 * - Progress tracking and monitoring
 * - Real-time updates via WebSocket
 * - Job control operations (pause/resume/stop)
 * - Event handling and notifications
 */
export class JobManager {
  private queue;
  private progressTracker;
  private webSocketManager;
  private eventListeners = new Map<string, JobEventListener>();

  constructor() {
    this.queue = getEnhancedJobQueue();
    this.progressTracker = getProgressTracker();
    this.webSocketManager = getWebSocketManager();
    this.setupEventHandlers();
  }

  /**
   * Create and queue a new job
   */
  async createJob(
    type: JobType,
    data: any,
    options: JobOptions = {}
  ): Promise<Job> {
    try {
      console.log(`🎯 Creating job: ${type}`);
      
      // Add job to queue
      const job = await this.queue.add(type, data, options);
      
      // Emit job created event
      this.emitJobEvent('created', job);
      
      return job;
    } catch (error) {
      console.error(`Failed to create job ${type}:`, error);
      throw error;
    }
  }

  /**
   * Get job by ID with real-time status
   */
  async getJob(id: string): Promise<Job | null> {
    try {
      const job = await this.queue.getJob(id);
      if (!job) return null;

      // Enhance with real-time progress data
      const progress = this.progressTracker.getProgress(id);
      if (progress) {
        job.progress = progress.progress;
        job.progressDetails = progress.details;
      }

      return job;
    } catch (error) {
      console.error(`Failed to get job ${id}:`, error);
      return null;
    }
  }

  /**
   * Get jobs with filtering and pagination
   */
  async getJobs(filter: JobHistoryFilter = {}): Promise<Job[]> {
    try {
      let jobs: Job[];

      if (filter.status && filter.status.length === 1) {
        jobs = await this.queue.getJobs(filter.status[0]);
      } else {
        jobs = await this.queue.getJobHistory(filter.limit, filter.offset);
      }

      // Apply additional filters
      if (filter.type && filter.type.length > 0) {
        jobs = jobs.filter(job => filter.type!.includes(job.type));
      }

      if (filter.toolId) {
        jobs = jobs.filter(job => job.toolId === filter.toolId);
      }

      if (filter.dateFrom) {
        jobs = jobs.filter(job => job.createdAt >= filter.dateFrom!);
      }

      if (filter.dateTo) {
        jobs = jobs.filter(job => job.createdAt <= filter.dateTo!);
      }

      // Enhance with real-time progress data
      jobs.forEach(job => {
        const progress = this.progressTracker.getProgress(job.id);
        if (progress) {
          job.progress = progress.progress;
          job.progressDetails = progress.details;
        }
      });

      return jobs;
    } catch (error) {
      console.error('Failed to get jobs:', error);
      return [];
    }
  }

  /**
   * Pause a job
   */
  async pauseJob(id: string, options: JobControlOptions = {}): Promise<Job> {
    try {
      console.log(`⏸️ Pausing job ${id}${options.reason ? ` - ${options.reason}` : ''}`);
      
      const job = await this.queue.pauseJob(id);
      
      // Emit job paused event
      this.emitJobEvent('paused', job, options);
      
      return job;
    } catch (error) {
      console.error(`Failed to pause job ${id}:`, error);
      throw error;
    }
  }

  /**
   * Resume a paused job
   */
  async resumeJob(id: string, options: JobControlOptions = {}): Promise<Job> {
    try {
      console.log(`▶️ Resuming job ${id}${options.reason ? ` - ${options.reason}` : ''}`);
      
      const job = await this.queue.resumeJob(id);
      
      // Emit job resumed event
      this.emitJobEvent('resumed', job, options);
      
      return job;
    } catch (error) {
      console.error(`Failed to resume job ${id}:`, error);
      throw error;
    }
  }

  /**
   * Stop a job
   */
  async stopJob(id: string, options: JobControlOptions = {}): Promise<Job> {
    try {
      console.log(`⏹️ Stopping job ${id}${options.reason ? ` - ${options.reason}` : ''}`);
      
      const job = await this.queue.stopJob(id);
      
      // Emit job stopped event
      this.emitJobEvent('stopped', job, options);
      
      return job;
    } catch (error) {
      console.error(`Failed to stop job ${id}:`, error);
      throw error;
    }
  }

  /**
   * Retry a failed job
   */
  async retryJob(id: string, options: JobControlOptions = {}): Promise<Job> {
    try {
      console.log(`🔄 Retrying job ${id}${options.reason ? ` - ${options.reason}` : ''}`);
      
      const job = await this.queue.retryJob(id);
      
      // Emit job retried event
      this.emitJobEvent('retried', job, options);
      
      return job;
    } catch (error) {
      console.error(`Failed to retry job ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a job
   */
  async deleteJob(id: string, options: JobControlOptions = {}): Promise<boolean> {
    try {
      console.log(`🗑️ Deleting job ${id}${options.reason ? ` - ${options.reason}` : ''}`);
      
      const job = await this.getJob(id);
      const success = await this.queue.removeJob(id);
      
      if (success && job) {
        // Emit job deleted event
        this.emitJobEvent('deleted', job, options);
      }
      
      return success;
    } catch (error) {
      console.error(`Failed to delete job ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get jobs by tool ID
   */
  async getJobsByTool(toolId: string): Promise<Job[]> {
    try {
      const jobs = await this.queue.getJobsByTool(toolId);
      
      // Enhance with real-time progress data
      jobs.forEach(job => {
        const progress = this.progressTracker.getProgress(job.id);
        if (progress) {
          job.progress = progress.progress;
          job.progressDetails = progress.details;
        }
      });
      
      return jobs;
    } catch (error) {
      console.error(`Failed to get jobs for tool ${toolId}:`, error);
      return [];
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<QueueStats> {
    try {
      const stats = await this.queue.getQueueStats();
      
      // Add real-time progress statistics
      const progressStats = this.progressTracker.getProgressStats();
      
      return {
        ...stats,
        // Note: activeJobs and averageProgress from progressStats are additional metrics
        // that could be added to QueueStats interface in the future
      };
    } catch (error) {
      console.error('Failed to get queue stats:', error);
      return {
        totalJobs: 0,
        pendingJobs: 0,
        processingJobs: 0,
        completedJobs: 0,
        failedJobs: 0,
        pausedJobs: 0,
        averageProcessingTime: 0,
        successRate: 0,
        lastUpdated: new Date(),
      };
    }
  }

  /**
   * Subscribe to job events
   */
  subscribeToJobEvents(jobId: string, listener: JobEventListener): () => void {
    this.eventListeners.set(jobId, listener);
    
    // Subscribe to progress updates
    const unsubscribeProgress = this.progressTracker.subscribeToJob(jobId, (data) => {
      if (listener.onProgressUpdate) {
        // Get the full job data
        this.getJob(jobId).then(job => {
          if (job) {
            listener.onProgressUpdate!(job, data.progress, data.details);
          }
        });
      }
    });
    
    return () => {
      this.eventListeners.delete(jobId);
      unsubscribeProgress();
    };
  }

  /**
   * Subscribe to WebSocket updates for a job
   */
  subscribeToWebSocketUpdates(connectionId: string, jobId: string): void {
    this.webSocketManager.subscribeToJob(connectionId, jobId);
  }

  /**
   * Unsubscribe from WebSocket updates for a job
   */
  unsubscribeFromWebSocketUpdates(connectionId: string, jobId: string): void {
    this.webSocketManager.unsubscribeFromJob(connectionId, jobId);
  }

  /**
   * Get WebSocket manager statistics
   */
  getWebSocketStats() {
    return this.webSocketManager.getStats();
  }

  /**
   * Setup event handlers for job lifecycle events
   */
  private setupEventHandlers(): void {
    // Listen for progress tracker events
    this.progressTracker.on('progress', (data) => {
      const listener = this.eventListeners.get(data.jobId);
      if (listener && listener.onProgressUpdate) {
        this.getJob(data.jobId).then(job => {
          if (job) {
            listener.onProgressUpdate!(job, data.progress, data.details);
          }
        });
      }
    });
  }

  /**
   * Emit job event to registered listeners
   */
  private emitJobEvent(
    eventType: string, 
    job: Job, 
    options?: JobControlOptions
  ): void {
    const listener = this.eventListeners.get(job.id);
    if (!listener) return;

    switch (eventType) {
      case 'created':
      case 'retried':
      case 'resumed':
        // These events don't have specific handlers, but could be added
        break;
      case 'paused':
      case 'stopped':
      case 'deleted':
        // These events don't have specific handlers, but could be added
        break;
      case 'completed':
        if (listener.onCompleted) {
          listener.onCompleted(job, job.result);
        }
        break;
      case 'failed':
        if (listener.onError) {
          listener.onError(job, new Error(job.error || 'Job failed'));
        }
        break;
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.eventListeners.clear();
    this.progressTracker.cleanup();
    this.webSocketManager.cleanup();
  }
}

// Singleton instance
let jobManagerInstance: JobManager | null = null;

export function getJobManager(): JobManager {
  if (!jobManagerInstance) {
    jobManagerInstance = new JobManager();
  }
  return jobManagerInstance;
}
