'use client';

import React from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { JobMetricsCards } from '@/components/admin/JobMetricsCards';

/**
 * Test page for Job Monitoring Dashboard components
 * This page can be used to test the dashboard components with mock data
 */
export default function JobMonitoringTestPage(): React.JSX.Element {
  // Mock metrics data for testing
  const mockMetrics = {
    totalJobs: 1247,
    activeJobs: 8,
    queuedJobs: 23,
    completedToday: 156,
    failedJobs: 3,
    successRate: 97.2,
    averageProcessingTime: 45000, // 45 seconds
    queueHealth: 'healthy' as const
  };

  return (
    <div className="min-h-screen bg-zinc-900 text-white font-roboto">
      <div className="container mx-auto px-4 py-8" style={{ maxWidth: 'var(--container-width)' }}>
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              Job Monitoring Dashboard - Test Page
            </h1>
            <p className="text-gray-300">
              Testing job monitoring components with mock data
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.href = '/admin/jobs'}
            >
              → Go to Live Dashboard
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.href = '/admin'}
            >
              ← Back to Admin
            </Button>
          </div>
        </div>

        {/* Test Metrics Cards */}
        <Card className="bg-zinc-800 border border-zinc-700 p-6 mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Job Metrics Cards Test</h2>
          <JobMetricsCards 
            metrics={mockMetrics}
            isLoading={false}
            className="mb-4"
          />
          
          {/* Loading State Test */}
          <h3 className="text-lg font-semibold text-white mb-4 mt-8">Loading State Test</h3>
          <JobMetricsCards 
            metrics={mockMetrics}
            isLoading={true}
          />
        </Card>

        {/* Component Status */}
        <Card className="bg-zinc-800 border border-zinc-700 p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Component Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="text-lg font-medium text-white">Implemented Components</h3>
              <ul className="space-y-1 text-sm text-gray-300">
                <li>✅ JobMetricsCards - Real-time metrics display</li>
                <li>✅ JobMonitoringDashboard - Main dashboard orchestrator</li>
                <li>✅ JobListTable - Sortable job list with actions</li>
                <li>✅ JobDetailsModal - Detailed job inspection</li>
                <li>✅ JobFiltersPanel - Advanced filtering options</li>
                <li>✅ JobBulkActions - Bulk operations for multiple jobs</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-medium text-white">Custom Hooks</h3>
              <ul className="space-y-1 text-sm text-gray-300">
                <li>✅ useJobMonitoring - Job data fetching and state management</li>
                <li>✅ useWebSocketConnection - Real-time updates via WebSocket</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
            <h3 className="text-lg font-medium text-blue-300 mb-2">Next Steps</h3>
            <ul className="space-y-1 text-sm text-blue-200">
              <li>• Test with real job data from the API</li>
              <li>• Verify WebSocket connections for real-time updates</li>
              <li>• Test job actions (pause, resume, stop, delete)</li>
              <li>• Validate filtering and search functionality</li>
              <li>• Test bulk operations with multiple jobs</li>
            </ul>
          </div>
        </Card>
      </div>
    </div>
  );
}
