'use client';

import { useState, useRef, useCallback } from 'react';
// Import only the file processors, not the full bulk processing engine
import { TextFileProcessor, JSONFileProcessor, FILE_SPECS } from '@/lib/bulk-processing/file-processors';

interface FileUploadSectionProps {
  active: boolean;
  onSelect: () => void;
  onDataProcessed: (data: any) => void;
  onError: (error: string) => void;
}

/**
 * File Upload Section Component
 * 
 * Handles drag-and-drop file upload for .txt and .json files
 * with validation and processing preview.
 */
export function FileUploadSection({
  active,
  onSelect,
  onDataProcessed,
  onError,
}: FileUploadSectionProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const handleFileSelection = async (file: File) => {
    if (!active) {
      onSelect();
    }

    // Validate file type
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    const isTextFile = FILE_SPECS.text.allowedExtensions.includes(extension as any);
    const isJsonFile = FILE_SPECS.json.allowedExtensions.includes(extension as any);

    if (!isTextFile && !isJsonFile) {
      onError('Invalid file type. Please upload a .txt or .json file.');
      return;
    }

    // Validate file size
    const maxSize = isTextFile ? FILE_SPECS.text.maxSize : FILE_SPECS.json.maxSize;
    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      onError(`File too large. Maximum size is ${maxSizeMB}MB.`);
      return;
    }

    setUploadedFile(file);
    setProcessing(true);

    try {
      let result;
      
      if (isTextFile) {
        const processor = new TextFileProcessor();
        result = await processor.processFile(file);
      } else {
        const processor = new JSONFileProcessor();
        result = await processor.processFile(file);
      }

      if (result.success && result.data) {
        onDataProcessed(result.data);
      } else {
        onError(result.error || 'Failed to process file');
      }
    } catch (err) {
      onError(err instanceof Error ? err.message : 'Failed to process file');
    } finally {
      setProcessing(false);
    }
  };

  const handleBrowseClick = () => {
    if (!active) {
      onSelect();
    }
    fileInputRef.current?.click();
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`
      bg-zinc-800 border-2 rounded-lg p-6 transition-all duration-200 cursor-pointer
      ${active 
        ? 'border-orange-500 bg-zinc-800' 
        : 'border-zinc-700 hover:border-zinc-600'
      }
      ${isDragging ? 'border-orange-400 bg-orange-500/10' : ''}
    `}>
      <div className="text-center">
        <div className="mb-4">
          <div className={`
            inline-flex items-center justify-center w-16 h-16 rounded-full mb-4
            ${active ? 'bg-orange-500 text-white' : 'bg-zinc-700 text-gray-400'}
          `}>
            <span className="text-2xl">📁</span>
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">File Upload</h3>
          <p className="text-gray-400 text-sm">
            Upload .txt or .json files with tool URLs and data
          </p>
        </div>

        {/* Upload Area */}
        <div
          className={`
            border-2 border-dashed rounded-lg p-8 transition-colors
            ${isDragging 
              ? 'border-orange-400 bg-orange-500/5' 
              : 'border-zinc-600 hover:border-zinc-500'
            }
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleBrowseClick}
        >
          {processing ? (
            <div className="space-y-3">
              <div className="animate-spin w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full mx-auto"></div>
              <p className="text-gray-400">Processing file...</p>
            </div>
          ) : uploadedFile ? (
            <div className="space-y-3">
              <div className="text-green-400 text-2xl">✓</div>
              <div>
                <p className="text-white font-medium">{uploadedFile.name}</p>
                <p className="text-gray-400 text-sm">{formatFileSize(uploadedFile.size)}</p>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setUploadedFile(null);
                }}
                className="text-orange-400 hover:text-orange-300 text-sm"
              >
                Remove file
              </button>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="text-4xl text-gray-500">⬆️</div>
              <div>
                <p className="text-white font-medium">
                  Drag and drop files here
                </p>
                <p className="text-gray-400 text-sm">
                  or <span className="text-orange-400">browse</span> to select
                </p>
              </div>
            </div>
          )}
        </div>

        {/* File Specifications */}
        <div className="mt-6 text-left">
          <h4 className="text-sm font-medium text-white mb-3">Supported Formats:</h4>
          <div className="space-y-2 text-sm text-gray-400">
            <div className="flex justify-between">
              <span>📄 Text Files (.txt)</span>
              <span>Max 10MB</span>
            </div>
            <div className="flex justify-between">
              <span>📋 JSON Files (.json)</span>
              <span>Max 50MB</span>
            </div>
          </div>
          <div className="mt-3 text-xs text-gray-500">
            Text files should contain one URL per line. JSON files support multiple formats.
          </div>
        </div>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept=".txt,.json"
          onChange={handleFileInput}
          className="hidden"
        />
      </div>
    </div>
  );
}
