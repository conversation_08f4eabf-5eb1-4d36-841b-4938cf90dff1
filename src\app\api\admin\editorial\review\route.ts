import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { validateApi<PERSON>ey } from '@/lib/auth';
import { ManualReview } from '@/lib/content-generation/manual-review';
import { EditorialControls } from '@/lib/content-generation/editorial-controls';

/**
 * POST /api/admin/editorial/review
 * Submit editorial review decision for a tool submission
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      submissionId, 
      decision, 
      reviewNotes, 
      priority, 
      featuredDate, 
      editorialText, 
      qualityScore,
      reviewerId = 'admin'
    } = body;

    // Validate required fields
    if (!submissionId || !decision || !reviewNotes) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: submissionId, decision, reviewNotes' 
        },
        { status: 400 }
      );
    }

    // Validate decision type
    if (!['approve', 'reject', 'needs_revision'].includes(decision)) {
      return NextResponse.json(
        { success: false, error: 'Invalid decision type' },
        { status: 400 }
      );
    }

    // Get the submission details
    const { data: submission, error: submissionError } = await supabase
      .from('tool_submissions')
      .select('*')
      .eq('id', submissionId)
      .single();

    if (submissionError || !submission) {
      return NextResponse.json(
        { success: false, error: 'Submission not found' },
        { status: 404 }
      );
    }

    // Map decision to status
    const statusMap = {
      'approve': 'approved',
      'reject': 'rejected',
      'needs_revision': 'needs_revision'
    };

    const newStatus = statusMap[decision as keyof typeof statusMap];

    // Update submission status and review information
    const updateData = {
      status: newStatus,
      review_notes: reviewNotes,
      reviewed_at: new Date().toISOString(),
      reviewed_by: reviewerId,
      priority: priority || submission.priority || 'normal',
      updated_at: new Date().toISOString()
    };

    const { data: updatedSubmission, error: updateError } = await supabase
      .from('tool_submissions')
      .update(updateData)
      .eq('id', submissionId)
      .select()
      .single();

    if (updateError) {
      console.error('Failed to update submission:', updateError);
      return NextResponse.json(
        { success: false, error: 'Failed to update submission' },
        { status: 500 }
      );
    }

    let toolId: string | null = null;
    let editorialReview = null;

    // If approved, create tool record and editorial review
    if (decision === 'approve') {
      try {
        // Create tool record in tools table
        const toolData = {
          id: `tool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: submission.name,
          url: submission.url,
          description: submission.description,
          category: submission.category,
          subcategory: submission.subcategory,
          pricing_type: submission.pricing_type,
          content_status: 'draft',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        const { data: newTool, error: toolError } = await supabase
          .from('tools')
          .insert(toolData)
          .select()
          .single();

        if (toolError) {
          console.error('Failed to create tool:', toolError);
          // Don't fail the entire request, just log the error
        } else {
          toolId = newTool.id;

          // Create editorial review record
          try {
            const manualReview = new ManualReview();
            const editorialControls = new EditorialControls();

            // Validate editorial text format if provided
            if (editorialText) {
              editorialControls.validateEditorialTextFormat(editorialText);
            }

            editorialReview = await manualReview.submitReview({
              toolId: newTool.id,
              reviewerId,
              decision: 'approve',
              reviewNotes,
              featuredDate,
              editorialText,
              qualityOverride: qualityScore
            });

            console.log('Editorial review created:', editorialReview.id);
          } catch (editorialError) {
            console.error('Failed to create editorial review:', editorialError);
            // Don't fail the request, editorial review is optional
          }
        }
      } catch (toolCreationError) {
        console.error('Tool creation failed:', toolCreationError);
        // Don't fail the request, tool creation can be done later
      }
    }

    // Send notification email to submitter
    try {
      await sendReviewNotification(submission, decision, reviewNotes);
    } catch (emailError) {
      console.error('Failed to send notification email:', emailError);
      // Don't fail the request if email fails
    }

    // Transform response data
    const transformedSubmission = {
      id: updatedSubmission.id,
      name: updatedSubmission.name,
      url: updatedSubmission.url,
      description: updatedSubmission.description,
      category: updatedSubmission.category,
      subcategory: updatedSubmission.subcategory,
      submitterName: updatedSubmission.submitter_name,
      submitterEmail: updatedSubmission.submitter_email,
      status: updatedSubmission.status,
      submittedAt: updatedSubmission.submitted_at,
      reviewedAt: updatedSubmission.reviewed_at,
      reviewNotes: updatedSubmission.review_notes,
      priority: updatedSubmission.priority,
      pricingType: updatedSubmission.pricing_type
    };

    const response: any = {
      success: true,
      submission: transformedSubmission,
      message: `Submission ${decision}d successfully`
    };

    if (toolId) {
      response.toolId = toolId;
    }

    if (editorialReview) {
      response.editorialReview = {
        id: editorialReview.id,
        status: editorialReview.reviewStatus,
        qualityScore: editorialReview.qualityScore
      };
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Editorial review API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/editorial/review
 * Get editorial review information for a specific submission or tool
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin access
    const isValidAdmin = await validateApiKey(request);
    if (!isValidAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const submissionId = searchParams.get('submissionId');
    const toolId = searchParams.get('toolId');

    if (!submissionId && !toolId) {
      return NextResponse.json(
        { success: false, error: 'Either submissionId or toolId is required' },
        { status: 400 }
      );
    }

    let reviewData = null;

    if (submissionId) {
      // Get submission review information
      const { data: submission, error } = await supabase
        .from('tool_submissions')
        .select('*')
        .eq('id', submissionId)
        .single();

      if (error || !submission) {
        return NextResponse.json(
          { success: false, error: 'Submission not found' },
          { status: 404 }
        );
      }

      reviewData = {
        type: 'submission',
        id: submission.id,
        status: submission.status,
        reviewNotes: submission.review_notes,
        reviewedAt: submission.reviewed_at,
        reviewedBy: submission.reviewed_by,
        priority: submission.priority
      };
    }

    if (toolId) {
      // Get editorial review information
      const { data: editorialReview, error } = await supabase
        .from('editorial_reviews')
        .select('*')
        .eq('tool_id', toolId)
        .single();

      if (error || !editorialReview) {
        return NextResponse.json(
          { success: false, error: 'Editorial review not found' },
          { status: 404 }
        );
      }

      reviewData = {
        type: 'editorial',
        id: editorialReview.id,
        toolId: editorialReview.tool_id,
        status: editorialReview.review_status,
        reviewNotes: editorialReview.review_notes,
        editorialText: editorialReview.editorial_text,
        qualityScore: editorialReview.quality_score,
        featuredDate: editorialReview.featured_date,
        reviewedBy: editorialReview.reviewed_by,
        reviewDate: editorialReview.review_date,
        createdAt: editorialReview.created_at,
        updatedAt: editorialReview.updated_at
      };
    }

    return NextResponse.json({
      success: true,
      review: reviewData
    });

  } catch (error) {
    console.error('Get editorial review API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Send notification email to submitter about review decision
 */
async function sendReviewNotification(
  submission: any, 
  decision: string, 
  reviewNotes: string
): Promise<void> {
  // This is a placeholder for email notification functionality
  // In a real implementation, you would integrate with an email service
  // like SendGrid, AWS SES, or similar
  
  console.log(`Sending ${decision} notification to ${submission.submitter_email}`);
  console.log(`Tool: ${submission.name}`);
  console.log(`Notes: ${reviewNotes}`);
  
  // Example email content structure:
  const emailContent = {
    to: submission.submitter_email,
    subject: `AI Tool Submission ${decision.charAt(0).toUpperCase() + decision.slice(1)}: ${submission.name}`,
    template: decision === 'approve' ? 'submission-approved' : 'submission-rejected',
    data: {
      submitterName: submission.submitter_name,
      toolName: submission.name,
      toolUrl: submission.url,
      reviewNotes,
      decision
    }
  };

  // TODO: Implement actual email sending
  // await emailService.send(emailContent);
}
