/**
 * Performance and Load Testing Suite
 * 
 * Tests system performance under various load conditions:
 * - API endpoint response times
 * - Database query performance
 * - Concurrent request handling
 * - Memory usage monitoring
 * - Bulk processing performance
 */

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  threshold?: number;
  status: 'pass' | 'fail' | 'warning';
}

interface LoadTestResult {
  testName: string;
  duration: number;
  requestCount: number;
  successCount: number;
  failureCount: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  throughput: number; // requests per second
  metrics: PerformanceMetric[];
}

class LoadTester {
  private baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  private adminApiKey = process.env.ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';
  private results: LoadTestResult[] = [];

  async runLoadTests(): Promise<void> {
    console.log('⚡ Enhanced AI System - Performance & Load Testing');
    console.log('==================================================\n');

    await this.testAPIEndpointPerformance();
    await this.testConcurrentRequests();
    await this.testDatabasePerformance();
    await this.testBulkProcessingPerformance();
    await this.testMemoryUsage();

    this.generatePerformanceReport();
  }

  private async testAPIEndpointPerformance(): Promise<void> {
    console.log('🌐 Testing API Endpoint Performance...');

    const endpoints = [
      { path: '/api/automation/jobs', method: 'GET' },
      { path: '/api/admin/config', method: 'GET' },
      { path: '/api/admin/editorial/submissions', method: 'GET' },
      { path: '/api/admin/bulk-processing', method: 'GET' }
    ];

    for (const endpoint of endpoints) {
      const testName = `${endpoint.method} ${endpoint.path}`;
      console.log(`  Testing ${testName}...`);

      const responseTimes: number[] = [];
      const requestCount = 10;
      let successCount = 0;
      let failureCount = 0;

      const startTime = Date.now();

      for (let i = 0; i < requestCount; i++) {
        const requestStart = Date.now();
        try {
          const response = await fetch(`${this.baseUrl}${endpoint.path}`, {
            method: endpoint.method,
            headers: {
              'x-api-key': this.adminApiKey,
              'Content-Type': 'application/json'
            }
          });

          const responseTime = Date.now() - requestStart;
          responseTimes.push(responseTime);

          if (response.ok) {
            successCount++;
          } else {
            failureCount++;
          }
        } catch (error) {
          const responseTime = Date.now() - requestStart;
          responseTimes.push(responseTime);
          failureCount++;
        }
      }

      const totalDuration = Date.now() - startTime;
      const averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
      const minResponseTime = Math.min(...responseTimes);
      const maxResponseTime = Math.max(...responseTimes);
      const throughput = (requestCount / totalDuration) * 1000; // requests per second

      const metrics: PerformanceMetric[] = [
        {
          name: 'Average Response Time',
          value: averageResponseTime,
          unit: 'ms',
          threshold: 1000,
          status: averageResponseTime < 1000 ? 'pass' : 'warning'
        },
        {
          name: 'Max Response Time',
          value: maxResponseTime,
          unit: 'ms',
          threshold: 2000,
          status: maxResponseTime < 2000 ? 'pass' : 'warning'
        },
        {
          name: 'Success Rate',
          value: (successCount / requestCount) * 100,
          unit: '%',
          threshold: 95,
          status: (successCount / requestCount) * 100 >= 95 ? 'pass' : 'fail'
        }
      ];

      this.results.push({
        testName,
        duration: totalDuration,
        requestCount,
        successCount,
        failureCount,
        averageResponseTime,
        minResponseTime,
        maxResponseTime,
        throughput,
        metrics
      });

      console.log(`    ✅ ${testName}: ${averageResponseTime.toFixed(2)}ms avg, ${successCount}/${requestCount} success`);
    }
  }

  private async testConcurrentRequests(): Promise<void> {
    console.log('\n🔄 Testing Concurrent Request Handling...');

    const concurrentRequests = 5;
    const endpoint = '/api/automation/jobs';
    
    console.log(`  Testing ${concurrentRequests} concurrent requests to ${endpoint}...`);

    const startTime = Date.now();
    const promises = Array(concurrentRequests).fill(null).map(async () => {
      const requestStart = Date.now();
      try {
        const response = await fetch(`${this.baseUrl}${endpoint}`, {
          method: 'GET',
          headers: {
            'x-api-key': this.adminApiKey
          }
        });
        return {
          success: response.ok,
          responseTime: Date.now() - requestStart,
          status: response.status
        };
      } catch (error) {
        return {
          success: false,
          responseTime: Date.now() - requestStart,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    });

    const results = await Promise.all(promises);
    const totalDuration = Date.now() - startTime;
    
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;
    const responseTimes = results.map(r => r.responseTime);
    const averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const throughput = (concurrentRequests / totalDuration) * 1000;

    const metrics: PerformanceMetric[] = [
      {
        name: 'Concurrent Success Rate',
        value: (successCount / concurrentRequests) * 100,
        unit: '%',
        threshold: 90,
        status: (successCount / concurrentRequests) * 100 >= 90 ? 'pass' : 'fail'
      },
      {
        name: 'Concurrent Throughput',
        value: throughput,
        unit: 'req/s',
        threshold: 1,
        status: throughput >= 1 ? 'pass' : 'warning'
      }
    ];

    this.results.push({
      testName: 'Concurrent Requests',
      duration: totalDuration,
      requestCount: concurrentRequests,
      successCount,
      failureCount,
      averageResponseTime,
      minResponseTime: Math.min(...responseTimes),
      maxResponseTime: Math.max(...responseTimes),
      throughput,
      metrics
    });

    console.log(`    ✅ Concurrent Requests: ${successCount}/${concurrentRequests} success, ${averageResponseTime.toFixed(2)}ms avg`);
  }

  private async testDatabasePerformance(): Promise<void> {
    console.log('\n🗄️ Testing Database Performance...');

    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const queries = [
      {
        name: 'Simple Select',
        query: () => supabase.from('tools').select('id, name').limit(10)
      },
      {
        name: 'Complex Select with Filters',
        query: () => supabase
          .from('tools')
          .select('id, name, url, ai_generation_status')
          .not('ai_generation_status', 'is', null)
          .limit(50)
      },
      {
        name: 'Count Query',
        query: () => supabase
          .from('tools')
          .select('*', { count: 'exact', head: true })
      }
    ];

    for (const queryTest of queries) {
      console.log(`  Testing ${queryTest.name}...`);

      const iterations = 5;
      const queryTimes: number[] = [];
      let successCount = 0;

      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        try {
          const { data, error } = await queryTest.query();
          const queryTime = Date.now() - startTime;
          queryTimes.push(queryTime);

          if (!error) {
            successCount++;
          }
        } catch (error) {
          const queryTime = Date.now() - startTime;
          queryTimes.push(queryTime);
        }
      }

      const averageQueryTime = queryTimes.reduce((sum, time) => sum + time, 0) / queryTimes.length;
      const minQueryTime = Math.min(...queryTimes);
      const maxQueryTime = Math.max(...queryTimes);

      const metrics: PerformanceMetric[] = [
        {
          name: 'Average Query Time',
          value: averageQueryTime,
          unit: 'ms',
          threshold: 500,
          status: averageQueryTime < 500 ? 'pass' : 'warning'
        },
        {
          name: 'Query Success Rate',
          value: (successCount / iterations) * 100,
          unit: '%',
          threshold: 100,
          status: successCount === iterations ? 'pass' : 'fail'
        }
      ];

      this.results.push({
        testName: `Database: ${queryTest.name}`,
        duration: queryTimes.reduce((sum, time) => sum + time, 0),
        requestCount: iterations,
        successCount,
        failureCount: iterations - successCount,
        averageResponseTime: averageQueryTime,
        minResponseTime: minQueryTime,
        maxResponseTime: maxQueryTime,
        throughput: (iterations / (queryTimes.reduce((sum, time) => sum + time, 0))) * 1000,
        metrics
      });

      console.log(`    ✅ ${queryTest.name}: ${averageQueryTime.toFixed(2)}ms avg, ${successCount}/${iterations} success`);
    }
  }

  private async testBulkProcessingPerformance(): Promise<void> {
    console.log('\n📦 Testing Bulk Processing Performance...');

    // Test bulk processing API response time
    const startTime = Date.now();
    try {
      const response = await fetch(`${this.baseUrl}/api/admin/bulk-processing`, {
        method: 'GET',
        headers: {
          'x-api-key': this.adminApiKey
        }
      });

      const responseTime = Date.now() - startTime;
      const success = response.ok;

      const metrics: PerformanceMetric[] = [
        {
          name: 'Bulk API Response Time',
          value: responseTime,
          unit: 'ms',
          threshold: 1000,
          status: responseTime < 1000 ? 'pass' : 'warning'
        }
      ];

      this.results.push({
        testName: 'Bulk Processing API',
        duration: responseTime,
        requestCount: 1,
        successCount: success ? 1 : 0,
        failureCount: success ? 0 : 1,
        averageResponseTime: responseTime,
        minResponseTime: responseTime,
        maxResponseTime: responseTime,
        throughput: 1000 / responseTime,
        metrics
      });

      console.log(`    ✅ Bulk Processing API: ${responseTime}ms, ${success ? 'success' : 'failed'}`);
    } catch (error) {
      console.log(`    ❌ Bulk Processing API: Failed - ${error}`);
    }
  }

  private async testMemoryUsage(): Promise<void> {
    console.log('\n💾 Testing Memory Usage...');

    const memoryBefore = process.memoryUsage();
    
    // Simulate some memory-intensive operations
    const largeArray = new Array(100000).fill(0).map((_, i) => ({ id: i, data: `test-${i}` }));
    
    // Perform some operations
    const filtered = largeArray.filter(item => item.id % 2 === 0);
    const mapped = filtered.map(item => ({ ...item, processed: true }));
    
    const memoryAfter = process.memoryUsage();
    
    const memoryDelta = {
      rss: memoryAfter.rss - memoryBefore.rss,
      heapUsed: memoryAfter.heapUsed - memoryBefore.heapUsed,
      heapTotal: memoryAfter.heapTotal - memoryBefore.heapTotal,
      external: memoryAfter.external - memoryBefore.external
    };

    const metrics: PerformanceMetric[] = [
      {
        name: 'Heap Used Delta',
        value: memoryDelta.heapUsed / 1024 / 1024,
        unit: 'MB',
        threshold: 50,
        status: (memoryDelta.heapUsed / 1024 / 1024) < 50 ? 'pass' : 'warning'
      },
      {
        name: 'RSS Delta',
        value: memoryDelta.rss / 1024 / 1024,
        unit: 'MB',
        threshold: 100,
        status: (memoryDelta.rss / 1024 / 1024) < 100 ? 'pass' : 'warning'
      }
    ];

    this.results.push({
      testName: 'Memory Usage',
      duration: 0,
      requestCount: 1,
      successCount: 1,
      failureCount: 0,
      averageResponseTime: 0,
      minResponseTime: 0,
      maxResponseTime: 0,
      throughput: 0,
      metrics
    });

    console.log(`    ✅ Memory Usage: Heap +${(memoryDelta.heapUsed / 1024 / 1024).toFixed(2)}MB, RSS +${(memoryDelta.rss / 1024 / 1024).toFixed(2)}MB`);
  }

  private generatePerformanceReport(): void {
    console.log('\n📊 PERFORMANCE TEST REPORT');
    console.log('==================================================');

    let totalTests = 0;
    let passedMetrics = 0;
    let totalMetrics = 0;

    this.results.forEach(result => {
      totalTests++;
      console.log(`\n📋 ${result.testName}:`);
      console.log(`   Duration: ${result.duration}ms`);
      console.log(`   Requests: ${result.requestCount} | Success: ${result.successCount} | Failed: ${result.failureCount}`);
      console.log(`   Avg Response: ${result.averageResponseTime.toFixed(2)}ms`);
      console.log(`   Throughput: ${result.throughput.toFixed(2)} req/s`);

      if (result.metrics.length > 0) {
        console.log('   Metrics:');
        result.metrics.forEach(metric => {
          totalMetrics++;
          if (metric.status === 'pass') passedMetrics++;
          
          const statusIcon = metric.status === 'pass' ? '✅' : metric.status === 'warning' ? '⚠️' : '❌';
          console.log(`     ${statusIcon} ${metric.name}: ${metric.value.toFixed(2)} ${metric.unit}`);
        });
      }
    });

    console.log('\n🎯 OVERALL PERFORMANCE RESULTS:');
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Metrics Passed: ${passedMetrics}/${totalMetrics} (${Math.round((passedMetrics / totalMetrics) * 100)}%)`);
    
    const overallStatus = passedMetrics === totalMetrics ? '✅ ALL METRICS PASSED' : 
                         passedMetrics / totalMetrics >= 0.8 ? '⚠️ MOSTLY PASSED' : '❌ PERFORMANCE ISSUES';
    console.log(`\n🏆 Status: ${overallStatus}`);
    console.log('==================================================\n');
  }
}

// Export for use in other test files
export { LoadTester, PerformanceMetric, LoadTestResult };

// Main execution when run directly
if (require.main === module) {
  const tester = new LoadTester();
  tester.runLoadTests().catch(console.error);
}
