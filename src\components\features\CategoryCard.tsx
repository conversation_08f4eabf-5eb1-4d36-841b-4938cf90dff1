'use client';

import React, { memo, useCallback, useState } from 'react';
import { ArrowRight } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { AICategory } from '@/lib/types';
import { Icon } from '@/components/ui/Icon';
import { ToolListItem } from './ToolListItem';
import { getLayoutClasses, activeLayoutConfig } from '@/config/layout';

/**
 * CategoryCard Component - Pixel-perfect implementation matching AI-DUDE.COM design system
 *
 * Features:
 * - TypeScript interfaces matching existing architecture
 * - Pixel-perfect visual design with proper spacing and typography
 * - Dynamic border colors matching category button color schemes
 * - Interactive title tooltips with animated underline effects
 * - Color-matched underlines that expand from center on hover
 * - Integrated tooltip system for tool and category descriptions
 * - Responsive behavior with proper text truncation
 * - Accessibility support with semantic HTML and ARIA labels
 * - Performance optimized with React.memo and stable callbacks
 * - Smooth animations and hover effects with CSS custom properties
 * - Automatic color extraction from button classes with fallback
 */

interface CategoryCardProps {
  category: AICategory;
  onShowTooltip: (content: string, element: HTMLElement, triggerType?: 'title' | 'search-icon') => void;
  onHideTooltip: () => void;
}

export const CategoryCard = memo<CategoryCardProps>(function CategoryCard({
  category,
  onShowTooltip,
  onHideTooltip
}) {
  // State for card hover effect (for separator animation)
  const [isCardHovered, setIsCardHovered] = useState(false);

  // Router for navigation
  const router = useRouter();

  // Get layout configuration
  const layoutClasses = getLayoutClasses(activeLayoutConfig);

  // Stable callback to prevent unnecessary re-renders
  const handleSeeAllClick = useCallback(() => {
    router.push(`/category/${category.id}`);
  }, [category.id, router]);

  // Extract color from button color class for both border and underline
  const getColorInfo = useCallback(() => {
    const colorClass = category.seeAllButton.colorClass;

    // Extract the base color from the background class (e.g., "bg-sky-500" -> "sky-500")
    const bgColorMatch = colorClass.match(/bg-(\w+-\d+)/);

    if (bgColorMatch) {
      const colorName = bgColorMatch[1]; // e.g., "sky-500", "green-500", "pink-500", "yellow-600"

      // Map Tailwind colors to CSS custom properties
      const colorMap: Record<string, string> = {
        'sky-500': '#0ea5e9',
        'green-500': '#22c55e',
        'pink-500': '#ec4899',
        'yellow-600': '#ca8a04',
        'purple-500': '#a855f7',
        'indigo-500': '#6366f1',
        'orange-500': '#f97316',
        'teal-500': '#14b8a6',
        'cyan-500': '#06b6d4',
        'rose-500': '#f43f5e',
        'amber-500': '#f59e0b',
        'violet-500': '#8b5cf6',
        'emerald-500': '#10b981',
        'lime-500': '#84cc16',
        'zinc-700': '#3f3f46'
      };

      return {
        tailwindClass: colorName,
        cssColor: colorMap[colorName] || colorMap['zinc-700']
      };
    }

    // Fallback to default zinc color if extraction fails
    return {
      tailwindClass: 'zinc-700',
      cssColor: '#3f3f46'
    };
  }, [category.seeAllButton.colorClass]);

  const colorInfo = getColorInfo();

  // Card hover handlers for separator animation
  const handleCardMouseEnter = useCallback(() => {
    setIsCardHovered(true);
  }, []);

  const handleCardMouseLeave = useCallback(() => {
    setIsCardHovered(false);
  }, []);

  // Title hover handlers for tooltip (separate from animation)
  const handleTitleMouseEnter = useCallback((e: React.MouseEvent<HTMLHeadingElement>) => {
    onShowTooltip(category.description, e.currentTarget);
  }, [category.description, onShowTooltip]);

  const handleTitleMouseLeave = useCallback(() => {
    onHideTooltip();
  }, [onHideTooltip]);

  return (
    <article
      className={`${layoutClasses.cardContainer} enhanced-card`}
      style={{
        '--border-color': colorInfo.cssColor,
        '--separator-color': colorInfo.cssColor,
        '--glow-color': colorInfo.cssColor.replace(/[^\d,]/g, ''), // Extract RGB values
        borderColor: colorInfo.cssColor,
      } as React.CSSProperties & {
        '--border-color': string;
        '--separator-color': string;
        '--glow-color': string;
      }}
      role="region"
      aria-labelledby={`category-${category.id}-title`}
      aria-describedby={`category-${category.id}-description`}
      onMouseEnter={handleCardMouseEnter}
      onMouseLeave={handleCardMouseLeave}
    >
      {/* Card Header - Fixed (Icon + Title only) */}
      <header className="text-center flex-shrink-0">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Icon
            name={category.iconName as keyof typeof import('lucide-react')}
            size={16}
            className="text-white flex-shrink-0 enhanced-icon"
            aria-hidden="true"
          />
          <h3
            id={`category-${category.id}-title`}
            className="text-base font-bold leading-tight cursor-pointer text-white category-title-hover"
            onMouseEnter={handleTitleMouseEnter}
            onMouseLeave={handleTitleMouseLeave}
            role="button"
            tabIndex={0}
            aria-describedby={`category-${category.id}-description`}
            aria-label={`${category.title} - Hover to see description tooltip`}
            title={category.description}
          >
            {category.title}
          </h3>
        </div>

        {/* Enhanced full-width separator line */}
        <div className="relative mb-4">
          <div
            className={`
              h-1 enhanced-separator transform origin-center mx-auto
              ${isCardHovered ? 'w-full active' : 'w-1/4'}
              transition-all duration-300 ease-out
            `}
            style={{
              backgroundColor: colorInfo.cssColor,
            }}
          />
        </div>
      </header>

      {/* Scrollable Content Area (Description + Tool List) */}
      <div className="flex-1 flex flex-col min-h-0">
        <section
          className={layoutClasses.scrollableContent}
          style={layoutClasses.scrollableContentStyle}
          aria-label={`${category.title} content`}
          role="region"
        >
          {/* Description - Now in scrollable area */}
          <p
            id={`category-${category.id}-description`}
            className="text-gray-400 text-xs leading-relaxed mb-4 text-center"
          >
            {category.description}
          </p>

          {/* Tool List */}
          <div className="space-y-0" role="list" aria-label={`${category.title} tools list`}>
            {category.tools.map((tool, index) => (
              <ToolListItem
                key={tool.id}
                tool={tool}
                onShowTooltip={onShowTooltip}
                onHideTooltip={onHideTooltip}
                index={index + 1}
              />
            ))}
          </div>
        </section>
      </div>

      {/* Enhanced CTA Button */}
      <footer className="flex-shrink-0">
        <button
          onClick={handleSeeAllClick}
          className={`
            w-full enhanced-button
            ${category.seeAllButton.colorClass}
            ${category.seeAllButton.textColorClass}
            px-4 py-2
            rounded-lg
            font-semibold text-sm
            flex items-center justify-center gap-2
            focus:outline-none
            focus:ring-2
            focus:ring-offset-2
            focus:ring-offset-zinc-800
            focus:ring-white/20
            shadow-sm
          `}
          aria-label={`View all ${category.totalToolsCount} tools in ${category.title} category`}
          type="button"
        >
          <span>SEE ALL {category.totalToolsCount} TOOLS</span>
          <ArrowRight
            size={14}
            className="flex-shrink-0"
            aria-hidden="true"
          />
        </button>
      </footer>
    </article>
  );
});
