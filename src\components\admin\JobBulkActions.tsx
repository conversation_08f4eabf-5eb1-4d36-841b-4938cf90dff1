'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';

interface JobBulkActionsProps {
  selectedCount: number;
  selectedJobIds: string[];
  onBulkAction: (action: 'pause' | 'resume' | 'stop' | 'delete', jobIds: string[]) => void;
  onClearSelection: () => void;
}

interface BulkActionConfig {
  action: 'pause' | 'resume' | 'stop' | 'delete';
  label: string;
  icon: string;
  color: string;
  confirmationRequired: boolean;
  description: string;
}

/**
 * Job Bulk Actions Component
 * 
 * Provides bulk operation capabilities for selected jobs.
 * Features:
 * - Multiple job selection management
 * - Confirmation dialogs for destructive actions
 * - Progress indication during bulk operations
 * - Clear selection functionality
 * - Action availability based on job states
 */
export function JobBulkActions({
  selectedCount,
  selectedJobIds,
  onBulkAction,
  onClearSelection
}: JobBulkActionsProps): React.JSX.Element {
  const [showConfirmation, setShowConfirmation] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Define available bulk actions
  const bulkActions: BulkActionConfig[] = [
    {
      action: 'pause',
      label: 'Pause',
      icon: '⏸️',
      color: 'bg-yellow-600 hover:bg-yellow-700',
      confirmationRequired: false,
      description: 'Pause selected running jobs'
    },
    {
      action: 'resume',
      label: 'Resume',
      icon: '▶️',
      color: 'bg-green-600 hover:bg-green-700',
      confirmationRequired: false,
      description: 'Resume selected paused jobs'
    },
    {
      action: 'stop',
      label: 'Stop',
      icon: '⏹️',
      color: 'bg-red-600 hover:bg-red-700',
      confirmationRequired: true,
      description: 'Stop selected jobs (cannot be resumed)'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: '🗑️',
      color: 'bg-red-700 hover:bg-red-800',
      confirmationRequired: true,
      description: 'Permanently delete selected jobs'
    }
  ];

  // Handle bulk action execution
  const handleBulkAction = async (actionConfig: BulkActionConfig) => {
    if (actionConfig.confirmationRequired) {
      setShowConfirmation(actionConfig.action);
      return;
    }

    await executeBulkAction(actionConfig.action);
  };

  // Execute the bulk action
  const executeBulkAction = async (action: 'pause' | 'resume' | 'stop' | 'delete') => {
    setIsProcessing(true);
    try {
      await onBulkAction(action, selectedJobIds);
      setShowConfirmation(null);
    } catch (error) {
      console.error(`Bulk ${action} failed:`, error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Confirmation dialog
  const renderConfirmationDialog = () => {
    if (!showConfirmation) return null;

    const actionConfig = bulkActions.find(a => a.action === showConfirmation);
    if (!actionConfig) return null;

    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6 max-w-md mx-4">
          <div className="text-center">
            <div className="text-4xl mb-4">{actionConfig.icon}</div>
            <h3 className="text-lg font-semibold text-white mb-2">
              Confirm {actionConfig.label}
            </h3>
            <p className="text-gray-300 mb-4">
              Are you sure you want to {actionConfig.action} {selectedCount} selected job{selectedCount !== 1 ? 's' : ''}?
            </p>
            <p className="text-sm text-gray-400 mb-6">
              {actionConfig.description}
            </p>
            
            <div className="flex space-x-3 justify-center">
              <Button
                variant="outline"
                onClick={() => setShowConfirmation(null)}
                disabled={isProcessing}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={() => executeBulkAction(actionConfig.action)}
                disabled={isProcessing}
                className={actionConfig.color}
              >
                {isProcessing ? (
                  <>
                    <span className="animate-spin mr-2">🔄</span>
                    Processing...
                  </>
                ) : (
                  <>
                    {actionConfig.icon} {actionConfig.label}
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-white">
              {selectedCount} job{selectedCount !== 1 ? 's' : ''} selected
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={onClearSelection}
              className="text-xs"
            >
              Clear Selection
            </Button>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {bulkActions.map((actionConfig) => (
            <Button
              key={actionConfig.action}
              variant="primary"
              size="sm"
              onClick={() => handleBulkAction(actionConfig)}
              disabled={isProcessing || selectedCount === 0}
              className={`${actionConfig.color} text-xs`}
            >
              {actionConfig.icon} {actionConfig.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Processing Indicator */}
      {isProcessing && (
        <div className="mt-3 p-3 bg-blue-900/20 border border-blue-500/30 rounded-lg">
          <div className="flex items-center space-x-2">
            <span className="animate-spin text-blue-400">🔄</span>
            <span className="text-blue-300 text-sm">
              Processing bulk action on {selectedCount} job{selectedCount !== 1 ? 's' : ''}...
            </span>
          </div>
        </div>
      )}

      {/* Confirmation Dialog */}
      {renderConfirmationDialog()}
    </>
  );
}

export default JobBulkActions;
