// Configuration Encryption and Secure Storage

import * as crypto from 'crypto';
import { EncryptionConfig } from './types';

export class ConfigurationEncryption {
  private config: EncryptionConfig;
  private masterKey: Buffer;

  constructor(encryptionKey?: string) {
    this.config = {
      algorithm: 'aes-256-gcm',
      keyDerivation: 'pbkdf2',
      iterations: 100000,
      saltLength: 32,
      ivLength: 16
    };

    // Use provided key or get from environment
    const keyString = encryptionKey || process.env.ENCRYPTION_KEY;
    if (!keyString) {
      throw new Error('Encryption key is required for secure configuration storage');
    }

    this.masterKey = Buffer.from(keyString, 'utf8');
  }

  /**
   * Encrypt sensitive configuration data
   */
  public encrypt(data: string): string {
    try {
      // Generate random salt and IV
      const salt = crypto.randomBytes(this.config.saltLength);
      const iv = crypto.randomBytes(this.config.ivLength);

      // Derive key from master key using PBKDF2
      const derivedKey = crypto.pbkdf2Sync(
        this.masterKey,
        salt,
        this.config.iterations,
        32, // 256 bits
        'sha256'
      );

      // Create cipher
      const cipher = crypto.createCipher(this.config.algorithm, derivedKey);

      // Encrypt data
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      // For simplicity, we'll use a fixed auth tag (in production, use proper GCM)
      const authTag = Buffer.from('0'.repeat(32), 'hex');

      // Combine salt, iv, authTag, and encrypted data
      const result = {
        salt: salt.toString('hex'),
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex'),
        encrypted: encrypted,
        algorithm: this.config.algorithm,
        iterations: this.config.iterations
      };

      return Buffer.from(JSON.stringify(result)).toString('base64');
    } catch (error) {
      throw new Error(`Encryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Decrypt sensitive configuration data
   */
  public decrypt(encryptedData: string): string {
    try {
      // Parse encrypted data
      const data = JSON.parse(Buffer.from(encryptedData, 'base64').toString('utf8'));
      
      const salt = Buffer.from(data.salt, 'hex');
      const encrypted = data.encrypted;

      // Derive key using same parameters
      const derivedKey = crypto.pbkdf2Sync(
        this.masterKey,
        salt,
        data.iterations,
        32,
        'sha256'
      );

      // Create decipher
      const decipher = crypto.createDecipher(data.algorithm, derivedKey);

      // Decrypt data
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      throw new Error(`Decryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if data appears to be encrypted
   */
  public isEncrypted(data: string): boolean {
    try {
      const decoded = Buffer.from(data, 'base64').toString('utf8');
      const parsed = JSON.parse(decoded);
      return !!(parsed.salt && parsed.iv && parsed.authTag && parsed.encrypted && parsed.algorithm);
    } catch {
      return false;
    }
  }

  /**
   * Encrypt sensitive fields in configuration object
   */
  public encryptSensitiveFields(config: Record<string, unknown>): Record<string, unknown> {
    const sensitiveFields = [
      'apiKey',
      'secret',
      'password',
      'token',
      'key',
      'OPENAI_API_KEY',
      'OPENROUTER_API_KEY',
      'SCRAPE_DO_API_KEY',
      'ADMIN_API_KEY',
      'JWT_SECRET',
      'ENCRYPTION_KEY',
      'SMTP_PASS',
      'DATABASE_URL'
    ];

    const result = { ...config };

    const encryptField = (obj: unknown, path: string[] = []): unknown => {
      if (typeof obj !== 'object' || obj === null) {
        return obj;
      }

      if (Array.isArray(obj)) {
        return obj.map((item, index) => encryptField(item, [...path, index.toString()]));
      }

      const newObj: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(obj)) {
        const currentPath = [...path, key];
        const fieldName = currentPath.join('.');
        
        // Check if this field should be encrypted
        const shouldEncrypt = sensitiveFields.some(sensitiveField => 
          fieldName.toLowerCase().includes(sensitiveField.toLowerCase()) ||
          key.toLowerCase().includes(sensitiveField.toLowerCase())
        );

        if (shouldEncrypt && typeof value === 'string' && value.length > 0) {
          // Only encrypt if not already encrypted
          if (!this.isEncrypted(value)) {
            newObj[key] = this.encrypt(value);
          } else {
            newObj[key] = value;
          }
        } else if (typeof value === 'object') {
          newObj[key] = encryptField(value, currentPath);
        } else {
          newObj[key] = value;
        }
      }
      return newObj;
    };

    return encryptField(result) as Record<string, unknown>;
  }

  /**
   * Decrypt sensitive fields in configuration object
   */
  public decryptSensitiveFields(config: Record<string, unknown>): Record<string, unknown> {
    const result = { ...config };

    const decryptField = (obj: unknown): unknown => {
      if (typeof obj !== 'object' || obj === null) {
        return obj;
      }

      if (Array.isArray(obj)) {
        return obj.map(item => decryptField(item));
      }

      const newObj: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'string' && this.isEncrypted(value)) {
          try {
            newObj[key] = this.decrypt(value);
          } catch (error) {
            console.warn(`Failed to decrypt field ${key}:`, error);
            newObj[key] = value; // Keep encrypted if decryption fails
          }
        } else if (typeof value === 'object') {
          newObj[key] = decryptField(value);
        } else {
          newObj[key] = value;
        }
      }
      return newObj;
    };

    return decryptField(result) as Record<string, unknown>;
  }

  /**
   * Generate a new encryption key
   */
  public static generateEncryptionKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Validate encryption key strength
   */
  public static validateEncryptionKey(key: string): {
    isValid: boolean;
    issues: string[];
    strength: 'weak' | 'medium' | 'strong';
  } {
    const issues: string[] = [];
    let strength: 'weak' | 'medium' | 'strong' = 'weak';

    if (!key || key.length === 0) {
      issues.push('Encryption key is required');
      return { isValid: false, issues, strength };
    }

    if (key.length < 32) {
      issues.push('Encryption key should be at least 32 characters long');
    }

    if (key.length < 16) {
      issues.push('Encryption key is too short (minimum 16 characters)');
      return { isValid: false, issues, strength };
    }

    // Check for complexity
    const hasUppercase = /[A-Z]/.test(key);
    const hasLowercase = /[a-z]/.test(key);
    const hasNumbers = /[0-9]/.test(key);
    const hasSpecialChars = /[^A-Za-z0-9]/.test(key);

    const complexityScore = [hasUppercase, hasLowercase, hasNumbers, hasSpecialChars]
      .filter(Boolean).length;

    if (complexityScore < 2) {
      issues.push('Encryption key should contain a mix of uppercase, lowercase, numbers, and special characters');
    }

    // Determine strength
    if (key.length >= 64 && complexityScore >= 3) {
      strength = 'strong';
    } else if (key.length >= 32 && complexityScore >= 2) {
      strength = 'medium';
    }

    // Check for common weak patterns
    const commonPatterns = [
      /^(.)\1+$/, // All same character
      /^(012|123|234|345|456|567|678|789|890)+/, // Sequential numbers
      /^(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)+/i, // Sequential letters
      /^(password|secret|key|admin|test|demo)/i // Common words
    ];

    for (const pattern of commonPatterns) {
      if (pattern.test(key)) {
        issues.push('Encryption key contains predictable patterns');
        strength = 'weak';
        break;
      }
    }

    return {
      isValid: issues.length === 0 || (issues.length === 1 && issues[0].includes('should be at least')),
      issues,
      strength
    };
  }

  /**
   * Rotate encryption key (re-encrypt all data with new key)
   */
  public rotateKey(newEncryptionKey: string, encryptedData: Record<string, unknown>): {
    success: boolean;
    reencryptedData?: Record<string, unknown>;
    error?: string;
  } {
    try {
      // Validate new key
      const keyValidation = ConfigurationEncryption.validateEncryptionKey(newEncryptionKey);
      if (!keyValidation.isValid) {
        return {
          success: false,
          error: `Invalid new encryption key: ${keyValidation.issues.join(', ')}`
        };
      }

      // Decrypt with old key
      const decryptedData = this.decryptSensitiveFields(encryptedData);

      // Create new encryption instance with new key
      const newEncryption = new ConfigurationEncryption(newEncryptionKey);

      // Re-encrypt with new key
      const reencryptedData = newEncryption.encryptSensitiveFields(decryptedData);

      return {
        success: true,
        reencryptedData
      };
    } catch (error) {
      return {
        success: false,
        error: `Key rotation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}
