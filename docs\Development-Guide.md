# Development Guide - AI Dude Directory

## Quick Start for New Pages

### 1. Creating a New Page

To create a new page in the AI Dude Directory:

```bash
# Create the page directory
mkdir src/app/[page-name]

# Copy the template
cp docs/Page-Template.tsx src/app/[page-name]/page.tsx
```

### 2. Page Structure

All pages automatically inherit:
- ✅ **Header** with search functionality
- ✅ **Footer** with social links and branding
- ✅ **Dark theme** (zinc-900 background)
- ✅ **Responsive design**
- ✅ **Global search state**
- ✅ **Floating buttons** (scroll to top, chat, etc.)

### 3. Essential Page Code

```tsx
'use client';

import React from 'react';
import { useSearchContext } from '@/providers/SearchProvider';

export default function YourPage() {
  const { searchTerm, searchResults } = useSearchContext();

  return (
    <div className="w-full">
      <div className="mx-auto px-4 py-8" style={{ maxWidth: 'var(--container-width)' }}>
        <h1 className="text-3xl font-bold text-white mb-6">Your Page Title</h1>
        {/* Your content here */}
      </div>
    </div>
  );
}
```

## Design System Quick Reference

### Colors
- **Background**: `bg-zinc-900` (#18181b)
- **Cards**: `bg-zinc-800` (#27272a)
- **Text**: `text-white` (#f4f4f5)
- **Hover**: Custom orange `rgb(255, 150, 0)`

### Layout
- **Container**: `style={{ maxWidth: 'var(--container-width)' }}`
- **Padding**: `px-4` for horizontal, `py-8` for vertical
- **Grid**: `grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4`

### Components
- **Cards**: `bg-zinc-800 border border-zinc-700 p-6 rounded-lg`
- **Buttons**: `bg-zinc-700 hover:bg-orange-500 text-white px-4 py-2 rounded`
- **Hover Effects**: Use custom orange `rgb(255, 150, 0)`

## Common Patterns

### 1. Card Grid Layout
```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {items.map((item) => (
    <div key={item.id} className="bg-zinc-800 border border-zinc-700 p-6 rounded-lg hover:bg-zinc-700 transition-colors duration-200">
      <h3 className="text-white font-medium mb-3">{item.title}</h3>
      <p className="text-zinc-400">{item.description}</p>
    </div>
  ))}
</div>
```

### 2. Custom Orange Hover
```tsx
<button
  className="bg-zinc-700 text-white px-4 py-2 rounded transition-colors duration-200"
  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)'}
  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = ''}
>
  Button Text
</button>
```

### 3. Loading States
```tsx
{isLoading ? (
  <div className="flex items-center justify-center py-20">
    <div className="text-white text-lg">Loading...</div>
  </div>
) : (
  // Content
)}
```

### 4. Search Integration
```tsx
const { searchTerm, searchResults, isLoadingSearchResults } = useSearchContext();

// Show search results if available
{searchTerm && (
  <div className="mb-8">
    <h2 className="text-2xl font-bold text-white mb-4">
      Search Results for "{searchTerm}"
    </h2>
    {/* Display search results */}
  </div>
)}
```

## Development Workflow

### 1. Start Development Server
```bash
npm run dev
```

### 2. Check for Errors
- Watch the terminal for TypeScript errors
- Check browser console for runtime errors
- Test responsive design at different breakpoints

### 3. Testing Checklist
- [ ] Page loads without errors
- [ ] Header and footer are present
- [ ] Search functionality works
- [ ] Responsive design works on mobile/tablet/desktop
- [ ] Dark theme is consistent
- [ ] Hover effects use custom orange color
- [ ] Typography follows Roboto font family

### 4. Git Workflow
```bash
# Create feature branch
git checkout -b feature/new-page-name

# Make changes and commit
git add .
git commit -m "Add new page: [page description]"

# Push to remote (when ready)
git push origin feature/new-page-name
```

## Available Hooks

### useSearchContext()
```tsx
const {
  searchTerm,           // Current search term
  searchResults,        // Array of search results
  isLoadingSearchResults, // Loading state
  handleSearchChange,   // Function to update search
  // ... other search functions
} = useSearchContext();
```

### useTooltip()
```tsx
const {
  activeTooltip,        // Current tooltip data
  triggerType,          // 'hover' | 'click'
  showTooltip,          // Function to show tooltip
  hideTooltip,          // Function to hide tooltip
} = useTooltip();
```

## Database Operations

### Supabase Integration
```tsx
import { supabase } from '@/lib/supabase/client';

// Fetch tools
const { data: tools, error } = await supabase
  .from('tools')
  .select('*')
  .eq('published', true);

// Fetch categories with tools
const { data: categories } = await supabase
  .from('categories')
  .select(`
    *,
    tools:tools(*)
  `);
```

### Background Jobs
```tsx
// Create a new job
const response = await fetch('/api/automation/jobs', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    type: 'tool-submission',
    data: { url: 'https://example.com' }
  })
});

// Check job status
const job = await fetch(`/api/automation/jobs/${jobId}`);
```

## Component Library

### Available Components
- `Header` - Automatic in layout
- `Footer` - Automatic in layout
- `CategoryGrid` - For displaying AI tool categories
- `CategoryCard` - Individual category cards
- `SearchBarHeader` - Search input component
- `Tooltip` - Dynamic tooltips
- `Button` - Styled button component
- `Card` - Base card component
- `Icon` - Lucide icon wrapper
- `Tag` - Badge/label component

### Import Paths
```tsx
// Layout components
import { Header } from '@/components/layout/Header';

// Feature components
import { CategoryGrid } from '@/components/features/CategoryGrid';

// UI components
import { Button } from '@/components/ui/Button';

// Hooks
import { useSearchContext } from '@/providers/SearchProvider';
import { useTooltip } from '@/hooks/useTooltip';

// Constants and types
import { AI_CATEGORIES } from '@/lib/constants';
import type { AITool, AICategory } from '@/lib/types';
```

## Troubleshooting

### Common Issues

1. **TypeScript Errors**
   - Check import paths use `@/` alias
   - Ensure proper type definitions
   - Verify component props match interfaces

2. **Styling Issues**
   - Use established color classes
   - Check responsive breakpoints
   - Verify container width usage

3. **Search Not Working**
   - Ensure `useSearchContext()` is used correctly
   - Check if component is wrapped in SearchProvider

4. **Layout Issues**
   - Verify page structure follows template
   - Check container width and padding
   - Test responsive behavior

5. **Hydration Errors**
   - Avoid using `Math.random()` or `Date.now()` in render
   - Use static values or `useEffect` for dynamic content
   - Ensure server and client render the same HTML
   - Example: Use predefined arrays instead of random values

### Getting Help

1. **Check Documentation**
   - `docs/UI-Design-System.md` - Complete design system
   - `docs/Component-Usage-Guide.md` - Component details
   - `docs/Project-Structure.md` - Architecture overview

2. **Code Examples**
   - `src/app/page.tsx` - Home page implementation
   - `docs/Page-Template.tsx` - New page template
   - Existing components for patterns

3. **Development Tools**
   - Browser DevTools for debugging
   - React DevTools for component inspection
   - TypeScript compiler for type checking

## Performance Considerations

### Best Practices
- Use `'use client'` only when necessary
- Implement proper loading states
- Optimize images with Next.js Image component
- Minimize bundle size with tree shaking

### Monitoring
- Check Lighthouse scores
- Monitor Core Web Vitals
- Test on different devices and connections

## Deployment

### Build Process
```bash
# Test build locally
npm run build

# Start production server
npm start
```

### Pre-deployment Checklist
- [ ] All TypeScript errors resolved
- [ ] Build completes successfully
- [ ] All pages load correctly
- [ ] Responsive design tested
- [ ] Performance metrics acceptable
- [ ] Accessibility standards met

This guide provides everything needed to efficiently develop new pages while maintaining consistency with the established design system and architecture.
