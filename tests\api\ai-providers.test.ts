/**
 * AI Provider Integration Tests
 * 
 * Tests the dual AI provider system integration:
 * - OpenAI API connectivity and functionality
 * - OpenRouter API connectivity and functionality
 * - Model selection logic
 * - Fallback mechanisms
 * - Error handling and recovery
 * - Content generation pipeline
 */

interface AIProviderTestResult {
  provider: string;
  testName: string;
  success: boolean;
  duration: number;
  error?: string;
  data?: any;
}

interface AIProviderSuite {
  providerName: string;
  results: AIProviderTestResult[];
  totalTests: number;
  passedTests: number;
  failedTests: number;
  totalDuration: number;
}

class AIProviderTester {
  private baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  private adminApiKey = process.env.ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';
  private suites: AIProviderSuite[] = [];

  async runAIProviderTests(): Promise<void> {
    console.log('🤖 Enhanced AI System - AI Provider Integration Tests');
    console.log('====================================================\n');

    await this.testAISystemHealth();
    await this.testOpenAIProvider();
    await this.testOpenRouterProvider();
    await this.testModelSelection();
    await this.testFallbackMechanisms();
    await this.testContentGeneration();
    await this.testErrorHandling();

    this.generateAIProviderReport();
  }

  private async testAISystemHealth(): Promise<void> {
    console.log('🏥 Testing AI System Health...');

    const suite: AIProviderSuite = {
      providerName: 'AI System Health',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    // Test 1: Overall health check
    await this.runAITest(suite, 'Overall Health Check', async () => {
      try {
        const { performHealthCheck } = await import('../../src/lib/ai');
        const health = await performHealthCheck();
        
        return {
          status: health.status,
          providers: health.providers,
          configurationValid: health.configuration?.valid || false,
          issues: health.configuration?.issues || []
        };
      } catch (error: any) {
        throw new Error(`Health check failed: ${error.message}`);
      }
    });

    // Test 2: Configuration validation
    await this.runAITest(suite, 'Configuration Validation', async () => {
      try {
        const { AIUtils } = await import('../../src/lib/ai');
        const validation = AIUtils.validateConfiguration();
        
        return {
          valid: validation.valid,
          issues: validation.issues || [],
          hasOpenAI: !!process.env.OPENAI_API_KEY,
          hasOpenRouter: !!process.env.OPENROUTER_API_KEY
        };
      } catch (error: any) {
        throw new Error(`Configuration validation failed: ${error.message}`);
      }
    });

    // Test 3: AI API endpoint
    await this.runAITest(suite, 'AI API Endpoint', async () => {
      const response = await fetch(`${this.baseUrl}/api/ai/test?type=health`, {
        method: 'GET',
        headers: {
          'x-api-key': this.adminApiKey
        }
      });

      if (!response.ok) {
        throw new Error(`AI API endpoint failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return {
        hasAIEndpoint: true,
        success: data.success,
        results: data.results || {}
      };
    });

    this.suites.push(suite);
    console.log(`✅ AI System Health: ${suite.passedTests}/${suite.totalTests} tests passed\n`);
  }

  private async testOpenAIProvider(): Promise<void> {
    console.log('🔵 Testing OpenAI Provider...');

    const suite: AIProviderSuite = {
      providerName: 'OpenAI Provider',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    // Test 1: OpenAI connectivity
    await this.runAITest(suite, 'OpenAI Connectivity', async () => {
      try {
        const { OpenAIClient } = await import('../../src/lib/ai/providers/openai-client');
        const client = new OpenAIClient();
        
        // Test basic connectivity (this might fail if no API key)
        const isConfigured = client.isConfigured();
        
        return {
          configured: isConfigured,
          hasAPIKey: !!process.env.OPENAI_API_KEY,
          clientCreated: true
        };
      } catch (error: any) {
        throw new Error(`OpenAI connectivity test failed: ${error.message}`);
      }
    });

    // Test 2: OpenAI model selection
    await this.runAITest(suite, 'OpenAI Model Selection', async () => {
      try {
        const { ModelSelector } = await import('../../src/lib/ai/model-selector');
        
        const criteria = {
          contentSize: 5000,
          complexity: 'simple' as const,
          priority: 'speed' as const,
          features: []
        };

        const selected = ModelSelector.selectOptimalModel(criteria);
        
        return {
          selectedProvider: selected.provider,
          selectedModel: selected.model,
          reasoning: selected.reasoning,
          isOpenAI: selected.provider === 'openai'
        };
      } catch (error: any) {
        throw new Error(`OpenAI model selection failed: ${error.message}`);
      }
    });

    // Test 3: OpenAI API test endpoint
    await this.runAITest(suite, 'OpenAI API Test', async () => {
      const response = await fetch(`${this.baseUrl}/api/ai/test?type=openai`, {
        method: 'GET',
        headers: {
          'x-api-key': this.adminApiKey
        }
      });

      if (!response.ok) {
        throw new Error(`OpenAI API test failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return {
        hasOpenAITest: true,
        success: data.success,
        results: data.results || {}
      };
    });

    this.suites.push(suite);
    console.log(`✅ OpenAI Provider: ${suite.passedTests}/${suite.totalTests} tests passed\n`);
  }

  private async testOpenRouterProvider(): Promise<void> {
    console.log('🟠 Testing OpenRouter Provider...');

    const suite: AIProviderSuite = {
      providerName: 'OpenRouter Provider',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    // Test 1: OpenRouter connectivity
    await this.runAITest(suite, 'OpenRouter Connectivity', async () => {
      try {
        const { OpenRouterClient } = await import('../../src/lib/ai/providers/openrouter-client');
        const client = new OpenRouterClient();
        
        const isConfigured = client.isConfigured();
        
        return {
          configured: isConfigured,
          hasAPIKey: !!process.env.OPENROUTER_API_KEY,
          clientCreated: true
        };
      } catch (error: any) {
        throw new Error(`OpenRouter connectivity test failed: ${error.message}`);
      }
    });

    // Test 2: OpenRouter model selection
    await this.runAITest(suite, 'OpenRouter Model Selection', async () => {
      try {
        const { ModelSelector } = await import('../../src/lib/ai/model-selector');
        
        const criteria = {
          contentSize: 200000, // Large content to prefer OpenRouter
          complexity: 'complex' as const,
          priority: 'quality' as const,
          features: ['multimodal']
        };

        const selected = ModelSelector.selectOptimalModel(criteria);
        
        return {
          selectedProvider: selected.provider,
          selectedModel: selected.model,
          reasoning: selected.reasoning,
          isOpenRouter: selected.provider === 'openrouter'
        };
      } catch (error: any) {
        throw new Error(`OpenRouter model selection failed: ${error.message}`);
      }
    });

    // Test 3: OpenRouter API test endpoint
    await this.runAITest(suite, 'OpenRouter API Test', async () => {
      const response = await fetch(`${this.baseUrl}/api/ai/test?type=openrouter`, {
        method: 'GET',
        headers: {
          'x-api-key': this.adminApiKey
        }
      });

      if (!response.ok) {
        throw new Error(`OpenRouter API test failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return {
        hasOpenRouterTest: true,
        success: data.success,
        results: data.results || {}
      };
    });

    this.suites.push(suite);
    console.log(`✅ OpenRouter Provider: ${suite.passedTests}/${suite.totalTests} tests passed\n`);
  }

  private async testModelSelection(): Promise<void> {
    console.log('🎯 Testing Model Selection Logic...');

    const suite: AIProviderSuite = {
      providerName: 'Model Selection',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    // Test 1: Speed-optimized selection
    await this.runAITest(suite, 'Speed-Optimized Selection', async () => {
      try {
        const { ModelSelector } = await import('../../src/lib/ai/model-selector');
        
        const criteria = {
          contentSize: 5000,
          complexity: 'simple' as const,
          priority: 'speed' as const,
          features: []
        };

        const selected = ModelSelector.selectOptimalModel(criteria);
        
        return {
          provider: selected.provider,
          model: selected.model,
          reasoning: selected.reasoning,
          optimizedForSpeed: selected.reasoning.includes('speed') || selected.reasoning.includes('fast')
        };
      } catch (error: any) {
        throw new Error(`Speed-optimized selection failed: ${error.message}`);
      }
    });

    // Test 2: Quality-optimized selection
    await this.runAITest(suite, 'Quality-Optimized Selection', async () => {
      try {
        const { ModelSelector } = await import('../../src/lib/ai/model-selector');
        
        const criteria = {
          contentSize: 50000,
          complexity: 'complex' as const,
          priority: 'quality' as const,
          features: ['multimodal']
        };

        const selected = ModelSelector.selectOptimalModel(criteria);
        
        return {
          provider: selected.provider,
          model: selected.model,
          reasoning: selected.reasoning,
          optimizedForQuality: selected.reasoning.includes('quality') || selected.reasoning.includes('advanced')
        };
      } catch (error: any) {
        throw new Error(`Quality-optimized selection failed: ${error.message}`);
      }
    });

    // Test 3: Cost-optimized selection
    await this.runAITest(suite, 'Cost-Optimized Selection', async () => {
      try {
        const { ModelSelector } = await import('../../src/lib/ai/model-selector');
        
        const criteria = {
          contentSize: 10000,
          complexity: 'medium' as const,
          priority: 'cost' as const,
          scrapingCost: 5,
          features: []
        };

        const selected = ModelSelector.selectOptimalModel(criteria);
        
        return {
          provider: selected.provider,
          model: selected.model,
          reasoning: selected.reasoning,
          optimizedForCost: selected.reasoning.includes('cost') || selected.reasoning.includes('efficient')
        };
      } catch (error: any) {
        throw new Error(`Cost-optimized selection failed: ${error.message}`);
      }
    });

    this.suites.push(suite);
    console.log(`✅ Model Selection: ${suite.passedTests}/${suite.totalTests} tests passed\n`);
  }

  private async testFallbackMechanisms(): Promise<void> {
    console.log('🔄 Testing Fallback Mechanisms...');

    const suite: AIProviderSuite = {
      providerName: 'Fallback Mechanisms',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    // Test 1: Fallback model selection
    await this.runAITest(suite, 'Fallback Model Selection', async () => {
      try {
        const { ModelSelector } = await import('../../src/lib/ai/model-selector');
        
        const primaryModel = {
          provider: 'openai' as const,
          model: 'gpt-4o-2024-11-20',
          maxTokens: 128000,
          reasoning: 'Primary model'
        };

        const fallback = ModelSelector.getFallbackModel(primaryModel, 'Simulated failure');
        
        return {
          fallbackProvider: fallback.provider,
          fallbackModel: fallback.model,
          reasoning: fallback.reasoning,
          isDifferentProvider: fallback.provider !== primaryModel.provider
        };
      } catch (error: any) {
        throw new Error(`Fallback model selection failed: ${error.message}`);
      }
    });

    // Test 2: API fallback test
    await this.runAITest(suite, 'API Fallback Test', async () => {
      const response = await fetch(`${this.baseUrl}/api/ai/test?type=fallback`, {
        method: 'GET',
        headers: {
          'x-api-key': this.adminApiKey
        }
      });

      if (!response.ok) {
        throw new Error(`API fallback test failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return {
        hasFallbackTest: true,
        success: data.success,
        results: data.results || {}
      };
    });

    this.suites.push(suite);
    console.log(`✅ Fallback Mechanisms: ${suite.passedTests}/${suite.totalTests} tests passed\n`);
  }

  private async testContentGeneration(): Promise<void> {
    console.log('📝 Testing Content Generation Pipeline...');

    const suite: AIProviderSuite = {
      providerName: 'Content Generation',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    // Test 1: Content generation API
    await this.runAITest(suite, 'Content Generation API', async () => {
      const response = await fetch(`${this.baseUrl}/api/generate-content`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.adminApiKey
        },
        body: JSON.stringify({
          url: 'https://httpbin.org/html',
          scrapedData: {
            content: 'Test content for AI generation',
            title: 'Test Tool',
            description: 'A test tool for validation'
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Content generation API failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return {
        hasContentGenerationAPI: true,
        success: data.success,
        jobId: data.data?.jobId
      };
    });

    this.suites.push(suite);
    console.log(`✅ Content Generation: ${suite.passedTests}/${suite.totalTests} tests passed\n`);
  }

  private async testErrorHandling(): Promise<void> {
    console.log('🚨 Testing AI Error Handling...');

    const suite: AIProviderSuite = {
      providerName: 'Error Handling',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    // Test 1: Error handling utilities
    await this.runAITest(suite, 'Error Handling Utilities', async () => {
      try {
        const { AIErrorHandler } = await import('../../src/lib/ai/error-handler');
        
        const testError = { code: 'rate_limit_exceeded', message: 'Rate limit exceeded' };
        const result = await AIErrorHandler.handleAIError(testError, {
          provider: 'openai',
          attempt: 1,
          maxRetries: 3
        });

        return {
          hasErrorHandler: true,
          retryable: result.retryable,
          suggestion: result.suggestion,
          handledError: true
        };
      } catch (error: any) {
        throw new Error(`Error handling test failed: ${error.message}`);
      }
    });

    this.suites.push(suite);
    console.log(`✅ Error Handling: ${suite.passedTests}/${suite.totalTests} tests passed\n`);
  }

  private async runAITest(
    suite: AIProviderSuite,
    testName: string,
    testFunction: () => Promise<any>
  ): Promise<void> {
    const startTime = Date.now();
    suite.totalTests++;

    try {
      const result = await testFunction();
      const duration = Date.now() - startTime;
      
      suite.results.push({
        provider: suite.providerName,
        testName,
        success: true,
        duration,
        data: result
      });
      
      suite.passedTests++;
      suite.totalDuration += duration;
      
      console.log(`  ✅ ${testName} (${duration}ms)`);
    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      suite.results.push({
        provider: suite.providerName,
        testName,
        success: false,
        duration,
        error: error.message
      });
      
      suite.failedTests++;
      suite.totalDuration += duration;
      
      console.log(`  ❌ ${testName} (${duration}ms): ${error.message}`);
    }
  }

  private generateAIProviderReport(): void {
    console.log('\n📊 AI PROVIDER INTEGRATION TEST REPORT');
    console.log('==================================================');

    let totalTests = 0;
    let totalPassed = 0;
    let totalFailed = 0;
    let totalDuration = 0;

    this.suites.forEach(suite => {
      totalTests += suite.totalTests;
      totalPassed += suite.passedTests;
      totalFailed += suite.failedTests;
      totalDuration += suite.totalDuration;

      console.log(`\n📋 ${suite.providerName}:`);
      console.log(`   Tests: ${suite.totalTests} | Passed: ${suite.passedTests} | Failed: ${suite.failedTests}`);
      console.log(`   Duration: ${suite.totalDuration}ms`);
      
      if (suite.failedTests > 0) {
        const failedTests = suite.results.filter(r => !r.success).map(r => r.testName);
        console.log(`   Failed: ${failedTests.join(', ')}`);
      }
    });

    console.log('\n🎯 OVERALL AI PROVIDER RESULTS:');
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${totalPassed} (${Math.round((totalPassed / totalTests) * 100)}%)`);
    console.log(`   Failed: ${totalFailed} (${Math.round((totalFailed / totalTests) * 100)}%)`);
    console.log(`   Total Duration: ${totalDuration}ms`);
    
    const status = totalFailed === 0 ? '✅ ALL TESTS PASSED' : 
                  totalPassed / totalTests >= 0.8 ? '⚠️ MOSTLY PASSED' : '❌ AI PROVIDER ISSUES';
    console.log(`\n🏆 Status: ${status}`);
    console.log('==================================================\n');
  }
}

// Export for use in other test files
export { AIProviderTester, AIProviderTestResult, AIProviderSuite };

// Main execution when run directly
if (require.main === module) {
  const tester = new AIProviderTester();
  tester.runAIProviderTests().catch(console.error);
}
