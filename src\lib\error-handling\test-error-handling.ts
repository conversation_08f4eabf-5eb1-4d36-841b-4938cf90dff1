/**
 * Test script for the Enhanced Error Handling System
 * Tests various error scenarios and recovery mechanisms
 */

import { 
  errorManager, 
  ErrorHandlingUtils, 
  CommonErrorHandlers,
  ErrorCategory,
  ErrorSeverity,
  ERROR_TYPES
} from './index';

/**
 * Test the error handling system with various scenarios
 */
export async function testErrorHandlingSystem(): Promise<void> {
  console.log('🧪 Starting Enhanced Error Handling System Tests...\n');

  try {
    // Test 1: Network Error Handling
    await testNetworkErrorHandling();

    // Test 2: AI Provider Error Handling
    await testAIProviderErrorHandling();

    // Test 3: Database Error Handling
    await testDatabaseErrorHandling();

    // Test 4: Validation Error Handling
    await testValidationErrorHandling();

    // Test 5: Health Check System
    await testHealthCheckSystem();

    // Test 6: Error Monitoring and Alerting
    await testErrorMonitoring();

    // Test 7: Recovery Strategies
    await testRecoveryStrategies();

    console.log('\n✅ All error handling tests completed successfully!');

  } catch (error) {
    console.error('\n❌ Error handling tests failed:', error);
    throw error;
  }
}

/**
 * Test network error handling
 */
async function testNetworkErrorHandling(): Promise<void> {
  console.log('1️⃣ Testing Network Error Handling...');

  // Simulate network timeout error
  const networkError = new Error('Request timeout');
  (networkError as any).code = 'ETIMEDOUT';

  const context = ErrorHandlingUtils.createErrorContext({
    operation: 'network_request',
    metadata: { url: 'https://example.com' }
  });

  const result = await errorManager.handleError(networkError, context);
  
  console.log(`   Network error handled: ${result.success ? 'SUCCESS' : 'FAILED'}`);
  console.log(`   Strategy used: ${result.strategy}`);
  console.log(`   Retryable: ${!result.requiresManualIntervention}\n`);
}

/**
 * Test AI provider error handling
 */
async function testAIProviderErrorHandling(): Promise<void> {
  console.log('2️⃣ Testing AI Provider Error Handling...');

  // Test OpenAI rate limit error
  const openaiError = new Error('Rate limit exceeded');
  (openaiError as any).code = 'rate_limit_exceeded';

  const openaiContext = ErrorHandlingUtils.createErrorContext({
    operation: 'ai_generation',
    provider: 'openai',
    metadata: { model: 'gpt-4o' }
  });

  const openaiResult = await errorManager.handleError(openaiError, openaiContext);
  console.log(`   OpenAI error handled: ${openaiResult.success ? 'SUCCESS' : 'FAILED'}`);
  console.log(`   Strategy: ${openaiResult.strategy}`);

  // Test OpenRouter insufficient credits error
  const openrouterError = new Error('Insufficient credits');
  (openrouterError as any).status = 402;

  const openrouterContext = ErrorHandlingUtils.createErrorContext({
    operation: 'ai_generation',
    provider: 'openrouter',
    metadata: { model: 'google/gemini-2.5-pro-preview' }
  });

  const openrouterResult = await errorManager.handleError(openrouterError, openrouterContext);
  console.log(`   OpenRouter error handled: ${openrouterResult.success ? 'SUCCESS' : 'FAILED'}`);
  console.log(`   Strategy: ${openrouterResult.strategy}\n`);
}

/**
 * Test database error handling
 */
async function testDatabaseErrorHandling(): Promise<void> {
  console.log('3️⃣ Testing Database Error Handling...');

  const dbError = new Error('Connection lost');
  (dbError as any).code = 'PGRST';

  const context = ErrorHandlingUtils.createErrorContext({
    operation: 'database_query',
    metadata: { table: 'tools', query: 'SELECT * FROM tools' }
  });

  const result = await errorManager.handleError(dbError, context);
  
  console.log(`   Database error handled: ${result.success ? 'SUCCESS' : 'FAILED'}`);
  console.log(`   Strategy used: ${result.strategy}`);
  console.log(`   Manual intervention required: ${result.requiresManualIntervention}\n`);
}

/**
 * Test validation error handling
 */
async function testValidationErrorHandling(): Promise<void> {
  console.log('4️⃣ Testing Validation Error Handling...');

  const validationError = new Error('Schema validation failed');
  (validationError as any).type = 'validation_error';

  const context = ErrorHandlingUtils.createErrorContext({
    operation: 'content_validation',
    metadata: { contentType: 'ai_generated', schema: 'tool_content' }
  });

  const result = await errorManager.handleError(validationError, context);
  
  console.log(`   Validation error handled: ${result.success ? 'SUCCESS' : 'FAILED'}`);
  console.log(`   Strategy used: ${result.strategy}`);
  console.log(`   Auto-recoverable: ${!result.requiresManualIntervention}\n`);
}

/**
 * Test health check system
 */
async function testHealthCheckSystem(): Promise<void> {
  console.log('5️⃣ Testing Health Check System...');

  try {
    const healthStatus = await errorManager.getSystemHealth();
    
    console.log(`   Overall health: ${healthStatus.overall}`);
    console.log(`   Components checked: ${healthStatus.summary.total}`);
    console.log(`   Healthy components: ${healthStatus.summary.healthy}`);
    console.log(`   Degraded components: ${healthStatus.summary.degraded}`);
    console.log(`   Unhealthy components: ${healthStatus.summary.unhealthy}`);
    
    if (healthStatus.criticalIssues.length > 0) {
      console.log(`   Critical issues: ${healthStatus.criticalIssues.join(', ')}`);
    }
    
    console.log('');
  } catch (error) {
    console.log(`   Health check failed: ${error}\n`);
  }
}

/**
 * Test error monitoring and alerting
 */
async function testErrorMonitoring(): Promise<void> {
  console.log('6️⃣ Testing Error Monitoring and Alerting...');

  // Generate multiple errors to test alerting thresholds
  for (let i = 0; i < 3; i++) {
    const testError = new Error(`Test error ${i + 1}`);
    (testError as any).code = 'TEST_ERROR';

    const context = ErrorHandlingUtils.createErrorContext({
      operation: 'test_operation',
      metadata: { iteration: i + 1 }
    });

    await errorManager.handleError(testError, context);
  }

  // Get error metrics
  const metrics = errorManager.getErrorMetrics();
  console.log(`   Error types tracked: ${metrics.size}`);
  
  // Check for test error metrics
  const testErrorMetrics = metrics.get('system_TEST_ERROR');
  if (testErrorMetrics) {
    console.log(`   Test errors count: ${testErrorMetrics.count}`);
    console.log(`   Test error frequency: ${testErrorMetrics.frequency}/hour`);
  }

  console.log('');
}

/**
 * Test recovery strategies
 */
async function testRecoveryStrategies(): Promise<void> {
  console.log('7️⃣ Testing Recovery Strategies...');

  // Test with retry operation
  let attemptCount = 0;
  const retryOperation = async () => {
    attemptCount++;
    if (attemptCount < 3) {
      throw new Error('Simulated failure');
    }
    return { success: true, data: 'Recovery successful' };
  };

  try {
    const result = await ErrorHandlingUtils.withErrorHandling(
      retryOperation,
      ErrorHandlingUtils.createErrorContext({
        operation: 'test_retry',
        metadata: { maxRetries: 3 }
      })
    );

    console.log(`   Retry strategy test: SUCCESS`);
    console.log(`   Attempts made: ${attemptCount}`);
    console.log(`   Result: ${JSON.stringify(result)}`);
  } catch (error) {
    console.log(`   Retry strategy test: FAILED - ${error}`);
  }

  console.log('');
}

/**
 * Test specific error scenarios
 */
export async function testSpecificErrorScenarios(): Promise<void> {
  console.log('🎯 Testing Specific Error Scenarios...\n');

  // Test AI provider fallback
  await testAIProviderFallback();

  // Test content splitting
  await testContentSplitting();

  // Test database reconnection
  await testDatabaseReconnection();
}

/**
 * Test AI provider fallback scenario
 */
async function testAIProviderFallback(): Promise<void> {
  console.log('🔄 Testing AI Provider Fallback...');

  const primaryOperation = async () => {
    throw new Error('OpenAI rate limit exceeded');
  };

  const fallbackOperation = async () => {
    return { content: 'Generated using fallback provider', provider: 'openrouter' };
  };

  try {
    const result = await CommonErrorHandlers.aiProvider.handleWithFallback(
      primaryOperation,
      fallbackOperation,
      ErrorHandlingUtils.createErrorContext({
        operation: 'ai_generation',
        provider: 'openai'
      })
    );

    console.log(`   Fallback test: SUCCESS`);
    console.log(`   Result: ${JSON.stringify(result)}\n`);
  } catch (error) {
    console.log(`   Fallback test: FAILED - ${error}\n`);
  }
}

/**
 * Test content splitting scenario
 */
async function testContentSplitting(): Promise<void> {
  console.log('✂️ Testing Content Splitting...');

  const largeContent = 'A'.repeat(10000); // Simulate large content
  
  const processChunk = async (chunk: string) => {
    return { processed: true, length: chunk.length };
  };

  const context = ErrorHandlingUtils.createErrorContext({
    operation: 'content_processing',
    metadata: {
      content: largeContent,
      maxTokens: 2000,
      contentLength: largeContent.length
    }
  });

  // Simulate context length exceeded error
  const contextError = new Error('Context length exceeded');
  (contextError as any).code = 'context_length_exceeded';

  const result = await errorManager.handleError(contextError, context);
  
  console.log(`   Content splitting test: ${result.success ? 'SUCCESS' : 'FAILED'}`);
  console.log(`   Strategy: ${result.strategy}`);
  if (result.chunksProcessed) {
    console.log(`   Chunks processed: ${result.chunksProcessed}`);
  }
  console.log('');
}

/**
 * Test database reconnection scenario
 */
async function testDatabaseReconnection(): Promise<void> {
  console.log('🔌 Testing Database Reconnection...');

  let connectionAttempts = 0;
  const databaseOperation = async () => {
    connectionAttempts++;
    if (connectionAttempts < 3) {
      const error = new Error('Database connection lost');
      (error as any).code = 'PGRST';
      throw error;
    }
    return { connected: true, attempt: connectionAttempts };
  };

  try {
    const result = await CommonErrorHandlers.database.handleWithReconnect(
      databaseOperation,
      ErrorHandlingUtils.createErrorContext({
        operation: 'database_query'
      })
    );

    console.log(`   Database reconnection test: SUCCESS`);
    console.log(`   Connection attempts: ${connectionAttempts}`);
    console.log(`   Result: ${JSON.stringify(result)}\n`);
  } catch (error) {
    console.log(`   Database reconnection test: FAILED - ${error}\n`);
  }
}

/**
 * Run all tests
 */
export async function runAllErrorHandlingTests(): Promise<void> {
  console.log('🚀 Running Complete Error Handling Test Suite...\n');
  
  try {
    await testErrorHandlingSystem();
    await testSpecificErrorScenarios();
    
    console.log('🎉 All error handling tests completed successfully!');
  } catch (error) {
    console.error('💥 Error handling tests failed:', error);
    process.exit(1);
  }
}

// Export for use in other test files
export {
  testNetworkErrorHandling,
  testAIProviderErrorHandling,
  testDatabaseErrorHandling,
  testValidationErrorHandling,
  testHealthCheckSystem,
  testErrorMonitoring,
  testRecoveryStrategies,
  testAIProviderFallback,
  testContentSplitting,
  testDatabaseReconnection
};
