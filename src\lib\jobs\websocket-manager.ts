import { EventEmitter } from 'events';
import { WebSocketJobUpdate } from './types';
import { getProgressTracker } from './progress-tracker';

/**
 * WebSocket Manager for Real-time Job Updates
 * 
 * Manages WebSocket connections and broadcasts job updates
 * to connected admin clients in real-time.
 */
export class WebSocketManager extends EventEmitter {
  private connections = new Map<string, WebSocketConnection>();
  private progressTracker;

  constructor() {
    super();
    this.progressTracker = getProgressTracker();
    this.setupProgressTrackerListener();
  }

  /**
   * Register a new WebSocket connection
   */
  registerConnection(connectionId: string, connection: WebSocketConnection): void {
    this.connections.set(connectionId, connection);
    
    // Send initial connection confirmation
    this.sendToConnection(connectionId, {
      type: 'connection_established',
      data: {
        connectionId,
        timestamp: new Date().toISOString(),
        message: 'WebSocket connection established for job monitoring',
      },
    });

    console.log(`🔌 WebSocket connection registered: ${connectionId}`);
  }

  /**
   * Unregister a WebSocket connection
   */
  unregisterConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      try {
        connection.close();
      } catch (error) {
        console.error(`Error closing WebSocket connection ${connectionId}:`, error);
      }
      this.connections.delete(connectionId);
      console.log(`🔌 WebSocket connection unregistered: ${connectionId}`);
    }
  }

  /**
   * Broadcast update to all connected clients
   */
  broadcast(update: WebSocketJobUpdate): void {
    const message = {
      type: 'job_update',
      data: update,
      timestamp: new Date().toISOString(),
    };

    let successCount = 0;
    let errorCount = 0;

    for (const connectionId of this.connections.keys()) {
      const connection = this.connections.get(connectionId)!;
      try {
        if (connection.readyState === 1) { // WebSocket.OPEN
          connection.send(JSON.stringify(message));
          successCount++;
        } else {
          // Remove stale connections
          this.unregisterConnection(connectionId);
          errorCount++;
        }
      } catch (error) {
        console.error(`Failed to send message to connection ${connectionId}:`, error);
        this.unregisterConnection(connectionId);
        errorCount++;
      }
    }

    if (successCount > 0) {
      console.log(`📡 Broadcasted job update to ${successCount} clients (${errorCount} errors)`);
    }
  }

  /**
   * Send update to specific connection
   */
  sendToConnection(connectionId: string, message: any): void {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      console.warn(`WebSocket connection not found: ${connectionId}`);
      return;
    }

    try {
      if (connection.readyState === 1) { // WebSocket.OPEN
        connection.send(JSON.stringify({
          ...message,
          timestamp: new Date().toISOString(),
        }));
      } else {
        this.unregisterConnection(connectionId);
      }
    } catch (error) {
      console.error(`Failed to send message to connection ${connectionId}:`, error);
      this.unregisterConnection(connectionId);
    }
  }

  /**
   * Subscribe a connection to specific job updates
   */
  subscribeToJob(connectionId: string, jobId: string): void {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      console.warn(`WebSocket connection not found: ${connectionId}`);
      return;
    }

    // Add job subscription to connection metadata
    if (!connection.subscribedJobs) {
      connection.subscribedJobs = new Set();
    }
    connection.subscribedJobs.add(jobId);

    // Send current job status if available
    this.sendJobStatus(connectionId, jobId);

    console.log(`📋 Connection ${connectionId} subscribed to job ${jobId}`);
  }

  /**
   * Unsubscribe a connection from job updates
   */
  unsubscribeFromJob(connectionId: string, jobId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection && connection.subscribedJobs) {
      connection.subscribedJobs.delete(jobId);
      console.log(`📋 Connection ${connectionId} unsubscribed from job ${jobId}`);
    }
  }

  /**
   * Get connection statistics
   */
  getStats(): {
    totalConnections: number;
    activeConnections: number;
    totalSubscriptions: number;
  } {
    let activeConnections = 0;
    let totalSubscriptions = 0;

    for (const connectionId of this.connections.keys()) {
      const connection = this.connections.get(connectionId)!;
      if (connection.readyState === 1) {
        activeConnections++;
        totalSubscriptions += connection.subscribedJobs?.size || 0;
      }
    }

    return {
      totalConnections: this.connections.size,
      activeConnections,
      totalSubscriptions,
    };
  }

  /**
   * Send current job status to a connection
   */
  private async sendJobStatus(connectionId: string, jobId: string): Promise<void> {
    try {
      const progress = this.progressTracker.getProgress(jobId);
      if (progress) {
        this.sendToConnection(connectionId, {
          type: 'job_status',
          data: {
            jobId,
            progress: progress.progress,
            progressDetails: progress.details,
            timestamp: new Date().toISOString(),
          },
        });
      }
    } catch (error) {
      console.error(`Failed to send job status for ${jobId}:`, error);
    }
  }

  /**
   * Setup listener for progress tracker events
   */
  private setupProgressTrackerListener(): void {
    this.progressTracker.on('websocket_broadcast', (update: WebSocketJobUpdate) => {
      this.broadcast(update);
    });
  }

  /**
   * Broadcast to connections subscribed to a specific job
   */
  broadcastToJobSubscribers(jobId: string, update: WebSocketJobUpdate): void {
    let successCount = 0;

    for (const connectionId of this.connections.keys()) {
      const connection = this.connections.get(connectionId)!;
      if (connection.subscribedJobs?.has(jobId)) {
        try {
          if (connection.readyState === 1) {
            connection.send(JSON.stringify({
              type: 'job_update',
              data: update,
              timestamp: new Date().toISOString(),
            }));
            successCount++;
          } else {
            this.unregisterConnection(connectionId);
          }
        } catch (error) {
          console.error(`Failed to send job update to connection ${connectionId}:`, error);
          this.unregisterConnection(connectionId);
        }
      }
    }

    if (successCount > 0) {
      console.log(`📡 Broadcasted job ${jobId} update to ${successCount} subscribers`);
    }
  }

  /**
   * Cleanup all connections
   */
  cleanup(): void {
    for (const connectionId of this.connections.keys()) {
      this.unregisterConnection(connectionId);
    }
    this.connections.clear();
    this.removeAllListeners();
  }
}

/**
 * WebSocket Connection Interface
 */
export interface WebSocketConnection {
  send(data: string): void;
  close(): void;
  readyState: number;
  subscribedJobs?: Set<string>;
  metadata?: Record<string, unknown>;
}

// Singleton instance
let webSocketManagerInstance: WebSocketManager | null = null;

export function getWebSocketManager(): WebSocketManager {
  if (!webSocketManagerInstance) {
    webSocketManagerInstance = new WebSocketManager();
  }
  return webSocketManagerInstance;
}
