'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { JobStatus, JobType } from '@/lib/jobs/types';

interface JobFilters {
  status?: JobStatus;
  type?: JobType;
  dateRange?: {
    start: Date;
    end: Date;
  };
  toolId?: string;
  search?: string;
}

interface JobFiltersPanelProps {
  filters: JobFilters;
  onFilterChange: (filters: Partial<JobFilters>) => void;
  onClearFilters: () => void;
  jobTypes: JobType[];
  jobStatuses: JobStatus[];
}

/**
 * Job Filters Panel Component
 * 
 * Provides advanced filtering options for the job list.
 * Features:
 * - Status and type filtering
 * - Date range selection
 * - Tool ID search
 * - Text search across job data
 * - Quick filter presets
 * - Clear all filters functionality
 */
export function JobFiltersPanel({
  filters,
  onFilterChange,
  onClearFilters,
  jobTypes,
  jobStatuses
}: JobFiltersPanelProps): React.JSX.Element {
  const [localFilters, setLocalFilters] = useState<JobFilters>(filters);

  // Apply filters
  const handleApplyFilters = () => {
    onFilterChange(localFilters);
  };

  // Reset local filters
  const handleResetFilters = () => {
    setLocalFilters({});
    onClearFilters();
  };

  // Update local filter state
  const updateLocalFilter = (key: keyof JobFilters, value: any) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Quick filter presets
  const quickFilters = [
    {
      label: 'Active Jobs',
      icon: '⚡',
      filters: { status: JobStatus.PROCESSING }
    },
    {
      label: 'Failed Jobs',
      icon: '❌',
      filters: { status: JobStatus.FAILED }
    },
    {
      label: 'Queued Jobs',
      icon: '⏳',
      filters: { status: JobStatus.PENDING }
    },
    {
      label: 'Content Generation',
      icon: '🤖',
      filters: { type: JobType.CONTENT_GENERATION }
    },
    {
      label: 'Web Scraping',
      icon: '🕷️',
      filters: { type: JobType.WEB_SCRAPING }
    },
    {
      label: 'Today\'s Jobs',
      icon: '📅',
      filters: {
        dateRange: {
          start: new Date(new Date().setHours(0, 0, 0, 0)),
          end: new Date(new Date().setHours(23, 59, 59, 999))
        }
      }
    }
  ];

  // Format job type for display
  const formatJobType = (type: JobType): string => {
    return type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  // Format job status for display
  const formatJobStatus = (status: JobStatus): string => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  // Check if filters have been modified
  const hasChanges = JSON.stringify(localFilters) !== JSON.stringify(filters);
  const hasActiveFilters = Object.keys(filters).some(key => 
    filters[key as keyof JobFilters] !== undefined && 
    filters[key as keyof JobFilters] !== ''
  );

  return (
    <Card className="bg-zinc-800 border border-zinc-700 p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-white">Filter Jobs</h3>
          <div className="flex items-center space-x-2">
            {hasActiveFilters && (
              <span className="text-sm text-orange-400 bg-orange-500/10 px-2 py-1 rounded">
                {Object.keys(filters).length} active filter{Object.keys(filters).length !== 1 ? 's' : ''}
              </span>
            )}
          </div>
        </div>

        {/* Quick Filters */}
        <div>
          <label className="text-sm font-medium text-gray-400 mb-3 block">Quick Filters</label>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
            {quickFilters.map((quickFilter, index) => (
              <button
                key={index}
                onClick={() => {
                  setLocalFilters(quickFilter.filters);
                  onFilterChange(quickFilter.filters);
                }}
                className="flex items-center space-x-2 px-3 py-2 text-sm bg-zinc-700 hover:bg-zinc-600 rounded-lg transition-colors text-left"
              >
                <span>{quickFilter.icon}</span>
                <span className="text-white truncate">{quickFilter.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Filter Controls */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Status Filter */}
          <div>
            <label className="text-sm font-medium text-gray-400 mb-2 block">Status</label>
            <select
              value={localFilters.status || ''}
              onChange={(e) => updateLocalFilter('status', e.target.value || undefined)}
              className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="">All Statuses</option>
              {jobStatuses.map(status => (
                <option key={status} value={status}>
                  {formatJobStatus(status)}
                </option>
              ))}
            </select>
          </div>

          {/* Type Filter */}
          <div>
            <label className="text-sm font-medium text-gray-400 mb-2 block">Type</label>
            <select
              value={localFilters.type || ''}
              onChange={(e) => updateLocalFilter('type', e.target.value || undefined)}
              className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="">All Types</option>
              {jobTypes.map(type => (
                <option key={type} value={type}>
                  {formatJobType(type)}
                </option>
              ))}
            </select>
          </div>

          {/* Tool ID Search */}
          <div>
            <label className="text-sm font-medium text-gray-400 mb-2 block">Tool ID</label>
            <input
              type="text"
              placeholder="Search by tool ID..."
              value={localFilters.toolId || ''}
              onChange={(e) => updateLocalFilter('toolId', e.target.value || undefined)}
              className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
          </div>

          {/* General Search */}
          <div>
            <label className="text-sm font-medium text-gray-400 mb-2 block">Search</label>
            <input
              type="text"
              placeholder="Search jobs..."
              value={localFilters.search || ''}
              onChange={(e) => updateLocalFilter('search', e.target.value || undefined)}
              className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Date Range Filter */}
        <div>
          <label className="text-sm font-medium text-gray-400 mb-2 block">Date Range</label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-xs text-gray-500 mb-1 block">From</label>
              <input
                type="datetime-local"
                value={localFilters.dateRange?.start ? 
                  new Date(localFilters.dateRange.start.getTime() - localFilters.dateRange.start.getTimezoneOffset() * 60000)
                    .toISOString().slice(0, 16) : ''
                }
                onChange={(e) => {
                  const startDate = e.target.value ? new Date(e.target.value) : undefined;
                  updateLocalFilter('dateRange', {
                    ...localFilters.dateRange,
                    start: startDate
                  });
                }}
                className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="text-xs text-gray-500 mb-1 block">To</label>
              <input
                type="datetime-local"
                value={localFilters.dateRange?.end ? 
                  new Date(localFilters.dateRange.end.getTime() - localFilters.dateRange.end.getTimezoneOffset() * 60000)
                    .toISOString().slice(0, 16) : ''
                }
                onChange={(e) => {
                  const endDate = e.target.value ? new Date(e.target.value) : undefined;
                  updateLocalFilter('dateRange', {
                    ...localFilters.dateRange,
                    end: endDate
                  });
                }}
                className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t border-zinc-700">
          <div className="flex items-center space-x-3">
            <Button
              variant="primary"
              onClick={handleApplyFilters}
              disabled={!hasChanges}
            >
              🔍 Apply Filters
            </Button>
            <Button
              variant="outline"
              onClick={handleResetFilters}
              disabled={!hasActiveFilters && !hasChanges}
            >
              🗑️ Clear All
            </Button>
          </div>
          
          <div className="text-sm text-gray-400">
            {hasChanges && (
              <span className="text-yellow-400">
                ⚠️ Unsaved changes
              </span>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
}

export default JobFiltersPanel;
