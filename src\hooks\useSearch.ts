'use client';

import { useState, useCallback, useEffect } from 'react';
import { AITool } from '@/lib/types';
import { apiClient } from '@/lib/api';

export function useSearch() {
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearchDropdownVisible, setIsSearchDropdownVisible] = useState(false);
  const [searchResults, setSearchResults] = useState<AITool[] | null>(null);
  const [isLoadingSearchResults, setIsLoadingSearchResults] = useState(false);

  const handleSearchFocus = useCallback(() => {
    setIsSearchDropdownVisible(true);
  }, []);

  const handleSearchBlur = useCallback(() => {
    // Delay hiding to allow clicking on dropdown items
    setTimeout(() => {
      setIsSearchDropdownVisible(false);
    }, 200);
  }, []);

  const handleSearchChange = useCallback(async (value: string) => {
    setSearchTerm(value);

    if (value.trim().length > 2) {
      setIsLoadingSearchResults(true);

      try {
        const results = await apiClient.searchTools(value, 20);
        setSearchResults(results);
      } catch (error) {
        console.error('Search failed:', error);
        setSearchResults([]);
      } finally {
        setIsLoadingSearchResults(false);
      }
    } else {
      setSearchResults(null);
      setIsLoadingSearchResults(false);
    }
  }, []);

  const handleTopSearchClick = useCallback((term: string) => {
    const cleanTerm = term.replace('#', '');
    setSearchTerm(cleanTerm);
    handleSearchChange(cleanTerm);
    setIsSearchDropdownVisible(false);
  }, [handleSearchChange]);

  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setSearchResults(null);
    setIsLoadingSearchResults(false);
  }, []);

  return {
    searchTerm,
    isSearchDropdownVisible,
    searchResults,
    isLoadingSearchResults,
    handleSearchFocus,
    handleSearchBlur,
    handleSearchChange,
    handleTopSearchClick,
    clearSearch,
  };
}
