import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { SearchProvider } from "@/providers/SearchProvider";
import { LayoutContent } from "@/components/layout/LayoutContent";
import { FloatingButtonsWrapper } from "@/components/layout/FloatingButtonsWrapper";

export const metadata: Metadata = {
  title: "AI-DUDE.COM - The Best AI Tools Directory",
  description: "Discover the best 10000+ AI tools for writing, image generation, coding, chatbots and more. Your ultimate AI tools directory.",
  keywords: "AI tools, artificial intelligence, AI directory, AI writing tools, AI image generators, AI chatbots, AI dev tools",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="min-h-screen w-full bg-zinc-900 text-white">
        <SearchProvider>
          <LayoutContent>
            {children}
          </LayoutContent>
          <FloatingButtonsWrapper />
        </SearchProvider>
      </body>
    </html>
  );
}
