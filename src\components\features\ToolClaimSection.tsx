'use client';

import React from 'react';
import { User, ExternalLink, Info, Mail } from 'lucide-react';
import { AITool } from '@/lib/types';

interface ToolClaimSectionProps {
  tool: AITool;
}

export function ToolClaimSection({ tool }: ToolClaimSectionProps) {
  // Only show if tool is not claimed and is claimable
  if (tool.isClaimed || !tool.claimInfo?.isClaimable) {
    return null;
  }

  const handleClaimClick = () => {
    if (tool.claimInfo?.claimUrl) {
      window.open(tool.claimInfo.claimUrl, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <section className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
      <div className="flex items-center gap-2 mb-4">
        <User size={20} className="text-orange-400" />
        <h3 className="text-lg font-bold text-white">Claim This Tool</h3>
      </div>
      
      <div className="space-y-4">
        
        {/* Claim Notice */}
        <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <Info size={16} className="text-orange-400 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-orange-200 text-sm leading-relaxed">
                Are you the creator or owner of <strong>{tool.name}</strong>? 
                Claim this tool to manage its information, respond to reviews, and connect with users.
              </p>
            </div>
          </div>
        </div>
        
        {/* Benefits of Claiming */}
        <div className="space-y-3">
          <h4 className="text-white font-medium">Benefits of claiming:</h4>
          <ul className="space-y-2 text-gray-300 text-sm">
            <li className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
              <span>Update tool information and descriptions</span>
            </li>
            <li className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
              <span>Add official screenshots and media</span>
            </li>
            <li className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
              <span>Respond to user reviews and feedback</span>
            </li>
            <li className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
              <span>Get verified status and blue checkmark</span>
            </li>
            <li className="flex items-start gap-2">
              <div className="w-1.5 h-1.5 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
              <span>Access analytics and user insights</span>
            </li>
          </ul>
        </div>
        
        {/* Claim Instructions */}
        {tool.claimInfo?.claimInstructions && (
          <div className="bg-zinc-700/50 rounded-lg p-4">
            <h4 className="text-white font-medium mb-2">How to claim:</h4>
            <p className="text-gray-300 text-sm leading-relaxed">
              {tool.claimInfo.claimInstructions}
            </p>
          </div>
        )}
        
        {/* Claim Button */}
        <div className="pt-4">
          <button
            onClick={handleClaimClick}
            className="w-full bg-orange-500 hover:bg-orange-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
            style={{
              backgroundColor: 'rgb(255, 150, 0)',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
            }}
          >
            {tool.claimInfo?.claimUrl ? (
              <>
                <ExternalLink size={18} />
                Claim {tool.name}
              </>
            ) : (
              <>
                <Mail size={18} />
                Contact to Claim
              </>
            )}
          </button>
        </div>
        
        {/* Additional Info */}
        <div className="text-center">
          <p className="text-gray-500 text-xs">
            Claiming is free and takes just a few minutes to verify ownership.
          </p>
        </div>
        
      </div>
    </section>
  );
}
