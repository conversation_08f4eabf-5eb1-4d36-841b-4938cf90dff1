import { Job, JobType, JobStatus, JobPriority, JobQueue, JobOptions } from './types';

class InMemoryJobQueue {
  private jobs: Map<string, Job> = new Map();
  private processing = false;
  private maxConcurrent: number;
  private currentlyProcessing = 0;

  constructor(maxConcurrent = 3) {
    this.maxConcurrent = maxConcurrent;
  }

  async add(type: JobType, data: any, options: JobOptions = {}): Promise<Job> {
    const job: Job = {
      id: this.generateId(),
      type,
      data,
      status: JobStatus.PENDING,
      priority: options.priority || JobPriority.NORMAL,
      attempts: 0,
      maxAttempts: options.maxAttempts || parseInt(process.env.JOB_RETRY_ATTEMPTS || '3'),
      createdAt: new Date(),
      updatedAt: new Date(),
      scheduledFor: options.scheduledFor,
    };

    this.jobs.set(job.id, job);

    // Start processing if not already running
    if (!this.processing) {
      this.startProcessing();
    }

    // Trigger immediate processing attempt
    setImmediate(() => this.process());

    return job;
  }

  async process(): Promise<void> {
    if (this.currentlyProcessing >= this.maxConcurrent) {
      return;
    }

    const pendingJobs = Array.from(this.jobs.values())
      .filter(job =>
        job.status === JobStatus.PENDING &&
        (!job.scheduledFor || job.scheduledFor <= new Date())
      )
      .sort((a, b) => b.priority - a.priority || a.createdAt.getTime() - b.createdAt.getTime());

    if (pendingJobs.length === 0) {
      return;
    }

    const job = pendingJobs[0];
    console.log(`🔄 Processing job ${job.id} (${job.type})`);
    this.currentlyProcessing++;

    try {
      await this.processJob(job);
    } finally {
      this.currentlyProcessing--;
    }
  }

  private async processJob(job: Job): Promise<void> {
    job.status = JobStatus.PROCESSING;
    job.updatedAt = new Date();
    job.attempts++;

    try {
      const handler = await this.getHandler(job.type);
      const result = await handler.handle(job);
      
      job.status = JobStatus.COMPLETED;
      job.result = result;
      job.completedAt = new Date();
    } catch (error) {
      job.error = error instanceof Error ? error.message : String(error);
      
      if (job.attempts < job.maxAttempts) {
        job.status = JobStatus.RETRYING;
        // Add exponential backoff delay
        const delay = Math.pow(2, job.attempts) * 1000;
        job.scheduledFor = new Date(Date.now() + delay);
      } else {
        job.status = JobStatus.FAILED;
      }
    }

    job.updatedAt = new Date();
    this.jobs.set(job.id, job);
  }

  private async getHandler(type: JobType) {
    const { getJobHandler } = await import('./handlers');
    return getJobHandler(type);
  }

  private startProcessing(): void {
    if (this.processing) return;

    this.processing = true;
    console.log('🚀 Starting job processing loop');

    const processLoop = async () => {
      while (this.processing) {
        try {
          await this.process();
          await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second interval
        } catch (error) {
          console.error('Job processing error:', error);
          await new Promise(resolve => setTimeout(resolve, 5000)); // 5 second delay on error
        }
      }
    };

    processLoop();
  }

  async getJob(id: string): Promise<Job | null> {
    return this.jobs.get(id) || null;
  }

  async getJobs(status?: JobStatus): Promise<Job[]> {
    const jobs = Array.from(this.jobs.values());
    return status ? jobs.filter(job => job.status === status) : jobs;
  }

  async removeJob(id: string): Promise<boolean> {
    return this.jobs.delete(id);
  }

  async retryJob(id: string): Promise<Job> {
    const job = this.jobs.get(id);
    if (!job) {
      throw new Error(`Job ${id} not found`);
    }

    if (job.status !== JobStatus.FAILED) {
      throw new Error(`Job ${id} is not in failed state`);
    }

    job.status = JobStatus.PENDING;
    job.attempts = 0;
    job.error = undefined;
    job.scheduledFor = undefined;
    job.updatedAt = new Date();

    this.jobs.set(id, job);
    return job;
  }

  private generateId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  stop(): void {
    this.processing = false;
  }

  // Enhanced methods for backward compatibility (stub implementations)
  async pauseJob(id: string): Promise<Job> {
    throw new Error('Pause functionality not available in legacy queue. Use EnhancedJobQueue instead.');
  }

  async resumeJob(id: string): Promise<Job> {
    throw new Error('Resume functionality not available in legacy queue. Use EnhancedJobQueue instead.');
  }

  async stopJob(id: string): Promise<Job> {
    throw new Error('Stop functionality not available in legacy queue. Use EnhancedJobQueue instead.');
  }

  async updateProgress(id: string, progress: number, details?: any): Promise<void> {
    // Legacy queue doesn't support progress tracking
    console.warn('Progress tracking not available in legacy queue. Use EnhancedJobQueue instead.');
  }

  async getJobHistory(limit?: number, offset?: number): Promise<Job[]> {
    return this.getJobs();
  }

  async getJobsByTool(toolId: string): Promise<Job[]> {
    const allJobs = await this.getJobs();
    return allJobs.filter(job => job.toolId === toolId);
  }

  async getQueueStats(): Promise<any> {
    const allJobs = await this.getJobs();
    return {
      totalJobs: allJobs.length,
      pendingJobs: allJobs.filter(j => j.status === 'pending').length,
      processingJobs: allJobs.filter(j => j.status === 'processing').length,
      completedJobs: allJobs.filter(j => j.status === 'completed').length,
      failedJobs: allJobs.filter(j => j.status === 'failed').length,
      pausedJobs: 0,
      averageProcessingTime: 0,
      successRate: 0,
      lastUpdated: new Date(),
    };
  }
}

// Singleton instance
let queueInstance: InMemoryJobQueue | null = null;

export function getJobQueue(): InMemoryJobQueue {
  if (!queueInstance) {
    const maxConcurrent = parseInt(process.env.MAX_CONCURRENT_JOBS || '3');
    queueInstance = new InMemoryJobQueue(maxConcurrent);
  }
  return queueInstance;
}

export { InMemoryJobQueue };
