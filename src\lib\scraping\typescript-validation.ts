/**
 * TypeScript validation file for scrape.do integration
 * This file imports and uses all the main types to ensure they compile correctly
 */

import {
  ScrapeDoConfig,
  ScrapeOptions,
  ScrapeResult,
  ContentAnalysis,
  MediaAsset,
  ImageCollection,
  MultiPageScrapingConfig,
  EnhancedScrapeRequest,
  EnhancedScrapeResult,
  ValidationResult,
  UsageStats,
  ScrapeError,
  RetryConfig,
  NetworkRequest,
  NetworkScrapeResult
} from './types';

import { scrapeDoClient } from './scrape-do-client';
import { costOptimizer } from './cost-optimizer';
import { ContentAnalyzer } from './content-analyzer';
import { mediaExtractor } from './media-extractor';
import { multiPageScraper } from './multi-page-scraper';

/**
 * Validation function to ensure all types are properly defined
 * This function will not be called at runtime but ensures TypeScript compilation
 */
export function validateTypes(): void {
  // Test ScrapeDoConfig
  const config: ScrapeDoConfig = {
    apiKey: 'test',
    baseUrl: 'https://api.scrape.do',
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 2000
  };

  // Test ScrapeOptions
  const options: ScrapeOptions = {
    useResidentialProxy: false,
    enableJSRendering: true,
    outputFormat: 'markdown',
    deviceType: 'desktop',
    waitCondition: 'networkidle2',
    customWaitTime: 3000,
    blockResources: true,
    timeout: 30000
  };

  // Test ScrapeResult
  const result: ScrapeResult = {
    success: true,
    content: 'test content',
    metadata: {
      creditsUsed: 5,
      requestType: 'Enhanced',
      proxyType: 'datacenter',
      browserEnabled: true,
      processingTime: 1500,
      validation: true,
      qualityScore: 85
    },
    timestamp: new Date().toISOString(),
    url: 'https://example.com'
  };

  // Test ContentAnalysis
  const analysis: ContentAnalysis = {
    hasMetaTags: true,
    hasLoadingIndicators: false,
    hasSubstantialContent: true,
    hasStructure: true,
    contentRatio: 0.8,
    needsEnhancedScraping: false,
    confidence: 90,
    scenario: 'content_sufficient'
  };

  // Test MediaAsset
  const mediaAsset: MediaAsset = {
    type: 'og:image',
    url: 'https://example.com/image.jpg',
    priority: 1,
    metadata: {
      width: 1200,
      height: 630,
      format: 'jpeg',
      size: 150000
    }
  };

  // Test ImageCollection
  const imageCollection: ImageCollection = {
    favicon: ['https://example.com/favicon.ico'],
    ogImages: [mediaAsset],
    screenshot: {
      success: true,
      screenshot: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      metadata: {
        width: 1200,
        height: 800,
        fullPage: false,
        capturedAt: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    }
  };

  // Test MultiPageScrapingConfig
  const multiPageConfig: MultiPageScrapingConfig = {
    enabled: true,
    mode: 'conditional',
    maxPagesPerTool: 4,
    creditThreshold: 100,
    pageTypes: {
      pricing: {
        enabled: true,
        priority: 'high',
        patterns: ['/pricing', '/plans'],
        selectors: ['.pricing'],
        required: true
      },
      faq: {
        enabled: true,
        priority: 'medium',
        patterns: ['/faq', '/help'],
        selectors: ['.faq'],
        required: false
      },
      features: {
        enabled: true,
        priority: 'high',
        patterns: ['/features'],
        selectors: ['.features'],
        required: true
      },
      about: {
        enabled: false,
        priority: 'low',
        patterns: ['/about'],
        selectors: ['.about'],
        required: false
      }
    },
    fallbackStrategy: {
      searchInMainPage: true,
      useNavigation: true,
      useSitemap: false
    }
  };

  // Test EnhancedScrapeRequest
  const enhancedRequest: EnhancedScrapeRequest = {
    url: 'https://example.com',
    options,
    multiPageConfig,
    mediaCollection: true,
    costOptimization: true
  };

  // Test EnhancedScrapeResult
  const enhancedResult: EnhancedScrapeResult = {
    ...result,
    mediaAssets: imageCollection,
    additionalPages: [result],
    costAnalysis: {
      creditsUsed: 10,
      estimatedSavings: 5,
      optimizationStrategy: 'pattern-based'
    },
    contentAnalysis: analysis
  };

  // Test ValidationResult
  const validation: ValidationResult = {
    isValid: true,
    issues: [],
    contentLength: 1500,
    url: 'https://example.com',
    qualityScore: 85
  };

  // Test UsageStats
  const stats: UsageStats = {
    isActive: true,
    concurrentRequests: 5,
    maxMonthlyRequests: 10000,
    remainingConcurrentRequests: 3,
    remainingMonthlyRequests: 8500,
    lastUpdated: new Date().toISOString()
  };

  // Test ScrapeError
  const error: ScrapeError = {
    code: 'TIMEOUT',
    message: 'Request timed out',
    url: 'https://example.com',
    timestamp: new Date().toISOString(),
    retryable: true,
    context: { timeout: 30000 }
  };

  // Test RetryConfig
  const retryConfig: RetryConfig = {
    maxAttempts: 3,
    baseDelay: 2000,
    maxDelay: 10000,
    backoffMultiplier: 2
  };

  // Test NetworkRequest
  const networkRequest: NetworkRequest = {
    url: 'https://example.com/api/data',
    method: 'GET',
    status: 200,
    headers: { 'content-type': 'application/json' },
    timestamp: new Date().toISOString()
  };

  // Test NetworkScrapeResult
  const networkResult: NetworkScrapeResult = {
    content: 'scraped content',
    networkRequests: [networkRequest],
    frames: [{
      id: 'frame1',
      url: 'https://example.com/frame',
      name: 'main-frame'
    }],
    websockets: [{
      url: 'wss://example.com/ws',
      readyState: 1,
      protocol: 'websocket'
    }],
    headers: { 'user-agent': 'scrape-do-client' }
  };

  // Verify all variables are used (prevents unused variable warnings)
  console.log('TypeScript validation passed for:', {
    config,
    options,
    result,
    analysis,
    mediaAsset,
    imageCollection,
    multiPageConfig,
    enhancedRequest,
    enhancedResult,
    validation,
    stats,
    error,
    retryConfig,
    networkRequest,
    networkResult
  });
}

/**
 * Test function signatures and method calls
 */
export async function validateMethodSignatures(): Promise<void> {
  try {
    // Test scrapeDoClient methods
    const stats = await scrapeDoClient.getUsageStatistics();
    console.log('Usage stats type check:', stats.isActive);

    // Test costOptimizer methods
    const urls = ['https://github.com/test', 'https://claude.ai'];
    const categorized = costOptimizer.categorizeUrlsByPattern(urls);
    console.log('Categorized URLs type check:', categorized.neverEnhance.length);

    // Test contentAnalyzer methods
    const analyzer = new ContentAnalyzer();
    const contentAnalysis = analyzer.analyzeContentQuality('test content');
    console.log('Content analysis type check:', contentAnalysis.confidence);

    // Test mediaExtractor methods
    const images = await mediaExtractor.collectImagesWithPriority('https://example.com', 'test content');
    console.log('Image collection type check:', images.favicon?.length);

    // Test multiPageScraper methods
    const config = multiPageScraper.getConfig();
    console.log('Multi-page config type check:', config.enabled);

    // Test contentProcessor methods
    const request: EnhancedScrapeRequest = {
      url: 'https://example.com',
      options: { outputFormat: 'markdown' },
      costOptimization: true
    };
    // Note: Not actually calling this in validation to avoid API calls
    console.log('Content processor request type check:', request.url);

  } catch {
    console.log('Method signature validation completed with expected errors (no actual API calls)');
  }
}

// Export validation functions for testing
export { validateTypes as default };
