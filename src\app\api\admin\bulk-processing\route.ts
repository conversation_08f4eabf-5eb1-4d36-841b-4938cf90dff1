/**
 * Bulk Processing API Routes
 * Handles bulk processing operations including file uploads and job management
 */

import { NextRequest, NextResponse } from 'next/server';
import { getBulkProcessingEngine } from '@/lib/bulk-processing/bulk-engine';
import { TextFileProcessor, JSONFileProcessor, ManualEntryProcessor } from '@/lib/bulk-processing/file-processors';
import { BulkProcessingOptions } from '@/lib/bulk-processing/bulk-engine';

// Admin API key validation
function validateAdminApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('x-admin-api-key');
  const expectedKey = process.env.ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';
  return apiKey === expectedKey;
}

/**
 * GET /api/admin/bulk-processing
 * Get bulk processing jobs with filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin access
    if (!validateAdminApiKey(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    const bulkEngine = getBulkProcessingEngine();
    const jobs = await bulkEngine.getBulkJobs({
      status: status as any,
      limit,
      offset,
    });

    return NextResponse.json({
      success: true,
      data: {
        jobs,
        pagination: {
          limit,
          offset,
          total: jobs.length, // TODO: Get actual total count
        },
      },
    });
  } catch (error) {
    console.error('Failed to get bulk processing jobs:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get bulk processing jobs' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/bulk-processing
 * Create new bulk processing job
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin access
    if (!validateAdminApiKey(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const contentType = request.headers.get('content-type') || '';

    // Handle file upload (multipart/form-data)
    if (contentType.includes('multipart/form-data')) {
      return await handleFileUpload(request);
    }

    // Handle JSON data (manual entry or API submission)
    if (contentType.includes('application/json')) {
      return await handleJSONSubmission(request);
    }

    return NextResponse.json(
      { success: false, error: 'Unsupported content type' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Failed to create bulk processing job:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create bulk processing job' },
      { status: 500 }
    );
  }
}

/**
 * Handle file upload processing
 */
async function handleFileUpload(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const optionsJson = formData.get('options') as string;

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Parse processing options
    let options: BulkProcessingOptions;
    try {
      options = JSON.parse(optionsJson || '{}');
    } catch {
      return NextResponse.json(
        { success: false, error: 'Invalid options JSON' },
        { status: 400 }
      );
    }

    // Set default options
    const processingOptions: BulkProcessingOptions = {
      batchSize: options.batchSize || 5,
      delayBetweenBatches: options.delayBetweenBatches || 2000,
      retryAttempts: options.retryAttempts || 3,
      aiProvider: options.aiProvider || 'openai',
      skipExisting: options.skipExisting || false,
      scrapeOnly: options.scrapeOnly || false,
      generateContent: options.generateContent !== false, // Default to true
      autoPublish: options.autoPublish || false,
      priority: options.priority || 'normal',
    };

    // Determine file type and process
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    let processor;
    let jobType: 'text_file' | 'json_file';

    if (fileExtension === '.txt') {
      processor = new TextFileProcessor();
      jobType = 'text_file';
    } else if (fileExtension === '.json') {
      processor = new JSONFileProcessor();
      jobType = 'json_file';
    } else {
      return NextResponse.json(
        { success: false, error: `Unsupported file type: ${fileExtension}` },
        { status: 400 }
      );
    }

    // Process file
    const processingResult = await processor.processFile(file);

    if (!processingResult.success || !processingResult.data) {
      return NextResponse.json(
        { 
          success: false, 
          error: processingResult.error || 'File processing failed',
          validation: processingResult.validation,
        },
        { status: 400 }
      );
    }

    // Create bulk processing job
    const bulkEngine = getBulkProcessingEngine();
    const bulkJob = await bulkEngine.createBulkJob(
      processingResult.data.validItems,
      processingOptions,
      {
        jobType,
        filename: file.name,
        submittedBy: 'admin', // TODO: Get actual user from auth
      }
    );

    return NextResponse.json({
      success: true,
      data: {
        job: bulkJob,
        fileProcessing: {
          totalItems: processingResult.data.totalItems,
          validItems: processingResult.data.validItems.length,
          invalidItems: processingResult.data.invalidItems.length,
          duplicatesRemoved: processingResult.data.duplicatesRemoved,
        },
        validation: processingResult.validation,
      },
    });
  } catch (error) {
    console.error('File upload processing failed:', error);
    return NextResponse.json(
      { success: false, error: 'File upload processing failed' },
      { status: 500 }
    );
  }
}

/**
 * Handle JSON submission (manual entry or API)
 */
async function handleJSONSubmission(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, data, options } = body;

    if (!type || !data) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: type, data' },
        { status: 400 }
      );
    }

    // Set default options
    const processingOptions: BulkProcessingOptions = {
      batchSize: options?.batchSize || 5,
      delayBetweenBatches: options?.delayBetweenBatches || 2000,
      retryAttempts: options?.retryAttempts || 3,
      aiProvider: options?.aiProvider || 'openai',
      skipExisting: options?.skipExisting || false,
      scrapeOnly: options?.scrapeOnly || false,
      generateContent: options?.generateContent !== false,
      autoPublish: options?.autoPublish || false,
      priority: options?.priority || 'normal',
    };

    let processingResult;

    if (type === 'manual_entry') {
      // Handle manual URL entry
      const processor = new ManualEntryProcessor();
      processingResult = processor.processInput(data.urls || data);
    } else if (type === 'json_data') {
      // Handle JSON data submission
      const processor = new JSONFileProcessor();
      // Create a mock file object for processing
      const jsonString = JSON.stringify(data);
      const mockFile = new File([jsonString], 'data.json', { type: 'application/json' });
      processingResult = await processor.processFile(mockFile);
    } else {
      return NextResponse.json(
        { success: false, error: `Unsupported submission type: ${type}` },
        { status: 400 }
      );
    }

    if (!processingResult.success || !processingResult.data) {
      return NextResponse.json(
        { 
          success: false, 
          error: processingResult.error || 'Data processing failed',
          validation: processingResult.validation,
        },
        { status: 400 }
      );
    }

    // Create bulk processing job
    const bulkEngine = getBulkProcessingEngine();
    const bulkJob = await bulkEngine.createBulkJob(
      processingResult.data.validItems,
      processingOptions,
      {
        jobType: 'manual_entry',
        submittedBy: 'admin', // TODO: Get actual user from auth
      }
    );

    return NextResponse.json({
      success: true,
      data: {
        job: bulkJob,
        processing: {
          totalItems: processingResult.data.totalItems,
          validItems: processingResult.data.validItems.length,
          invalidItems: processingResult.data.invalidItems.length,
          duplicatesRemoved: processingResult.data.duplicatesRemoved,
        },
        validation: processingResult.validation,
      },
    });
  } catch (error) {
    console.error('JSON submission processing failed:', error);
    return NextResponse.json(
      { success: false, error: 'JSON submission processing failed' },
      { status: 500 }
    );
  }
}
