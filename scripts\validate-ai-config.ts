#!/usr/bin/env tsx

import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables from .env.local
config({ path: resolve(process.cwd(), '.env.local') });

// Also try .env as fallback
config({ path: resolve(process.cwd(), '.env') });

async function validateAIConfiguration() {
  console.log('🔍 Validating AI Configuration...\n');

  // Import after loading env vars
  const { AIUtils, performHealthCheck } = await import('../src/lib/ai');

  // 1. Configuration Validation
  console.log('1️⃣ Environment Variables:');
  const configValidation = AIUtils.validateConfiguration();
  
  if (configValidation.valid) {
    console.log('✅ All environment variables are properly configured');
  } else {
    console.log('❌ Configuration issues found:');
    configValidation.issues.forEach(issue => {
      console.log(`   - ${issue}`);
    });
  }
  console.log();

  // 2. Show current environment values (masked for security)
  console.log('2️⃣ Current Configuration:');
  console.log(`OPENAI_API_KEY: ${process.env.OPENAI_API_KEY ? '✅ Set (sk-...)' : '❌ Missing'}`);
  console.log(`OPENROUTER_API_KEY: ${process.env.OPENROUTER_API_KEY ? '✅ Set (sk-or-...)' : '❌ Missing'}`);
  console.log(`OPENAI_MODEL: ${process.env.OPENAI_MODEL || '❌ Not set'}`);
  console.log(`OPENROUTER_MODEL: ${process.env.OPENROUTER_MODEL || '❌ Not set'}`);
  console.log(`SITE_URL: ${process.env.SITE_URL || '❌ Not set'}`);
  console.log(`AI_PROVIDER_PRIMARY: ${process.env.AI_PROVIDER_PRIMARY || '❌ Not set'}`);
  console.log(`AI_PROVIDER_FALLBACK: ${process.env.AI_PROVIDER_FALLBACK || '❌ Not set'}`);
  console.log(`CONTENT_GENERATION_ENABLED: ${process.env.CONTENT_GENERATION_ENABLED || '❌ Not set'}`);
  console.log();

  // 3. Health Check (only if config is valid)
  if (configValidation.valid) {
    console.log('3️⃣ Provider Health Check:');
    try {
      const healthCheck = await performHealthCheck();
      console.log(`System Status: ${healthCheck.status}`);
      console.log(`OpenAI: ${healthCheck.providers.openai.status} ${healthCheck.providers.openai.latency ? `(${healthCheck.providers.openai.latency}ms)` : ''}`);
      console.log(`OpenRouter: ${healthCheck.providers.openrouter.status} ${healthCheck.providers.openrouter.latency ? `(${healthCheck.providers.openrouter.latency}ms)` : ''}`);
    } catch (error) {
      console.log('❌ Health check failed:', error instanceof Error ? error.message : String(error));
    }
  } else {
    console.log('3️⃣ Skipping health check due to configuration issues');
  }
  console.log();

  // 4. Recommendations
  console.log('4️⃣ Recommendations:');
  if (!configValidation.valid) {
    console.log('📝 To fix configuration issues:');
    console.log('   1. Ensure .env.local file exists in project root');
    console.log('   2. Add missing environment variables to .env.local');
    console.log('   3. Restart your development server after making changes');
    console.log();
    console.log('📋 Required environment variables:');
    console.log('   OPENAI_API_KEY=sk-proj-...');
    console.log('   OPENROUTER_API_KEY=sk-or-v1-...');
    console.log('   SITE_URL=https://aidude.com');
    console.log('   OPENAI_MODEL=gpt-4o-2024-11-20');
    console.log('   OPENROUTER_MODEL=google/gemini-2.5-pro-preview');
    console.log('   CONTENT_GENERATION_ENABLED=true');
    console.log('   AI_PROVIDER_PRIMARY=openrouter');
    console.log('   AI_PROVIDER_FALLBACK=openai');
  } else {
    console.log('✅ Configuration is valid and ready for use!');
  }
}

// Run validation
validateAIConfiguration().catch(console.error);
