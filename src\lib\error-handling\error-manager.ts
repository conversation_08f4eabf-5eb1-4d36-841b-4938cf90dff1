import { 
  ErrorCategory, 
  ErrorSeverity, 
  SystemError, 
  ErrorContext, 
  ErrorClassification,
  RecoveryResult,
  ErrorMetrics,
  AlertThreshold,
  ErrorReport,
  TimeRange,
  ImpactAssessment
} from './types';
import { RecoveryStrategyManager } from './recovery-strategies';
import { ErrorMonitoringSystem } from './monitoring';
import { HealthChecker } from '../monitoring/health-checker';

/**
 * Central error management system for the Enhanced AI System
 * Provides comprehensive error handling, classification, recovery, and monitoring
 */
export class ErrorManager {
  private static instance: ErrorManager;
  private recoveryManager: RecoveryStrategyManager;
  private monitoringSystem: ErrorMonitoringSystem;
  private healthChecker: HealthChecker;
  private errorMetrics = new Map<string, ErrorMetrics>();
  private alertThresholds = new Map<string, AlertThreshold>();

  private constructor() {
    this.recoveryManager = new RecoveryStrategyManager();
    this.monitoringSystem = new ErrorMonitoringSystem();
    this.healthChecker = new HealthChecker();
    this.initializeAlertThresholds();
  }

  public static getInstance(): ErrorManager {
    if (!ErrorManager.instance) {
      ErrorManager.instance = new ErrorManager();
    }
    return ErrorManager.instance;
  }

  /**
   * Main error handling entry point
   * Classifies error, attempts recovery, and logs results
   */
  async handleError(
    error: any, 
    context: ErrorContext = {}
  ): Promise<RecoveryResult> {
    try {
      // Classify the error
      const systemError = this.classifyError(error, context);
      
      // Track error metrics
      await this.trackError(systemError, context);
      
      // Attempt automatic recovery if applicable
      const recoveryResult = await this.attemptRecovery(systemError, context);
      
      // Log the error and recovery attempt
      await this.logErrorEvent(systemError, context, recoveryResult);
      
      // Check if manual intervention is required
      if (!recoveryResult.success && systemError.classification.requiresManualIntervention) {
        await this.triggerManualIntervention(systemError, context);
      }
      
      return recoveryResult;
      
    } catch (handlingError) {
      console.error('Error in error handling system:', handlingError);

      // Fallback error response
      const errorMessage = handlingError instanceof Error ? handlingError.message : String(handlingError);
      return {
        success: false,
        strategy: 'none',
        error: `Error handling failed: ${errorMessage}`,
        timestamp: new Date().toISOString(),
        requiresManualIntervention: true
      };
    }
  }

  /**
   * Classify error based on type, message, and context
   */
  private classifyError(error: any, context: ErrorContext): SystemError {
    const classification = this.determineErrorClassification(error, context);
    
    return {
      id: this.generateErrorId(),
      type: this.determineErrorType(error, context),
      category: classification.category,
      severity: classification.severity,
      message: error.message || 'Unknown error',
      stack: error.stack,
      context,
      classification,
      timestamp: new Date().toISOString(),
      originalError: error
    };
  }

  /**
   * Determine error classification based on error characteristics
   */
  private determineErrorClassification(error: any, context: ErrorContext): ErrorClassification {
    // Network errors
    if (this.isNetworkError(error)) {
      return {
        category: ErrorCategory.NETWORK,
        severity: this.getNetworkErrorSeverity(error),
        retryable: true,
        autoRecoverable: true,
        requiresManualIntervention: false,
        affectedSystems: ['external_apis'],
        estimatedImpact: this.assessNetworkErrorImpact(error)
      };
    }

    // AI provider errors
    if (this.isAIProviderError(error, context)) {
      return {
        category: ErrorCategory.AI_GENERATION,
        severity: this.getAIErrorSeverity(error),
        retryable: this.isAIErrorRetryable(error),
        autoRecoverable: true,
        requiresManualIntervention: this.requiresAIManualIntervention(error),
        affectedSystems: ['ai_generation', 'content_pipeline'],
        estimatedImpact: this.assessAIErrorImpact(error)
      };
    }

    // Scraping errors
    if (this.isScrapingError(error, context)) {
      return {
        category: ErrorCategory.SCRAPING,
        severity: ErrorSeverity.MEDIUM,
        retryable: true,
        autoRecoverable: true,
        requiresManualIntervention: false,
        affectedSystems: ['web_scraping', 'content_extraction'],
        estimatedImpact: { userImpact: 'medium', systemImpact: 'low', businessImpact: 'low' }
      };
    }

    // Database errors
    if (this.isDatabaseError(error)) {
      return {
        category: ErrorCategory.STORAGE,
        severity: ErrorSeverity.CRITICAL,
        retryable: true,
        autoRecoverable: true,
        requiresManualIntervention: true,
        affectedSystems: ['database', 'job_processing', 'content_storage'],
        estimatedImpact: { userImpact: 'high', systemImpact: 'critical', businessImpact: 'high' }
      };
    }

    // Validation errors
    if (this.isValidationError(error, context)) {
      return {
        category: ErrorCategory.VALIDATION,
        severity: ErrorSeverity.MEDIUM,
        retryable: true,
        autoRecoverable: true,
        requiresManualIntervention: false,
        affectedSystems: ['content_validation', 'quality_control'],
        estimatedImpact: { userImpact: 'low', systemImpact: 'low', businessImpact: 'medium' }
      };
    }

    // Default classification for unknown errors
    return {
      category: ErrorCategory.SYSTEM,
      severity: ErrorSeverity.HIGH,
      retryable: false,
      autoRecoverable: false,
      requiresManualIntervention: true,
      affectedSystems: ['unknown'],
      estimatedImpact: { userImpact: 'medium', systemImpact: 'medium', businessImpact: 'medium' }
    };
  }

  /**
   * Attempt automatic recovery based on error classification
   */
  private async attemptRecovery(
    systemError: SystemError, 
    context: ErrorContext
  ): Promise<RecoveryResult> {
    if (!systemError.classification.autoRecoverable) {
      return {
        success: false,
        strategy: 'none',
        error: 'Error not auto-recoverable',
        timestamp: new Date().toISOString(),
        requiresManualIntervention: systemError.classification.requiresManualIntervention
      };
    }

    return await this.recoveryManager.executeRecovery(systemError, context);
  }

  /**
   * Track error metrics for monitoring and alerting
   */
  private async trackError(systemError: SystemError, context: ErrorContext): Promise<void> {
    await this.monitoringSystem.trackError(systemError, context);
    this.updateLocalMetrics(systemError);
    await this.checkAlertThresholds(systemError);
  }

  /**
   * Update local error metrics
   */
  private updateLocalMetrics(systemError: SystemError): void {
    const key = `${systemError.category}_${systemError.type}`;
    const metrics = this.errorMetrics.get(key) || {
      count: 0,
      lastOccurrence: null,
      frequency: 0,
      trend: 'stable',
      consecutiveFailures: 0,
      recoverySuccessRate: 0
    };

    metrics.count++;
    metrics.lastOccurrence = new Date();
    metrics.frequency = this.calculateFrequency(key);
    metrics.trend = this.calculateTrend(key);

    this.errorMetrics.set(key, metrics);
  }

  /**
   * Check if error frequency exceeds alert thresholds
   */
  private async checkAlertThresholds(systemError: SystemError): Promise<void> {
    const thresholds = this.alertThresholds.get(systemError.type);
    if (!thresholds) return;

    const metrics = this.errorMetrics.get(`${systemError.category}_${systemError.type}`);
    if (!metrics) return;

    // Check frequency threshold
    if (metrics.frequency > thresholds.maxFrequency) {
      await this.triggerAlert({
        type: 'frequency_exceeded',
        errorType: systemError.type,
        currentFrequency: metrics.frequency,
        threshold: thresholds.maxFrequency,
        severity: systemError.severity
      });
    }

    // Check consecutive failures
    if (metrics.consecutiveFailures > thresholds.maxConsecutiveFailures) {
      await this.triggerAlert({
        type: 'consecutive_failures',
        errorType: systemError.type,
        consecutiveFailures: metrics.consecutiveFailures,
        threshold: thresholds.maxConsecutiveFailures,
        severity: systemError.severity
      });
    }
  }

  /**
   * Initialize alert thresholds for different error types
   */
  private initializeAlertThresholds(): void {
    // Network error thresholds
    this.alertThresholds.set('NETWORK_TIMEOUT', {
      maxFrequency: 10, // per hour
      maxConsecutiveFailures: 5,
      escalationTime: 30 * 60 * 1000 // 30 minutes
    });

    // AI provider error thresholds
    this.alertThresholds.set('OPENAI_RATE_LIMIT', {
      maxFrequency: 5, // per hour
      maxConsecutiveFailures: 3,
      escalationTime: 60 * 60 * 1000 // 1 hour
    });

    this.alertThresholds.set('OPENROUTER_INSUFFICIENT_CREDITS', {
      maxFrequency: 1, // per hour
      maxConsecutiveFailures: 1,
      escalationTime: 15 * 60 * 1000 // 15 minutes
    });

    // Database error thresholds
    this.alertThresholds.set('DATABASE_CONNECTION_LOST', {
      maxFrequency: 3, // per hour
      maxConsecutiveFailures: 2,
      escalationTime: 5 * 60 * 1000 // 5 minutes
    });
  }

  // Helper methods for error classification
  private isNetworkError(error: any): boolean {
    return error.code === 'ECONNRESET' || 
           error.code === 'ENOTFOUND' || 
           error.code === 'ETIMEDOUT' ||
           error.message?.includes('network') ||
           error.message?.includes('timeout');
  }

  private isAIProviderError(error: any, context: ErrorContext): boolean {
    return context.provider === 'openai' || 
           context.provider === 'openrouter' ||
           error.message?.includes('OpenAI') ||
           error.message?.includes('OpenRouter');
  }

  private isScrapingError(error: any, context: ErrorContext): boolean {
    return context.operation === 'scraping' ||
           error.message?.includes('scrape.do') ||
           error.message?.includes('scraping');
  }

  private isDatabaseError(error: any): boolean {
    return error.code === 'PGRST' ||
           error.message?.includes('database') ||
           error.message?.includes('Supabase');
  }

  private isValidationError(error: any, context: ErrorContext): boolean {
    return context.operation === 'validation' ||
           error.message?.includes('validation') ||
           error.message?.includes('schema');
  }

  // Additional helper methods would be implemented here...
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private determineErrorType(error: any, context: ErrorContext): string {
    // Implementation would determine specific error type based on error characteristics
    return error.code || error.type || 'UNKNOWN_ERROR';
  }

  private getNetworkErrorSeverity(error: any): ErrorSeverity {
    if (error.code === 'ENOTFOUND') return ErrorSeverity.HIGH;
    if (error.code === 'ETIMEDOUT') return ErrorSeverity.MEDIUM;
    return ErrorSeverity.LOW;
  }

  private getAIErrorSeverity(error: any): ErrorSeverity {
    if (error.code === 'insufficient_quota') return ErrorSeverity.HIGH;
    if (error.code === 'rate_limit_exceeded') return ErrorSeverity.MEDIUM;
    return ErrorSeverity.LOW;
  }

  private isAIErrorRetryable(error: any): boolean {
    const retryableCodes = ['rate_limit_exceeded', 'timeout', 'network_error'];
    return retryableCodes.includes(error.code);
  }

  private requiresAIManualIntervention(error: any): boolean {
    const manualCodes = ['insufficient_quota', 'invalid_api_key', 'account_suspended'];
    return manualCodes.includes(error.code);
  }

  private assessNetworkErrorImpact(error: any): ImpactAssessment {
    return {
      userImpact: 'medium',
      systemImpact: 'medium', 
      businessImpact: 'low'
    };
  }

  private assessAIErrorImpact(error: any): ImpactAssessment {
    if (error.code === 'insufficient_quota') {
      return { userImpact: 'high', systemImpact: 'high', businessImpact: 'high' };
    }
    return { userImpact: 'medium', systemImpact: 'medium', businessImpact: 'medium' };
  }

  private calculateFrequency(errorKey: string): number {
    // Implementation would calculate error frequency over time window
    return 0;
  }

  private calculateTrend(errorKey: string): 'increasing' | 'decreasing' | 'stable' {
    // Implementation would analyze error trend over time
    return 'stable';
  }

  private async triggerAlert(alert: any): Promise<void> {
    console.warn('ALERT TRIGGERED:', alert);
    // Implementation would send alerts via email, Slack, etc.
  }

  private async triggerManualIntervention(systemError: SystemError, context: ErrorContext): Promise<void> {
    console.error('MANUAL INTERVENTION REQUIRED:', {
      error: systemError,
      context,
      timestamp: new Date().toISOString()
    });
    // Implementation would notify administrators
  }

  private async logErrorEvent(
    systemError: SystemError, 
    context: ErrorContext, 
    recoveryResult: RecoveryResult
  ): Promise<void> {
    console.log('ERROR EVENT:', {
      error: systemError,
      context,
      recovery: recoveryResult,
      timestamp: new Date().toISOString()
    });
    // Implementation would log to persistent storage
  }

  /**
   * Get current system health status
   */
  async getSystemHealth(): Promise<any> {
    return await this.healthChecker.runAllHealthChecks();
  }

  /**
   * Generate error report for specified time range
   */
  async generateErrorReport(timeRange: TimeRange): Promise<ErrorReport> {
    return await this.monitoringSystem.generateErrorReport(timeRange);
  }

  /**
   * Get error metrics for monitoring dashboard
   */
  getErrorMetrics(): Map<string, ErrorMetrics> {
    return new Map(this.errorMetrics);
  }
}

// Export singleton instance
export const errorManager = ErrorManager.getInstance();
