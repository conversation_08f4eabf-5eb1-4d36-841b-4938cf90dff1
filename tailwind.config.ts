import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        'brand-orange': '#ff9701',
        'brand-yellow': '#fde049',
        'brand-brown': '#7f3e13',
        'header-bg': '#232323', // Main header background from screenshot
        'top-bar-bg': '#1a1a1a', // Darker top bar background from screenshot
        'search-bg': '#333333',
        'callout-bg': '#3a3a3a',
        'icon-bg': '#4a4a4a', // Adjusted for AIDUDE icon backgrounds
        'icon-border': '#6b6b6b', // Adjusted for AIDUDE icon borders
      },
      fontFamily: {
        roboto: ['Roboto', 'sans-serif'],
      },
    },
  },
  plugins: [],
}
export default config
