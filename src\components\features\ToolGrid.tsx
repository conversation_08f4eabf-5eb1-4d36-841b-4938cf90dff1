'use client';

import React from 'react';
import { AITool } from '@/lib/types';
import { ToolCard } from './ToolCard';

interface ToolGridProps {
  tools: AITool[];
  showCategory?: boolean;
  showSubcategory?: boolean;
  className?: string;
  emptyMessage?: string;
}

export function ToolGrid({ 
  tools, 
  showCategory = false, 
  showSubcategory = false, 
  className = '',
  emptyMessage = 'No tools found matching your criteria.'
}: ToolGridProps) {
  
  if (tools.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-8">
          <div className="text-gray-400 text-lg mb-2">
            {emptyMessage}
          </div>
          <p className="text-gray-500 text-sm">
            Try adjusting your filters or search terms to find more tools.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 ${className}`}>
      {tools.map((tool) => (
        <ToolCard
          key={tool.id}
          tool={tool}
          showCategory={showCategory}
          showSubcategory={showSubcategory}
        />
      ))}
    </div>
  );
}
