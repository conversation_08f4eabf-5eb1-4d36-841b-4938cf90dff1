'use client';

import { useState, useEffect } from 'react';

interface AIProviderConfigProps {
  onSave: () => void;
}

interface AIProviderData {
  openai: {
    enabled: boolean;
    model: string;
    maxTokens: number;
    temperature: number;
    timeout: number;
    priority: number;
  };
  openrouter: {
    enabled: boolean;
    model: string;
    maxTokens: number;
    temperature: number;
    implicitCaching: boolean;
    timeout: number;
    priority: number;
  };
  modelSelection: {
    strategy: string;
    fallbackOrder: string[];
    costThreshold: number;
    qualityThreshold: number;
  };
}

export function AIProviderConfig({ onSave }: AIProviderConfigProps) {
  const [config, setConfig] = useState<AIProviderData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadConfiguration();
  }, []);

  const loadConfiguration = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/config?section=ai-providers', {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load AI provider configuration');
      }

      const data = await response.json();
      setConfig(data.data);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!config) return;

    try {
      setSaving(true);
      setError(null);

      const response = await fetch('/api/admin/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          section: 'ai-providers',
          action: 'update',
          data: config
        })
      });

      if (!response.ok) {
        throw new Error('Failed to save AI provider configuration');
      }

      const result = await response.json();
      
      if (result.validation && !result.validation.isValid) {
        throw new Error(`Validation failed: ${result.validation.errors.map((e: { message: string }) => e.message).join(', ')}`);
      }

      onSave();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setSaving(false);
    }
  };

  const updateConfig = (path: string, value: unknown) => {
    if (!config) return;

    const keys = path.split('.');
    const newConfig = { ...config };
    let current: Record<string, unknown> = newConfig;

    for (let i = 0; i < keys.length - 1; i++) {
      current = (current as Record<string, unknown>)[keys[i]] as Record<string, unknown>;
    }
    current[keys[keys.length - 1]] = value;

    setConfig(newConfig);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-400">Loading AI provider configuration...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-700 rounded-lg p-4">
        <div className="text-red-400 font-semibold">Error</div>
        <div className="text-red-300 mt-1">{error}</div>
        <button
          onClick={loadConfiguration}
          className="mt-3 bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!config) return null;

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-2">AI Provider Configuration</h3>
        <p className="text-gray-400">
          Configure OpenAI and OpenRouter providers for content generation.
        </p>
      </div>

      {/* OpenAI Configuration */}
      <div className="bg-zinc-700 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="font-semibold">OpenAI Configuration</h4>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.openai.enabled}
              onChange={(e) => updateConfig('openai.enabled', e.target.checked)}
              className="rounded bg-zinc-600 border-zinc-500 text-orange-500 focus:ring-orange-500"
            />
            <span className="text-sm">Enabled</span>
          </label>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Model</label>
            <select
              value={config.openai.model}
              onChange={(e) => updateConfig('openai.model', e.target.value)}
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            >
              <option value="gpt-4o-2024-11-20">GPT-4o (2024-11-20)</option>
              <option value="gpt-4o">GPT-4o</option>
              <option value="gpt-4o-mini">GPT-4o Mini</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Max Tokens</label>
            <input
              type="number"
              value={config.openai.maxTokens}
              onChange={(e) => updateConfig('openai.maxTokens', parseInt(e.target.value))}
              min="1"
              max="16384"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Temperature</label>
            <input
              type="number"
              value={config.openai.temperature}
              onChange={(e) => updateConfig('openai.temperature', parseFloat(e.target.value))}
              min="0"
              max="2"
              step="0.1"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Timeout (ms)</label>
            <input
              type="number"
              value={config.openai.timeout}
              onChange={(e) => updateConfig('openai.timeout', parseInt(e.target.value))}
              min="1000"
              max="300000"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Priority</label>
            <input
              type="number"
              value={config.openai.priority}
              onChange={(e) => updateConfig('openai.priority', parseInt(e.target.value))}
              min="1"
              max="10"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>
        </div>
      </div>

      {/* OpenRouter Configuration */}
      <div className="bg-zinc-700 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="font-semibold">OpenRouter Configuration</h4>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.openrouter.enabled}
              onChange={(e) => updateConfig('openrouter.enabled', e.target.checked)}
              className="rounded bg-zinc-600 border-zinc-500 text-orange-500 focus:ring-orange-500"
            />
            <span className="text-sm">Enabled</span>
          </label>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Model</label>
            <select
              value={config.openrouter.model}
              onChange={(e) => updateConfig('openrouter.model', e.target.value)}
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            >
              <option value="google/gemini-2.5-pro-preview">Gemini 2.5 Pro Preview</option>
              <option value="google/gemini-pro-1.5">Gemini Pro 1.5</option>
              <option value="anthropic/claude-3.5-sonnet">Claude 3.5 Sonnet</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Max Tokens</label>
            <input
              type="number"
              value={config.openrouter.maxTokens}
              onChange={(e) => updateConfig('openrouter.maxTokens', parseInt(e.target.value))}
              min="1"
              max="65536"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Temperature</label>
            <input
              type="number"
              value={config.openrouter.temperature}
              onChange={(e) => updateConfig('openrouter.temperature', parseFloat(e.target.value))}
              min="0"
              max="2"
              step="0.1"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Timeout (ms)</label>
            <input
              type="number"
              value={config.openrouter.timeout}
              onChange={(e) => updateConfig('openrouter.timeout', parseInt(e.target.value))}
              min="1000"
              max="300000"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Priority</label>
            <input
              type="number"
              value={config.openrouter.priority}
              onChange={(e) => updateConfig('openrouter.priority', parseInt(e.target.value))}
              min="1"
              max="10"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>

          <div className="md:col-span-2">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.openrouter.implicitCaching}
                onChange={(e) => updateConfig('openrouter.implicitCaching', e.target.checked)}
                className="rounded bg-zinc-600 border-zinc-500 text-orange-500 focus:ring-orange-500"
              />
              <span className="text-sm">Enable Implicit Caching</span>
            </label>
          </div>
        </div>
      </div>

      {/* Model Selection Strategy */}
      <div className="bg-zinc-700 rounded-lg p-6">
        <h4 className="font-semibold mb-4">Model Selection Strategy</h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Strategy</label>
            <select
              value={config.modelSelection.strategy}
              onChange={(e) => updateConfig('modelSelection.strategy', e.target.value)}
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            >
              <option value="auto">Auto (Smart Selection)</option>
              <option value="manual">Manual</option>
              <option value="cost_optimized">Cost Optimized</option>
              <option value="quality_optimized">Quality Optimized</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Cost Threshold</label>
            <input
              type="number"
              value={config.modelSelection.costThreshold}
              onChange={(e) => updateConfig('modelSelection.costThreshold', parseFloat(e.target.value))}
              min="0"
              max="1"
              step="0.01"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Quality Threshold</label>
            <input
              type="number"
              value={config.modelSelection.qualityThreshold}
              onChange={(e) => updateConfig('modelSelection.qualityThreshold', parseFloat(e.target.value))}
              min="0"
              max="1"
              step="0.01"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSave}
          disabled={saving}
          className="bg-orange-500 hover:bg-orange-600 disabled:bg-orange-700 px-6 py-2 rounded-lg font-medium transition-colors"
        >
          {saving ? 'Saving...' : 'Save AI Provider Settings'}
        </button>
      </div>
    </div>
  );
}
