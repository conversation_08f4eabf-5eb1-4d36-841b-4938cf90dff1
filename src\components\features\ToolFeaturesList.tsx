'use client';

import React from 'react';
import { Check, Zap, Star, Shield, Clock, Users } from 'lucide-react';

interface ToolFeaturesListProps {
  features: string[];
}

// Icon mapping for different feature types
const getFeatureIcon = (feature: string, index: number) => {
  const lowerFeature = feature.toLowerCase();
  
  if (lowerFeature.includes('fast') || lowerFeature.includes('speed') || lowerFeature.includes('quick')) {
    return <Zap size={16} className="text-yellow-400" />;
  }
  if (lowerFeature.includes('secure') || lowerFeature.includes('privacy') || lowerFeature.includes('safe')) {
    return <Shield size={16} className="text-green-400" />;
  }
  if (lowerFeature.includes('time') || lowerFeature.includes('schedule') || lowerFeature.includes('24/7')) {
    return <Clock size={16} className="text-blue-400" />;
  }
  if (lowerFeature.includes('team') || lowerFeature.includes('collaboration') || lowerFeature.includes('share')) {
    return <Users size={16} className="text-purple-400" />;
  }
  if (lowerFeature.includes('premium') || lowerFeature.includes('advanced') || lowerFeature.includes('pro')) {
    return <Star size={16} className="text-orange-400" />;
  }
  
  // Default check icon
  return <Check size={16} className="text-green-400" />;
};

export function ToolFeaturesList({ features }: ToolFeaturesListProps) {
  return (
    <section className="bg-zinc-800 border border-black rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-200">
      <h2 className="text-2xl font-bold text-white mb-6">Key Features</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {features.map((feature, index) => (
          <div
            key={index}
            className="flex items-start gap-3 p-3 rounded-lg bg-zinc-700/50 hover:bg-zinc-700 transition-colors duration-200"
          >
            <div className="flex-shrink-0 mt-0.5">
              {getFeatureIcon(feature, index)}
            </div>
            <div className="flex-1">
              <p className="text-white text-sm leading-relaxed">
                {feature}
              </p>
            </div>
          </div>
        ))}
      </div>
      

    </section>
  );
}
