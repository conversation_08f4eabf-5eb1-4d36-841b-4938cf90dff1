'use client';

import React, { useEffect, useState } from 'react';
import { TooltipData } from '@/lib/types';

interface TooltipProps {
  tooltip: TooltipData;
  triggerType?: 'title' | 'search-icon';
}

export function Tooltip({ tooltip, triggerType = 'title' }: TooltipProps) {
  const [style, setStyle] = useState<React.CSSProperties>({});

  useEffect(() => {
    if (!tooltip.targetRect) return;

    const { targetRect, position } = tooltip;
    const tooltipWidth = triggerType === 'search-icon' ? 180 : 200; // Smaller width for search icon tooltips
    const tooltipHeight = 40; // Approximate tooltip height
    const offset = triggerType === 'search-icon' ? 6 : 10; // Smaller offset for search icons

    let top = 0;
    let left = 0;

    // Find the closest card container to constrain tooltip within it
    const targetElement = document.elementFromPoint(
      targetRect.left + targetRect.width / 2,
      targetRect.top + targetRect.height / 2
    );

    const cardContainer = targetElement?.closest('[role="region"]') as HTMLElement;
    let cardRect = null;

    if (cardContainer) {
      cardRect = cardContainer.getBoundingClientRect();
    }

    // Enhanced positioning logic for different trigger types
    if (cardRect) {
      const cardPadding = 16; // Account for card padding

      if (triggerType === 'search-icon') {
        // For search icon tooltips (triggered by list item), position above the list item
        // but keep tooltip well within card boundaries
        top = targetRect.top - tooltipHeight - offset;
        left = targetRect.left + cardPadding;

        // Ensure tooltip doesn't exceed card width
        const maxLeft = cardRect.right - tooltipWidth - cardPadding;
        if (left > maxLeft) {
          left = maxLeft;
        }

        // If not enough space above, position below but still within card
        if (top < cardRect.top + cardPadding) {
          top = targetRect.bottom + offset;
          // Make sure it doesn't go below card
          if (top + tooltipHeight > cardRect.bottom - cardPadding) {
            top = cardRect.bottom - tooltipHeight - cardPadding;
          }
        }
      } else {
        // For title tooltips, use existing logic
        top = targetRect.top - tooltipHeight - offset;
        left = targetRect.left + targetRect.width / 2 - tooltipWidth / 2;

        // Horizontal constraints - keep within card boundaries
        if (left < cardRect.left + cardPadding) {
          left = cardRect.left + cardPadding;
        }
        if (left + tooltipWidth > cardRect.right - cardPadding) {
          left = cardRect.right - tooltipWidth - cardPadding;
        }

        // Vertical constraint - ensure tooltip doesn't go above card
        if (top < cardRect.top + cardPadding) {
          // If not enough space above, position below the target
          top = targetRect.bottom + offset;
          // Make sure it doesn't go below card
          if (top + tooltipHeight > cardRect.bottom - cardPadding) {
            top = cardRect.bottom - tooltipHeight - cardPadding;
          }
        }
      }
    } else {
      // Fallback positioning for non-card tooltips
      switch (position) {
        case 'top':
          top = targetRect.top - tooltipHeight - offset;
          left = targetRect.left + targetRect.width / 2 - tooltipWidth / 2;
          break;
        case 'bottom':
          top = targetRect.bottom + offset;
          left = targetRect.left + targetRect.width / 2 - tooltipWidth / 2;
          break;
        case 'left':
          top = targetRect.top + targetRect.height / 2 - tooltipHeight / 2;
          left = targetRect.left - tooltipWidth - offset;
          break;
        case 'right':
          top = targetRect.top + targetRect.height / 2 - tooltipHeight / 2;
          left = targetRect.right + offset;
          break;
      }

      // Viewport constraints
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      if (left < 10) left = 10;
      if (left + tooltipWidth > viewportWidth - 10) left = viewportWidth - tooltipWidth - 10;
      if (top < 10) top = 10;
      if (top + tooltipHeight > viewportHeight - 10) top = viewportHeight - tooltipHeight - 10;
    }

    const finalStyle = {
      position: 'fixed' as const,
      top: `${top}px`,
      left: `${left}px`,
      zIndex: 9999,
      pointerEvents: 'none' as const,
    };

    setStyle(finalStyle);
  }, [tooltip]);

  return (
    <div
      style={{
        ...style,
        fontFamily: 'Roboto, sans-serif',
        fontSize: '14px',
        fontWeight: '400',
        lineHeight: '16.8px',
        letterSpacing: 'normal'
      }}
      className={`bg-zinc-800 text-white px-3 py-2 rounded-lg shadow-2xl border border-zinc-600 opacity-100 backdrop-blur-sm ${
        triggerType === 'search-icon' ? 'max-w-[180px] text-xs' : 'max-w-xs text-sm'
      }`}
    >
      {tooltip.content}
      <div className={`tooltip-pointer ${tooltip.position}`} />
    </div>
  );
}
