'use client';

import Link from 'next/link';
import { ArrowLeft, Home, Grid3X3 } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { findCategoryBySlug, getSubcategoriesForCategory } from '@/lib/categoryUtils';
import type { AICategory } from '@/lib/types';

export default function SubcategoryNotFound() {
  const params = useParams();
  const slug = params?.slug as string;
  const subcategorySlug = params?.subcategory as string;

  const [category, setCategory] = useState<AICategory | null>(null);
  const [subcategories, setSubcategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadData() {
      if (!slug) {
        setLoading(false);
        return;
      }

      try {
        const categoryData = await findCategoryBySlug(slug);
        setCategory(categoryData);

        if (categoryData) {
          const subcategoriesData = await getSubcategoriesForCategory(categoryData.id);
          setSubcategories(subcategoriesData);
        }
      } catch (error) {
        console.error('Error loading category data:', error);
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [slug]);
  
  const subcategoryName = subcategorySlug
    ?.replace(/-/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase()) || 'Unknown';

  if (loading) {
    return (
      <div className="w-full">
        <div className="mx-auto px-4 py-16" style={{ maxWidth: 'var(--container-width)' }}>
          <div className="text-center">
            <div className="text-white">Loading...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mx-auto px-4 py-16" style={{ maxWidth: 'var(--container-width)' }}>
        <div className="text-center">
          
          {/* 404 Icon */}
          <div className="mb-8">
            <div className="inline-flex items-center justify-center w-24 h-24 bg-zinc-800 border border-zinc-700 rounded-full">
              <Grid3X3 size={40} className="text-gray-400" />
            </div>
          </div>
          
          {/* Error Message */}
          <h1 className="text-4xl font-bold text-white mb-4">Subcategory Not Found</h1>
          <p className="text-gray-400 text-lg mb-8 max-w-md mx-auto">
            Sorry, we couldn't find "{subcategoryName}" in {category?.title || 'this category'}. 
            It may have been moved or doesn't exist.
          </p>
          
          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            {category && (
              <Link
                href={`/category/${category.id}`}
                className="inline-flex items-center gap-2 bg-orange-500 hover:bg-orange-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
                style={{
                  backgroundColor: 'rgb(255, 150, 0)',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
                }}
              >
                <Grid3X3 size={18} />
                View All {category.title}
              </Link>
            )}
            
            <Link
              href="/"
              className="inline-flex items-center gap-2 bg-zinc-700 hover:bg-zinc-600 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
            >
              <Home size={18} />
              Back to Home
            </Link>
          </div>
          
          {/* Available Subcategories */}
          {category && subcategories.length > 0 && (
            <div className="max-w-4xl mx-auto mb-12">
              <h3 className="text-white font-medium mb-6 text-xl">
                Available {category.title} Subcategories
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {subcategories.slice(0, 6).map((subcategory) => {
                  const subcategorySlug = subcategory
                    .toLowerCase()
                    .replace(/[^a-z0-9\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .replace(/-+/g, '-')
                    .trim();
                  
                  const toolCount = category.tools?.filter((tool: any) =>
                    tool.subcategory?.toLowerCase() === subcategory.toLowerCase()
                  ).length || 0;
                  
                  return (
                    <Link
                      key={subcategory}
                      href={`/category/${category.id}/${subcategorySlug}`}
                      className="bg-zinc-800 border border-zinc-700 p-4 rounded-lg hover:bg-zinc-700 transition-colors duration-200 text-left group"
                    >
                      <h4 className="text-white font-medium mb-2 group-hover:text-orange-400 transition-colors duration-200">
                        {subcategory}
                      </h4>
                      <div className="text-gray-500 text-xs">
                        {toolCount} tools
                      </div>
                    </Link>
                  );
                })}
              </div>
              
              {subcategories.length > 6 && (
                <div className="mt-6">
                  <Link
                    href={`/category/${category.id}`}
                    className="text-orange-400 hover:text-orange-300 font-medium transition-colors duration-200"
                  >
                    View all {subcategories.length} subcategories →
                  </Link>
                </div>
              )}
            </div>
          )}
          
          {/* Suggestions */}
          <div className="p-6 bg-zinc-800 border border-zinc-700 rounded-lg max-w-lg mx-auto">
            <h3 className="text-white font-medium mb-4">What you can do:</h3>
            <ul className="text-gray-400 text-sm space-y-2 text-left">
              <li>• Check the URL for any typos</li>
              <li>• Browse the available subcategories above</li>
              <li>• View all tools in the main category</li>
              <li>• Use the search function to find specific tools</li>
            </ul>
          </div>
          
        </div>
      </div>
    </div>
  );
}
