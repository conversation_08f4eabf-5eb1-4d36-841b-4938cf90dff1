'use client';

import React, { useState, useCallback, memo } from 'react';
import { Search } from 'lucide-react';
import { AITool } from '@/lib/types';
import { ResponsiveImage } from '@/components/ui/ResponsiveImage';
import { Tag } from '@/components/ui/Tag';

/**
 * ToolListItem Component - Individual tool item within CategoryCard
 *
 * Features:
 * - Numbered list display with proper spacing
 * - 16x16px favicons with rounded corners
 * - Hover effects with search icon animation
 * - Tag/badge display for tool categories
 * - Tooltip integration for descriptions
 * - Keyboard navigation support
 * - Performance optimized with React.memo
 */

interface ToolListItemProps {
  tool: AITool;
  onShowTooltip: (content: string, element: HTMLElement, triggerType?: 'title' | 'search-icon') => void;
  onHideTooltip: () => void;
  index: number;
}

export const ToolListItem = memo<ToolListItemProps>(function ToolListItem({
  tool,
  onShowTooltip,
  onHideTooltip,
  index
}) {
  const [isHovered, setIsHovered] = useState(false);

  // Handle hover for the entire item (for visual effects and tooltip)
  const handleItemMouseEnter = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    setIsHovered(true);
    // Show tooltip on list item hover, but specify it's triggered by search icon for positioning
    onShowTooltip(tool.description, e.currentTarget, 'search-icon');
  }, [tool.description, onShowTooltip]);

  const handleItemMouseLeave = useCallback(() => {
    setIsHovered(false);
    onHideTooltip(); // Hide tooltip when leaving the entire item
  }, [onHideTooltip]);

  const handleClick = useCallback(() => {
    // Navigate to tool detail page if link starts with /tools/, otherwise open external link
    if (tool.link.startsWith('/tools/')) {
      window.location.href = tool.link;
    } else {
      window.open(tool.link, '_blank', 'noopener,noreferrer');
    }
  }, [tool.link]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  }, [handleClick]);

  return (
    <div
      className={`
        flex items-center gap-2.5 px-2 py-2
        cursor-pointer transition-all duration-200
        rounded-sm
        ${isHovered ? 'bg-zinc-700' : 'hover:bg-zinc-700'}
        focus-within:bg-zinc-700 focus-within:outline-none
        focus-within:ring-1 focus-within:ring-white/20
      `}
      onMouseEnter={handleItemMouseEnter}
      onMouseLeave={handleItemMouseLeave}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      role="listitem"
      tabIndex={0}
      aria-label={`${tool.name} - ${tool.description}`}
    >
      {/* Index Number */}
      <span
        className="text-gray-500 text-xs w-5 flex-shrink-0 select-none"
        style={{ fontFamily: 'Roboto, sans-serif' }}
        aria-hidden="true"
      >
        {index}.
      </span>

      {/* Tool Favicon */}
      <ResponsiveImage
        src={tool.logoUrl}
        alt={`${tool.name} logo`}
        width={16}
        height={16}
        className="rounded-sm flex-shrink-0"
      />

      {/* Tool Name */}
      <div className="flex-1 min-w-0">
        <span
          className="text-white text-sm truncate block"
          style={{ fontFamily: 'Roboto, sans-serif' }}
        >
          {tool.name}
        </span>
      </div>

      {/* Tags and Search Icon */}
      <div className="flex items-center gap-1 flex-shrink-0">
        {tool.tags?.map((tag, tagIndex) => (
          <Tag
            key={`${tool.id}-tag-${tagIndex}`}
            type={tag.type}
            label={tag.label}
          />
        ))}

        {isHovered && (
          <Search
            size={14}
            className="text-orange-400 ml-0.5 opacity-100 transition-opacity duration-200 flex-shrink-0"
            strokeWidth={2.5}
            aria-hidden="true"
          />
        )}
      </div>
    </div>
  );
});
