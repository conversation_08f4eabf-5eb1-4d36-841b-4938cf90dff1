#!/usr/bin/env tsx

/**
 * Test Environment Setup Script
 * 
 * Sets up the test environment by:
 * 1. Validating environment variables
 * 2. Migrating configuration to database if needed
 * 3. Initializing test database configuration
 * 4. Generating environment file templates
 */

import { 
  getTestEnvironment, 
  printTestEnvironmentStatus,
  validateTestEnvironment 
} from '../tests/utils/test-environment';
import { createDatabaseConfigMigrator } from '../src/lib/config/database-config-migrator';

async function setupTestEnvironment(): Promise<void> {
  console.log('🚀 Enhanced AI System - Test Environment Setup');
  console.log('==============================================\n');

  const testEnv = getTestEnvironment();
  const validation = validateTestEnvironment();

  // Print current environment status
  printTestEnvironmentStatus();

  // Check if basic requirements are met
  if (!validation.isValid) {
    console.log('❌ SETUP FAILED: Missing required environment variables');
    console.log('\n💡 To fix this issue:');
    console.log('1. Create or update your .env.local file');
    console.log('2. Add the missing required variables');
    console.log('3. Run this setup script again\n');

    // Generate template .env file
    const envTemplate = testEnv.generateTestEnvFile();
    console.log('📝 ENVIRONMENT FILE TEMPLATE:');
    console.log('============================');
    console.log(envTemplate);
    console.log('============================\n');

    process.exit(1);
  }

  console.log('✅ Environment validation passed!\n');

  // Initialize database configuration if database is available
  if (validation.availableServices.includes('Database (Supabase)')) {
    console.log('🗄️ Setting up database configuration...');
    
    try {
      const config = testEnv.getConfig();
      const migrator = await createDatabaseConfigMigrator(
        config.supabaseUrl,
        config.supabaseServiceKey
      );

      // Validate configuration table
      const tableValidation = await migrator.validateConfigurationTable();
      
      if (!tableValidation.exists) {
        console.log('⚠️ Configuration table does not exist');
        console.log('   This is expected if the migration has not been run yet');
        console.log('   Run: npm run migrate:execute');
      } else if (!tableValidation.accessible) {
        console.log(`❌ Configuration table not accessible: ${tableValidation.error}`);
      } else {
        console.log('✅ Configuration table exists and is accessible');

        // Initialize default configuration
        const migrationResult = await migrator.initializeDefaultConfiguration();
        
        if (migrationResult.success) {
          if (migrationResult.migratedCount > 0) {
            console.log(`✅ Migrated ${migrationResult.migratedCount} configuration entries to database`);
          } else {
            console.log('✅ Database configuration already initialized');
          }
        } else {
          console.log('⚠️ Database configuration migration had issues:');
          migrationResult.errors.forEach(error => {
            console.log(`   • ${error}`);
          });
        }
      }

    } catch (error: any) {
      console.log(`⚠️ Database configuration setup failed: ${error.message}`);
      console.log('   Tests will run with environment variables only');
    }
  } else {
    console.log('⚠️ Database not available - skipping database configuration setup');
  }

  // Test AI providers if available
  if (validation.availableServices.includes('OpenAI API') || 
      validation.availableServices.includes('OpenRouter API')) {
    console.log('\n🤖 Testing AI provider connectivity...');
    
    // Test OpenAI
    if (validation.availableServices.includes('OpenAI API')) {
      try {
        const config = testEnv.getConfig();
        const response = await fetch('https://api.openai.com/v1/models', {
          headers: {
            'Authorization': `Bearer ${config.openaiApiKey}`,
            'User-Agent': 'DudeAI-Test/1.0'
          }
        });

        if (response.ok) {
          console.log('✅ OpenAI API connection successful');
        } else {
          console.log(`⚠️ OpenAI API connection failed: ${response.status} ${response.statusText}`);
        }
      } catch (error: any) {
        console.log(`⚠️ OpenAI API test failed: ${error.message}`);
      }
    }

    // Test OpenRouter
    if (validation.availableServices.includes('OpenRouter API')) {
      try {
        const config = testEnv.getConfig();
        const response = await fetch('https://openrouter.ai/api/v1/models', {
          headers: {
            'Authorization': `Bearer ${config.openrouterApiKey}`,
            'User-Agent': 'DudeAI-Test/1.0'
          }
        });

        if (response.ok) {
          console.log('✅ OpenRouter API connection successful');
        } else {
          console.log(`⚠️ OpenRouter API connection failed: ${response.status} ${response.statusText}`);
        }
      } catch (error: any) {
        console.log(`⚠️ OpenRouter API test failed: ${error.message}`);
      }
    }
  }

  // Test admin API if available
  if (validation.availableServices.includes('Admin API')) {
    console.log('\n🔐 Testing admin API access...');
    
    try {
      const baseUrl = testEnv.getConfig().baseUrl || 'http://localhost:3000';
      const headers = testEnv.getTestHeaders();
      
      const response = await fetch(`${baseUrl}/api/admin/config`, {
        method: 'GET',
        headers
      });

      if (response.ok) {
        console.log('✅ Admin API access successful');
      } else {
        console.log(`⚠️ Admin API access failed: ${response.status} ${response.statusText}`);
        console.log('   This is expected if the server is not running');
      }
    } catch (error: any) {
      console.log(`⚠️ Admin API test failed: ${error.message}`);
      console.log('   This is expected if the server is not running');
    }
  }

  // Generate test summary
  console.log('\n📊 TEST ENVIRONMENT SETUP SUMMARY');
  console.log('==================================');
  console.log(`Environment Status: ${validation.isValid ? '✅ Valid' : '❌ Invalid'}`);
  console.log(`Available Services: ${validation.availableServices.length}`);
  
  validation.availableServices.forEach(service => {
    console.log(`   ✅ ${service}`);
  });

  if (validation.missingOptional.length > 0) {
    console.log(`Missing Optional Services: ${validation.missingOptional.length}`);
    validation.missingOptional.forEach(service => {
      console.log(`   ⚠️ ${service}`);
    });
  }

  console.log('\n💡 NEXT STEPS:');
  if (validation.availableServices.length >= 2) {
    console.log('   🧪 Run tests: npm run test:system');
    console.log('   🔬 Run comprehensive tests: npm run test:comprehensive');
  } else {
    console.log('   🔧 Configure additional services for full testing capabilities');
    console.log('   📖 See documentation for service setup instructions');
  }

  if (validation.missingOptional.length > 0) {
    console.log('   ⚙️ Configure optional services for enhanced testing:');
    validation.missingOptional.forEach(service => {
      console.log(`      • ${service}`);
    });
  }

  console.log('\n🎉 Test environment setup completed!');
  console.log('==================================\n');
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Test Environment Setup Script

Usage:
  npm run setup:test-env
  tsx scripts/setup-test-environment.ts

Options:
  --help, -h    Show this help message

This script will:
1. Validate environment variables from .env.local
2. Set up database configuration if available
3. Test external service connectivity
4. Generate environment file templates if needed
5. Provide setup recommendations

Environment Variables Required:
- NEXT_PUBLIC_SUPABASE_URL (required)
- SUPABASE_SERVICE_ROLE_KEY (required)

Environment Variables Optional:
- ADMIN_API_KEY (for admin API testing)
- OPENAI_API_KEY (for OpenAI testing)
- OPENROUTER_API_KEY (for OpenRouter testing)
- SCRAPE_DO_API_KEY (for scraping testing)
- JWT_SECRET (for security testing)
- ENCRYPTION_KEY (for encryption testing)
`);
  process.exit(0);
}

// Run setup
setupTestEnvironment().catch((error) => {
  console.error('❌ Test environment setup failed:', error);
  process.exit(1);
});
