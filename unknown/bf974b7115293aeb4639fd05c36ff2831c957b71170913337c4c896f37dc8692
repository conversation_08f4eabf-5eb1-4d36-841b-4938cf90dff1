/**
 * Scraping Configuration for Enhanced AI System
 * Centralized configuration for scrape.do integration and cost optimization
 */

import { MultiPageScrapingConfig, ScrapeOptions } from '../scraping/types';

export const SCRAPING_CONFIG = {
  // API Configuration
  api: {
    baseUrl: process.env.SCRAPE_DO_BASE_URL || 'https://api.scrape.do',
    apiKey: process.env.SCRAPE_DO_API_KEY || '8e7e405ff81145c4afe447610ddb9a7f785f494dddc',
    timeout: parseInt(process.env.SCRAPE_DO_TIMEOUT || '30000'),
    retryAttempts: parseInt(process.env.SCRAPE_DO_RETRY_ATTEMPTS || '3'),
    retryDelay: 2000
  },

  // Cost Optimization Settings
  costOptimization: {
    enabled: true,
    targetSavingsPercentage: 60, // Target 60% cost reduction
    creditThreshold: 100, // Minimum credits before multi-page scraping
    maxCreditsPerTool: 50, // Maximum credits to spend per tool
    
    // Pattern-based optimization
    neverEnhancePatterns: [
      /wikipedia\.org/i,
      /github\.com/i,
      /stackoverflow\.com/i,
      /reddit\.com/i,
      /medium\.com/i,
      /dev\.to/i,
      /docs\./i,
      /blog\./i,
      /news\./i,
      /\.edu/i
    ],
    
    alwaysEnhancePatterns: [
      /claude\.ai/i,
      /chat\.openai\.com/i,
      /bard\.google\.com/i,
      /notion\.so/i,
      /figma\.com/i,
      /canva\.com/i,
      /miro\.com/i,
      /discord\.com/i,
      /app\./i,
      /dashboard\./i,
      /admin\./i
    ]
  },

  // Default scraping options
  defaultOptions: {
    basic: {
      useResidentialProxy: false,
      enableJSRendering: false,
      outputFormat: 'markdown',
      blockResources: true,
      timeout: 15000,
      customWaitTime: 1000,
      deviceType: 'desktop'
    } as ScrapeOptions,

    enhanced: {
      useResidentialProxy: false,
      enableJSRendering: true,
      outputFormat: 'markdown',
      waitCondition: 'networkidle2',
      customWaitTime: 3000,
      blockResources: true,
      timeout: 45000,
      deviceType: 'desktop'
    } as ScrapeOptions,

    premium: {
      useResidentialProxy: true,
      enableJSRendering: true,
      outputFormat: 'markdown',
      waitCondition: 'networkidle2',
      customWaitTime: 5000,
      blockResources: true,
      timeout: 60000,
      geoTargeting: 'us',
      deviceType: 'desktop'
    } as ScrapeOptions
  },

  // Multi-page scraping configuration
  multiPageScraping: {
    enabled: true,
    mode: 'conditional',
    maxPagesPerTool: 4,
    creditThreshold: 100,

    pageTypes: {
      pricing: {
        enabled: true,
        priority: 'high',
        patterns: [
          '/pricing', '/price', '/plans', '/subscription',
          '/cost', '/buy', '/purchase', '/upgrade', '/billing'
        ],
        selectors: [
          '.pricing', '.plans', '.subscription', '.billing',
          '[class*="price"]', '[id*="pricing"]', '[class*="plan"]'
        ],
        required: true
      },
      faq: {
        enabled: true,
        priority: 'medium',
        patterns: [
          '/faq', '/help', '/support', '/questions',
          '/q-and-a', '/frequently-asked', '/helpdesk'
        ],
        selectors: [
          '.faq', '.help', '.support', '.questions',
          '[class*="faq"]', '[id*="faq"]', '[class*="help"]'
        ],
        required: false
      },
      features: {
        enabled: true,
        priority: 'high',
        patterns: [
          '/features', '/capabilities', '/functionality',
          '/what-we-do', '/services', '/product'
        ],
        selectors: [
          '.features', '.capabilities', '.functionality',
          '[class*="feature"]', '[id*="features"]', '[class*="product"]'
        ],
        required: true
      },
      about: {
        enabled: true,
        priority: 'low',
        patterns: [
          '/about', '/about-us', '/company',
          '/story', '/mission', '/team', '/who-we-are'
        ],
        selectors: [
          '.about', '.company', '.story', '.mission',
          '[class*="about"]', '[id*="about"]', '[class*="company"]'
        ],
        required: false
      }
    },

    fallbackStrategy: {
      searchInMainPage: true,
      useNavigation: true,
      useSitemap: false
    }
  } as MultiPageScrapingConfig,

  // Media collection settings
  mediaCollection: {
    enabled: true,
    prioritizeFavicon: true,
    captureScreenshots: true,
    fullPageScreenshots: false, // Use standard viewport only
    maxImages: 10,
    imageQuality: 80,
    
    faviconSources: [
      'link[rel="icon"]',
      'link[rel="shortcut icon"]',
      'link[rel="apple-touch-icon"]',
      'link[rel="apple-touch-icon-precomposed"]'
    ],

    ogImageSources: [
      'meta[property="og:image"]',
      'meta[name="twitter:image"]',
      'meta[property="fb:image"]'
    ]
  },

  // Content processing settings
  contentProcessing: {
    maxContentLength: 50000, // ~12-15K tokens for AI processing
    removeNavigation: true,
    removeFooter: true,
    normalizeHeaders: true,
    preserveStructure: true,
    
    qualityThresholds: {
      minimumLength: 300,
      minimumWords: 50,
      minimumStructureScore: 60,
      minimumQualityScore: 60
    }
  },

  // Rate limiting and monitoring
  rateLimiting: {
    maxConcurrentRequests: 5,
    delayBetweenRequests: 1000, // 1 second
    delayBetweenBatches: 2000, // 2 seconds
    maxRequestsPerMinute: 30,
    
    // Usage monitoring thresholds
    warningThresholds: {
      monthlyUsage: 80, // Warn at 80% monthly usage
      concurrentUsage: 90 // Warn at 90% concurrent usage
    }
  },

  // Error handling and retry logic
  errorHandling: {
    maxRetries: 3,
    retryDelay: 2000,
    backoffMultiplier: 2,
    maxRetryDelay: 10000,
    
    retryableErrors: [
      'TIMEOUT',
      'NETWORK_ERROR',
      'SERVER_ERROR'
    ],
    
    fallbackStrategies: {
      usePuppeteer: false, // Disable Puppeteer fallback for now
      useBasicScraping: true,
      skipOnFailure: false
    }
  },

  // Development and testing settings
  development: {
    enableLogging: true,
    logLevel: 'info', // 'debug', 'info', 'warn', 'error'
    saveScrapedContent: false, // Save to files for debugging
    testMode: false, // Use test endpoints
    
    testUrls: [
      'https://httpbin.org/html',
      'https://example.com',
      'https://github.com/microsoft/vscode'
    ]
  }
};

// Helper functions for configuration access
export function getScrapeOptions(type: 'basic' | 'enhanced' | 'premium'): ScrapeOptions {
  return { ...SCRAPING_CONFIG.defaultOptions[type] };
}

export function getMultiPageConfig(): MultiPageScrapingConfig {
  return { ...SCRAPING_CONFIG.multiPageScraping };
}

export function shouldEnhanceUrl(url: string): boolean | null {
  const { neverEnhancePatterns, alwaysEnhancePatterns } = SCRAPING_CONFIG.costOptimization;
  
  if (neverEnhancePatterns.some(pattern => pattern.test(url))) {
    return false; // Never enhance
  }
  
  if (alwaysEnhancePatterns.some(pattern => pattern.test(url))) {
    return true; // Always enhance
  }
  
  return null; // Unknown - use intelligent detection
}

export function getCreditThreshold(): number {
  return SCRAPING_CONFIG.costOptimization.creditThreshold;
}

export function getMaxCreditsPerTool(): number {
  return SCRAPING_CONFIG.costOptimization.maxCreditsPerTool;
}

// Export the main configuration
export default SCRAPING_CONFIG;
