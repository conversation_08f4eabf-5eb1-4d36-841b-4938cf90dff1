/**
 * Performance Monitor Service
 * 
 * Collects and tracks system performance metrics including:
 * - API response times
 * - Database query performance
 * - Memory usage
 * - Job processing speeds
 * - AI provider response times
 */

import { createClient } from '@supabase/supabase-js';
import { EventEmitter } from 'events';

export interface PerformanceMetric {
  id?: string;
  metric_type: 'api_response' | 'database_query' | 'memory_usage' | 'job_processing' | 'ai_provider' | 'scraping';
  metric_name: string;
  value: number;
  unit: 'ms' | 'mb' | 'count' | 'percent' | 'bytes';
  endpoint?: string;
  operation?: string;
  provider?: string;
  metadata?: Record<string, any>;
  timestamp: string;
}

export interface PerformanceStats {
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  throughput: number;
  errorRate: number;
  memoryUsage: number;
  cpuUsage?: number;
}

export interface PerformanceSummary {
  timeRange: string;
  totalRequests: number;
  stats: PerformanceStats;
  topSlowEndpoints: Array<{
    endpoint: string;
    averageTime: number;
    requestCount: number;
  }>;
  trends: {
    responseTimeTrend: 'improving' | 'degrading' | 'stable';
    throughputTrend: 'increasing' | 'decreasing' | 'stable';
    errorRateTrend: 'improving' | 'degrading' | 'stable';
  };
  recommendations: string[];
}

export class PerformanceMonitor extends EventEmitter {
  private supabase: any;
  private metrics: PerformanceMetric[] = [];
  private isCollecting = false;
  private collectionInterval?: NodeJS.Timeout;
  private readonly maxMetricsInMemory = 1000;

  constructor() {
    super();
    
    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    this.supabase = createClient(supabaseUrl, supabaseServiceKey);
  }

  /**
   * Start performance monitoring
   */
  async startMonitoring(intervalMs: number = 60000): Promise<void> {
    if (this.isCollecting) return;

    this.isCollecting = true;
    console.log('Performance monitoring started');

    // Collect system metrics periodically
    this.collectionInterval = setInterval(async () => {
      await this.collectSystemMetrics();
    }, intervalMs);

    // Initial collection
    await this.collectSystemMetrics();
    this.emit('monitoring_started');
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    if (!this.isCollecting) return;

    this.isCollecting = false;
    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
      this.collectionInterval = undefined;
    }

    console.log('Performance monitoring stopped');
    this.emit('monitoring_stopped');
  }

  /**
   * Record a performance metric
   */
  async recordMetric(metric: Omit<PerformanceMetric, 'timestamp'>): Promise<void> {
    const fullMetric: PerformanceMetric = {
      ...metric,
      timestamp: new Date().toISOString()
    };

    // Add to in-memory collection
    this.metrics.push(fullMetric);

    // Persist to database
    try {
      const { error } = await this.supabase
        .from('performance_metrics')
        .insert(fullMetric);

      if (error) {
        console.error('Failed to persist performance metric:', error);
      }
    } catch (error) {
      console.error('Error persisting performance metric:', error);
    }

    // Cleanup old metrics from memory
    if (this.metrics.length > this.maxMetricsInMemory) {
      this.metrics = this.metrics.slice(-this.maxMetricsInMemory);
    }

    this.emit('metric_recorded', fullMetric);
  }

  /**
   * Record API response time
   */
  async recordApiResponse(endpoint: string, responseTime: number, statusCode: number): Promise<void> {
    await this.recordMetric({
      metric_type: 'api_response',
      metric_name: 'response_time',
      value: responseTime,
      unit: 'ms',
      endpoint,
      metadata: { statusCode }
    });
  }

  /**
   * Record database query performance
   */
  async recordDatabaseQuery(operation: string, queryTime: number, recordCount?: number): Promise<void> {
    await this.recordMetric({
      metric_type: 'database_query',
      metric_name: 'query_time',
      value: queryTime,
      unit: 'ms',
      operation,
      metadata: { recordCount }
    });
  }

  /**
   * Record memory usage
   */
  async recordMemoryUsage(usage: number): Promise<void> {
    await this.recordMetric({
      metric_type: 'memory_usage',
      metric_name: 'heap_used',
      value: usage,
      unit: 'mb'
    });
  }

  /**
   * Record job processing time
   */
  async recordJobProcessing(jobType: string, processingTime: number, success: boolean): Promise<void> {
    await this.recordMetric({
      metric_type: 'job_processing',
      metric_name: 'processing_time',
      value: processingTime,
      unit: 'ms',
      operation: jobType,
      metadata: { success }
    });
  }

  /**
   * Record AI provider response time
   */
  async recordAIProviderResponse(provider: string, responseTime: number, tokenCount?: number): Promise<void> {
    await this.recordMetric({
      metric_type: 'ai_provider',
      metric_name: 'response_time',
      value: responseTime,
      unit: 'ms',
      provider,
      metadata: { tokenCount }
    });
  }

  /**
   * Collect system metrics
   */
  private async collectSystemMetrics(): Promise<void> {
    try {
      // Memory usage
      const memUsage = process.memoryUsage();
      await this.recordMemoryUsage(memUsage.heapUsed / 1024 / 1024); // Convert to MB

      // Database connection health
      const dbStartTime = Date.now();
      try {
        await this.supabase.from('tools').select('id').limit(1);
        const dbResponseTime = Date.now() - dbStartTime;
        await this.recordDatabaseQuery('health_check', dbResponseTime);
      } catch (error) {
        console.error('Database health check failed:', error);
      }

    } catch (error) {
      console.error('Error collecting system metrics:', error);
    }
  }

  /**
   * Get performance metrics for a time range
   */
  async getMetrics(
    timeRange: string = '1h',
    metricType?: PerformanceMetric['metric_type']
  ): Promise<PerformanceMetric[]> {
    try {
      const hoursBack = this.parseTimeRange(timeRange);
      const startTime = new Date(Date.now() - hoursBack * 60 * 60 * 1000).toISOString();

      let query = this.supabase
        .from('performance_metrics')
        .select('*')
        .gte('timestamp', startTime)
        .order('timestamp', { ascending: false });

      if (metricType) {
        query = query.eq('metric_type', metricType);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Failed to fetch performance metrics:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching performance metrics:', error);
      return [];
    }
  }

  /**
   * Generate performance summary
   */
  async getPerformanceSummary(timeRange: string = '1h'): Promise<PerformanceSummary> {
    const metrics = await this.getMetrics(timeRange);
    const apiMetrics = metrics.filter(m => m.metric_type === 'api_response');

    // Calculate statistics
    const responseTimes = apiMetrics.map(m => m.value);
    const totalRequests = responseTimes.length;

    const stats: PerformanceStats = {
      averageResponseTime: this.calculateAverage(responseTimes),
      p95ResponseTime: this.calculatePercentile(responseTimes, 95),
      p99ResponseTime: this.calculatePercentile(responseTimes, 99),
      throughput: totalRequests / this.parseTimeRange(timeRange),
      errorRate: this.calculateErrorRate(apiMetrics),
      memoryUsage: this.getLatestMemoryUsage(metrics)
    };

    // Find slow endpoints
    const topSlowEndpoints = this.getTopSlowEndpoints(apiMetrics);

    // Calculate trends
    const trends = await this.calculateTrends(timeRange);

    // Generate recommendations
    const recommendations = this.generateRecommendations(stats, topSlowEndpoints);

    return {
      timeRange,
      totalRequests,
      stats,
      topSlowEndpoints,
      trends,
      recommendations
    };
  }

  /**
   * Parse time range string to hours
   */
  private parseTimeRange(timeRange: string): number {
    const match = timeRange.match(/^(\d+)([hmd])$/);
    if (!match) return 1; // Default to 1 hour

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 'm': return value / 60; // minutes to hours
      case 'h': return value;      // hours
      case 'd': return value * 24; // days to hours
      default: return 1;
    }
  }

  /**
   * Calculate average of numbers
   */
  private calculateAverage(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, n) => sum + n, 0) / numbers.length;
  }

  /**
   * Calculate percentile
   */
  private calculatePercentile(numbers: number[], percentile: number): number {
    if (numbers.length === 0) return 0;
    
    const sorted = [...numbers].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[Math.max(0, index)];
  }

  /**
   * Calculate error rate from API metrics
   */
  private calculateErrorRate(apiMetrics: PerformanceMetric[]): number {
    if (apiMetrics.length === 0) return 0;
    
    const errorCount = apiMetrics.filter(m => 
      m.metadata?.statusCode && m.metadata.statusCode >= 400
    ).length;
    
    return (errorCount / apiMetrics.length) * 100;
  }

  /**
   * Get latest memory usage
   */
  private getLatestMemoryUsage(metrics: PerformanceMetric[]): number {
    const memoryMetrics = metrics
      .filter(m => m.metric_type === 'memory_usage')
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    return memoryMetrics.length > 0 ? memoryMetrics[0].value : 0;
  }

  /**
   * Get top slow endpoints
   */
  private getTopSlowEndpoints(apiMetrics: PerformanceMetric[]): Array<{
    endpoint: string;
    averageTime: number;
    requestCount: number;
  }> {
    const endpointStats = new Map<string, { times: number[]; count: number }>();

    apiMetrics.forEach(metric => {
      if (metric.endpoint) {
        const stats = endpointStats.get(metric.endpoint) || { times: [], count: 0 };
        stats.times.push(metric.value);
        stats.count++;
        endpointStats.set(metric.endpoint, stats);
      }
    });

    return Array.from(endpointStats.entries())
      .map(([endpoint, stats]) => ({
        endpoint,
        averageTime: this.calculateAverage(stats.times),
        requestCount: stats.count
      }))
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, 5);
  }

  /**
   * Calculate performance trends
   */
  private async calculateTrends(timeRange: string): Promise<PerformanceSummary['trends']> {
    // This is a simplified implementation
    // In a real system, you'd compare current period with previous period
    return {
      responseTimeTrend: 'stable',
      throughputTrend: 'stable',
      errorRateTrend: 'stable'
    };
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(
    stats: PerformanceStats,
    slowEndpoints: Array<{ endpoint: string; averageTime: number; requestCount: number }>
  ): string[] {
    const recommendations: string[] = [];

    if (stats.averageResponseTime > 1000) {
      recommendations.push('Average response time is high (>1s). Consider optimizing slow endpoints.');
    }

    if (stats.errorRate > 5) {
      recommendations.push(`Error rate is ${stats.errorRate.toFixed(1)}%. Investigate failing requests.`);
    }

    if (stats.memoryUsage > 500) {
      recommendations.push('Memory usage is high (>500MB). Consider implementing memory optimization.');
    }

    if (slowEndpoints.length > 0 && slowEndpoints[0].averageTime > 2000) {
      recommendations.push(`Endpoint ${slowEndpoints[0].endpoint} is very slow (${slowEndpoints[0].averageTime.toFixed(0)}ms). Prioritize optimization.`);
    }

    if (recommendations.length === 0) {
      recommendations.push('System performance is within acceptable ranges.');
    }

    return recommendations;
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();
