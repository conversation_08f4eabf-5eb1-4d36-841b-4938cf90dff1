'use client';

import React from 'react';
import { Header } from './Header';
import { Footer } from './Footer';
import { useSearchContext } from '@/providers/SearchProvider';

interface LayoutContentProps {
  children: React.ReactNode;
}

export function LayoutContent({ children }: LayoutContentProps) {
  const {
    searchTerm,
    isSearchDropdownVisible,
    handleSearchFocus,
    handleSearchBlur,
    handleSearchChange,
    handleTopSearchClick,
  } = useSearchContext();

  return (
    <div className="min-h-screen w-full flex flex-col">
      <Header
        searchTerm={searchTerm}
        isSearchDropdownVisible={isSearchDropdownVisible}
        onSearchChange={handleSearchChange}
        onSearchFocus={handleSearchFocus}
        onSearchBlur={handleSearchBlur}
        onTopSearchClick={handleTopSearchClick}
      />
      <main className="flex-1 w-full relative z-10">
        {children}
      </main>
      <Footer />
    </div>
  );
}
