import { NextRequest, NextResponse } from 'next/server';
import { submitTool } from '@/lib/supabase';
import { getJobQueue } from '@/lib/jobs/queue';
import { JobType, JobPriority } from '@/lib/jobs/types';

export async function POST(request: NextRequest) {
  try {
    const submissionData = await request.json();

    // Validate required fields
    const { name, url, description, category, submitterEmail } = submissionData;
    
    if (!name || !url || !description || !category || !submitterEmail) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: name, url, description, category, submitterEmail' },
        { status: 400 }
      );
    }

    // Validate URL format
    try {
      new URL(url);
    } catch {
      return NextResponse.json(
        { success: false, error: 'Invalid URL format' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(submitterEmail)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    await submitTool(submissionData);

    // Trigger automated tool processing (if enabled)
    if (process.env.JOB_QUEUE_ENABLED === 'true') {
      try {
        const queue = getJobQueue();
        await queue.add(
          JobType.TOOL_SUBMISSION,
          {
            url: submissionData.url,
            name: submissionData.name,
            description: submissionData.description,
            category: submissionData.category,
            submitterEmail: submissionData.submitterEmail,
            submitterName: submissionData.submitterName,
          },
          {
            priority: JobPriority.NORMAL,
          }
        );
        console.log('Tool processing job queued successfully');
      } catch (jobError) {
        console.error('Failed to queue tool processing job:', jobError);
        // Don't fail the request if job queueing fails
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Tool submitted successfully. It will be reviewed and published if approved.',
    });

  } catch (error) {
    console.error('Tool submission error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to submit tool' },
      { status: 500 }
    );
  }
}
