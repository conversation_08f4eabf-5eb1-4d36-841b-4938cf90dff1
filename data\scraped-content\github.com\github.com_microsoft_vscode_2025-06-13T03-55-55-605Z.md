---
url: https://github.com/microsoft/vscode
scraped_at: 2025-06-13T03:55:49.274Z
stored_at: 2025-06-13T03:55:55.605Z
credits_used: 1
optimization_strategy: Datacenter Proxy (Cost-Optimized)
content_scenario: content_sufficient
content_quality: 70%
favicon: available
og_images: 0
screenshot: available
---

# Main Content

- Meta: charset: utf-8
- Title: GitHub - microsoft/vscode: Visual Studio Code
- Meta: name: route-pattern, content: /:user_id/:repository
- Meta: name: route-controller, content: files
- Meta: name: route-action, content: disambiguate
- Meta: name: fetch-nonce, content: v2:65e813aa-9ca8-457f-663e-7dd6a733216d
- Meta: name: current-catalog-service-hash, content: f3abb0cc802f3d7b95fc8762b94bdcb13bf39634c40c357301c4aa1d67a256fb
- Meta: content: 8743:1092F9:2001FFF:211BA24:684BA145, data-pjax-transient: true, name: request-id
- Meta: name: html-safe-nonce, content: ab3c0a5daedb5062c04b66af81fdb5a4a9ea94eae535eaa604182eaae4a40d09, data-pjax-transient: true
- Meta: name: visitor-payload, content: eyJyZWZlcnJlciI6Imh0dHBzOi8vZ2l0aHViLmNvbSIsInJlcXVlc3RfaWQiOiI4NzQzOjEwOTJGOToyMDAxRkZGOjIxMUJBMjQ6Njg0QkExNDUiLCJ2aXNpdG9yX2lkIjoiODQ5Mzk5Mjg0NTQ5MjU5Mjk2NiIsInJlZ2lvbl9lZGdlIjoiZnJhIiwicmVnaW9uX3JlbmRlciI6ImZyYSJ9, data-pjax-transient: true
- Meta: name: visitor-hmac, content: 4591a3785b9b2b1d28471f90d8b2a196b10930416cfd1674ce36c9ab6832d690, data-pjax-transient: true
- Meta: content: repository:41881900, name: hovercard-subject-tag
- Meta: name: github-keyboard-shortcuts, content: repository,copilot, data-turbo-transient: true
- Meta: value: repo_source, name: selected-link
- Meta: name: google-site-verification, content: Apib7-x98H0j5cPqHWwSMm6dNU4GmODRoqxLiDzdx9I
- Meta: name: octolytics-url, content: https://collector.github.com/github/collect
- Meta: name: analytics-location, content: /<user-name>/<repo-name>, data-turbo-transient: true
- Meta: name: user-login
- Meta: name: viewport, content: width=device-width
- Meta: name: description, content: Visual Studio Code. Contribute to microsoft/vscode development by creating an account on GitHub.
- Meta: content: ****************, property: fb:app_id
- Meta: name: apple-itunes-app, content: app-id=**********, app-argument=https://github.com/microsoft/vscode
- Meta: name: twitter:image, content: https://opengraph.githubassets.com/6aabdac6fad35f1791801ccf06a97254c01bcf4ebf46769c5a0dac7f4965a33b/microsoft/vscode
- Meta: name: twitter:site, content: @github
- Meta: name: twitter:card, content: summary_large_image
- Meta: name: twitter:title, content: GitHub - microsoft/vscode: Visual Studio Code
- Meta: name: twitter:description, content: Visual Studio Code. Contribute to microsoft/vscode development by creating an account on GitHub.
- Meta: property: og:image, content: https://opengraph.githubassets.com/6aabdac6fad35f1791801ccf06a97254c01bcf4ebf46769c5a0dac7f4965a33b/microsoft/vscode
- Meta: property: og:image:alt, content: Visual Studio Code. Contribute to microsoft/vscode development by creating an account on GitHub.
- Meta: property: og:image:width, content: 1200
- Meta: property: og:image:height, content: 600
- Meta: property: og:site_name, content: GitHub
- Meta: property: og:type, content: object
- Meta: property: og:title, content: GitHub - microsoft/vscode: Visual Studio Code
- Meta: property: og:url, content: https://github.com/microsoft/vscode
- Meta: property: og:description, content: Visual Studio Code. Contribute to microsoft/vscode development by creating an account on GitHub.
- Meta: content: github.com, name: hostname
- Meta: name: expected-hostname, content: github.com
- Meta: content: 270dc8f2c3caa5e4a13b4e6c9132a3203441ea5385f6033eb24eebc0a68d33d0, data-turbo-track: reload, http-equiv: x-pjax-version
- Meta: http-equiv: x-pjax-csp-version, content: 352e51c42d5f5727a7c545752bf34d1f83f40219e7036c6959817149a51651bc, data-turbo-track: reload
- Meta: http-equiv: x-pjax-css-version, content: 4e8d942c1e58aee61c7c4c8c9889c1c76351814c062c507d76d301fb8bcb7735, data-turbo-track: reload
- Meta: http-equiv: x-pjax-js-version, content: e1b90867113e8eba823025584fd35b194e8f3c0c72c4df2a982f5fdbd56fb792, data-turbo-track: reload
- Meta: name: turbo-cache-control, content: no-preview
- Meta: data-hydrostats: publish
- Meta: name: go-import, content: github.com/microsoft/vscode git https://github.com/microsoft/vscode.git
- Meta: name: octolytics-dimension-user_id, content: 6154722
- Meta: name: octolytics-dimension-user_login, content: microsoft
- Meta: name: octolytics-dimension-repository_id, content: 41881900
- Meta: name: octolytics-dimension-repository_nwo, content: microsoft/vscode
- Meta: name: octolytics-dimension-repository_public, content: true
- Meta: name: octolytics-dimension-repository_is_fork, content: false
- Meta: name: octolytics-dimension-repository_network_root_id, content: 41881900
- Meta: name: octolytics-dimension-repository_network_root_nwo, content: microsoft/vscode
- Meta: name: turbo-body-classes, content: logged-out env-production page-responsive
- Meta: name: browser-stats-url, content: https://api.github.com/_private/browser/stats
- Meta: name: browser-errors-url, content: https://api.github.com/_private/browser/errors
- Meta: name: release, content: 4c49132eb397fc271b508a39ee42ae10bc73415d
- Meta: name: theme-color, content: #1e2327
- Meta: name: color-scheme, content: light dark

[Skip to content](http://github.com#start-of-content)

## Navigation Menu

Toggle navigation

[Homepage](http://github.com/)

[Sign in](http://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Fmicrosoft%2Fvscode)

Appearance settings

- Product







- [GitHub Copilot\
\
\
Write better code with AI](https://github.com/features/copilot)
- [GitHub Models\
\
New\
\
\
Manage and compare prompts](https://github.com/features/models)
- [GitHub Advanced Security\
\
\
Find and fix vulnerabilities](https://github.com/security/advanced-security)
- [Actions\
\
\
Automate any workflow](https://github.com/features/actions)
- [Codespaces\
\
\
Instant dev environments](https://github.com/features/codespaces)

- [Issues\
\
\
Plan and track work](https://github.com/features/issues)
- [Code Review\
\
\
Manage code changes](https://github.com/features/code-review)
- [Discussions\
\
\
Collaborate outside of code](https://github.com/features/discussions)
- [Code Search\
\
\
Find more, search less](https://github.com/features/code-search)

Explore

- [Why GitHub](https://github.com/why-github)
- [All features](https://github.com/features)
- [Documentation](https://docs.github.com)
- [GitHub Skills](https://skills.github.com)
- [Blog](https://github.blog)

- Solutions





By company size

- [Enterprises](https://github.com/enterprise)
- [Small and medium teams](https://github.com/team)
- [Startups](https://github.com/enterprise/startups)
- [Nonprofits](http://github.com/solutions/industry/nonprofits)

By use case

- [DevSecOps](http://github.com/solutions/use-case/devsecops)
- [DevOps](http://github.com/solutions/use-case/devops)
- [CI/CD](http://github.com/solutions/use-case/ci-cd)
- [View all use cases](http://github.com/solutions/use-case)

By industry

- [Healthcare](http://github.com/solutions/industry/healthcare)
- [Financial services](http://github.com/solutions/industry/financial-services)
- [Manufacturing](http://github.com/solutions/industry/manufacturing)
- [Government](http://github.com/solutions/industry/government)
- [View all industries](http://github.com/solutions/industry)

[View all solutions](http://github.com/solutions)

- Resources





Topics

- [AI](http://github.com/resources/articles/ai)
- [DevOps](http://github.com/resources/articles/devops)
- [Security](http://github.com/resources/articles/security)
- [Software Development](http://github.com/resources/articles/software-development)
- [View all](http://github.com/resources/articles)

Explore

- [Learning Pathways](https://resources.github.com/learn/pathways)
- [Events & Webinars](https://resources.github.com)
- [Ebooks & Whitepapers](https://github.com/resources/whitepapers)
- [Customer Stories](https://github.com/customer-stories)
- [Partners](https://partner.github.com)
- [Executive Insights](https://github.com/solutions/executive-insights)

- Open Source







- [GitHub Sponsors\
\
\
Fund open source developers](http://github.com/sponsors)

- [The ReadME Project\
\
\
GitHub community articles](https://github.com/readme)

Repositories

- [Topics](https://github.com/topics)
- [Trending](https://github.com/trending)
- [Collections](https://github.com/collections)

- Enterprise







- [Enterprise platform\
\
\
AI-powered developer platform](http://github.com/enterprise)

Available add-ons

- [GitHub Advanced Security\
\
\
Enterprise-grade security features](https://github.com/security/advanced-security)
- [Copilot for business\
\
\
Enterprise-grade AI features](http://github.com/features/copilot/copilot-business)
- [Premium Support\
\
\
Enterprise-grade 24/7 support](http://github.com/premium-support)

- [Pricing](https://github.com/pricing)

Search or jump to...

# Search code, repositories, users, issues, pull requests...

Search


Clear

[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)

# Provide feedback

We read every piece of feedback, and take your input very seriously.

Include my email address so I can be contacted

Cancel
Submit feedback

# Saved searches

## Use saved searches to filter your results more quickly

Name

Query

To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax).


Cancel
Create saved search

[Sign in](http://github.com/login?return_to=https%3A%2F%2Fgithub.com%2Fmicrosoft%2Fvscode)

[Sign up](http://github.com/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E&source=header-repo&source_repo=microsoft%2Fvscode)

Appearance settings

Resetting focus

You signed in with another tab or window. Reload to refresh your session.You signed out in another tab or window. Reload to refresh your session.You switched accounts on another tab or window. Reload to refresh your session.Dismiss alert

{{ message }}

[microsoft](http://github.com/microsoft)/ **[vscode](http://github.com/microsoft/vscode)** Public

- [Notifications](http://github.com/login?return_to=%2Fmicrosoft%2Fvscode) You must be signed in to change notification settings
- [Fork\
33k](http://github.com/login?return_to=%2Fmicrosoft%2Fvscode)
- [Star\
173k](http://github.com/login?return_to=%2Fmicrosoft%2Fvscode)


Visual Studio Code


[code.visualstudio.com](https://code.visualstudio.com "https://code.visualstudio.com")

### License

[MIT license](http://github.com/microsoft/vscode/blob/main/LICENSE.txt)

[173k\
stars](http://github.com/microsoft/vscode/stargazers) [33k\
forks](http://github.com/microsoft/vscode/forks) [Branches](http://github.com/microsoft/vscode/branches) [Tags](http://github.com/microsoft/vscode/tags) [Activity](http://github.com/microsoft/vscode/activity)

[Star](http://github.com/login?return_to=%2Fmicrosoft%2Fvscode)

[Notifications](http://github.com/login?return_to=%2Fmicrosoft%2Fvscode) You must be signed in to change notification settings

- [Code](http://github.com/microsoft/vscode)
- [Issues5k+](http://github.com/microsoft/vscode/issues)
- [Pull requests598](http://github.com/microsoft/vscode/pulls)
- [Actions](http://github.com/microsoft/vscode/actions)
- [Projects1](http://github.com/microsoft/vscode/projects)
- [Wiki](http://github.com/microsoft/vscode/wiki)
- [Security](http://github.com/microsoft/vscode/security)






[**Uh oh!**](http://github.com/microsoft/vscode/security)

[There was an error while loading.](http://github.com/microsoft/vscode/security)Please reload this page.

- [Insights](http://github.com/microsoft/vscode/pulse)

Additional navigation options

- [Code](http://github.com/microsoft/vscode)
- [Issues](http://github.com/microsoft/vscode/issues)
- [Pull requests](http://github.com/microsoft/vscode/pulls)
- [Actions](http://github.com/microsoft/vscode/actions)
- [Projects](http://github.com/microsoft/vscode/projects)
- [Wiki](http://github.com/microsoft/vscode/wiki)
- [Security](http://github.com/microsoft/vscode/security)
- [Insights](http://github.com/microsoft/vscode/pulse)

# microsoft/vscode

main

[Branches](http://github.com/microsoft/vscode/branches) [Tags](http://github.com/microsoft/vscode/tags)

[Go to Branches page](http://github.com/microsoft/vscode/branches)[Go to Tags page](http://github.com/microsoft/vscode/tags)

Go to file

Code

Open more actions menu

## Folders and files

NameName

Last commit message

Last commit date

## Latest commit

## History

[134,322 Commits](http://github.com/microsoft/vscode/commits/main/)

[.config](http://github.com/microsoft/vscode/tree/main/.config ".config")

[.config](http://github.com/microsoft/vscode/tree/main/.config ".config")

[.devcontainer](http://github.com/microsoft/vscode/tree/main/.devcontainer ".devcontainer")

[.devcontainer](http://github.com/microsoft/vscode/tree/main/.devcontainer ".devcontainer")

[.eslint-plugin-local](http://github.com/microsoft/vscode/tree/main/.eslint-plugin-local ".eslint-plugin-local")

[.eslint-plugin-local](http://github.com/microsoft/vscode/tree/main/.eslint-plugin-local ".eslint-plugin-local")

[.github](http://github.com/microsoft/vscode/tree/main/.github ".github")

[.github](http://github.com/microsoft/vscode/tree/main/.github ".github")

[.vscode](http://github.com/microsoft/vscode/tree/main/.vscode ".vscode")

[.vscode](http://github.com/microsoft/vscode/tree/main/.vscode ".vscode")

[build](http://github.com/microsoft/vscode/tree/main/build "build")

[build](http://github.com/microsoft/vscode/tree/main/build "build")

[cli](http://github.com/microsoft/vscode/tree/main/cli "cli")

[cli](http://github.com/microsoft/vscode/tree/main/cli "cli")

[extensions](http://github.com/microsoft/vscode/tree/main/extensions "extensions")

[extensions](http://github.com/microsoft/vscode/tree/main/extensions "extensions")

[remote](http://github.com/microsoft/vscode/tree/main/remote "remote")

[remote](http://github.com/microsoft/vscode/tree/main/remote "remote")

[resources](http://github.com/microsoft/vscode/tree/main/resources "resources")

[resources](http://github.com/microsoft/vscode/tree/main/resources "resources")

[scripts](http://github.com/microsoft/vscode/tree/main/scripts "scripts")

[scripts](http://github.com/microsoft/vscode/tree/main/scripts "scripts")

[src](http://github.com/microsoft/vscode/tree/main/src "src")

[src](http://github.com/microsoft/vscode/tree/main/src "src")

[test](http://github.com/microsoft/vscode/tree/main/test "test")

[test](http://github.com/microsoft/vscode/tree/main/test "test")

[.editorconfig](http://github.com/microsoft/vscode/blob/main/.editorconfig ".editorconfig")

[.editorconfig](http://github.com/microsoft/vscode/blob/main/.editorconfig ".editorconfig")

[.eslint-ignore](http://github.com/microsoft/vscode/blob/main/.eslint-ignore ".eslint-ignore")

[.eslint-ignore](http://github.com/microsoft/vscode/blob/main/.eslint-ignore ".eslint-ignore")

[.git-blame-ignore-revs](http://github.com/microsoft/vscode/blob/main/.git-blame-ignore-revs ".git-blame-ignore-revs")

[.git-blame-ignore-revs](http://github.com/microsoft/vscode/blob/main/.git-blame-ignore-revs ".git-blame-ignore-revs")

[.gitattributes](http://github.com/microsoft/vscode/blob/main/.gitattributes ".gitattributes")

[.gitattributes](http://github.com/microsoft/vscode/blob/main/.gitattributes ".gitattributes")

[.gitignore](http://github.com/microsoft/vscode/blob/main/.gitignore ".gitignore")

[.gitignore](http://github.com/microsoft/vscode/blob/main/.gitignore ".gitignore")

[.lsifrc.json](http://github.com/microsoft/vscode/blob/main/.lsifrc.json ".lsifrc.json")

[.lsifrc.json](http://github.com/microsoft/vscode/blob/main/.lsifrc.json ".lsifrc.json")

[.mailmap](http://github.com/microsoft/vscode/blob/main/.mailmap ".mailmap")

[.mailmap](http://github.com/microsoft/vscode/blob/main/.mailmap ".mailmap")

[.mention-bot](http://github.com/microsoft/vscode/blob/main/.mention-bot ".mention-bot")

[.mention-bot](http://github.com/microsoft/vscode/blob/main/.mention-bot ".mention-bot")

[.npmrc](http://github.com/microsoft/vscode/blob/main/.npmrc ".npmrc")

[.npmrc](http://github.com/microsoft/vscode/blob/main/.npmrc ".npmrc")

[.nvmrc](http://github.com/microsoft/vscode/blob/main/.nvmrc ".nvmrc")

[.nvmrc](http://github.com/microsoft/vscode/blob/main/.nvmrc ".nvmrc")

[.vscode-test.js](http://github.com/microsoft/vscode/blob/main/.vscode-test.js ".vscode-test.js")

[.vscode-test.js](http://github.com/microsoft/vscode/blob/main/.vscode-test.js ".vscode-test.js")

[CONTRIBUTING.md](http://github.com/microsoft/vscode/blob/main/CONTRIBUTING.md "CONTRIBUTING.md")

[CONTRIBUTING.md](http://github.com/microsoft/vscode/blob/main/CONTRIBUTING.md "CONTRIBUTING.md")

[CodeQL.yml](http://github.com/microsoft/vscode/blob/main/CodeQL.yml "CodeQL.yml")

[CodeQL.yml](http://github.com/microsoft/vscode/blob/main/CodeQL.yml "CodeQL.yml")

[LICENSE.txt](http://github.com/microsoft/vscode/blob/main/LICENSE.txt "LICENSE.txt")

[LICENSE.txt](http://github.com/microsoft/vscode/blob/main/LICENSE.txt "LICENSE.txt")

[README.md](http://github.com/microsoft/vscode/blob/main/README.md "README.md")

[README.md](http://github.com/microsoft/vscode/blob/main/README.md "README.md")

[SECURITY.md](http://github.com/microsoft/vscode/blob/main/SECURITY.md "SECURITY.md")

[SECURITY.md](http://github.com/microsoft/vscode/blob/main/SECURITY.md "SECURITY.md")

[ThirdPartyNotices.txt](http://github.com/microsoft/vscode/blob/main/ThirdPartyNotices.txt "ThirdPartyNotices.txt")

[ThirdPartyNotices.txt](http://github.com/microsoft/vscode/blob/main/ThirdPartyNotices.txt "ThirdPartyNotices.txt")

[cglicenses.json](http://github.com/microsoft/vscode/blob/main/cglicenses.json "cglicenses.json")

[cglicenses.json](http://github.com/microsoft/vscode/blob/main/cglicenses.json "cglicenses.json")

[cgmanifest.json](http://github.com/microsoft/vscode/blob/main/cgmanifest.json "cgmanifest.json")

[cgmanifest.json](http://github.com/microsoft/vscode/blob/main/cgmanifest.json "cgmanifest.json")

[eslint.config.js](http://github.com/microsoft/vscode/blob/main/eslint.config.js "eslint.config.js")

[eslint.config.js](http://github.com/microsoft/vscode/blob/main/eslint.config.js "eslint.config.js")

[gulpfile.js](http://github.com/microsoft/vscode/blob/main/gulpfile.js "gulpfile.js")

[gulpfile.js](http://github.com/microsoft/vscode/blob/main/gulpfile.js "gulpfile.js")

[package-lock.json](http://github.com/microsoft/vscode/blob/main/package-lock.json "package-lock.json")

[package-lock.json](http://github.com/microsoft/vscode/blob/main/package-lock.json "package-lock.json")

[package.json](http://github.com/microsoft/vscode/blob/main/package.json "package.json")

[package.json](http://github.com/microsoft/vscode/blob/main/package.json "package.json")

[product.json](http://github.com/microsoft/vscode/blob/main/product.json "product.json")

[product.json](http://github.com/microsoft/vscode/blob/main/product.json "product.json")

[tsfmt.json](http://github.com/microsoft/vscode/blob/main/tsfmt.json "tsfmt.json")

[tsfmt.json](http://github.com/microsoft/vscode/blob/main/tsfmt.json "tsfmt.json")

View all files

## Repository files navigation

- README
- Code of conduct
- MIT license
- Security

# Visual Studio Code - Open Source ("Code - OSS")

[Permalink: Visual Studio Code - Open Source ("Code - OSS")](http://github.com#visual-studio-code---open-source-code---oss)

[![Feature Requests](https://camo.githubusercontent.com/ea327406fe1c720fbef262f25205a9ac8dc594191bd3e4ae9ab03f0b774380d0/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f6973737565732f6d6963726f736f66742f7673636f64652f666561747572652d726571756573742e737667)](https://github.com/microsoft/vscode/issues?q=is%3Aopen+is%3Aissue+label%3Afeature-request+sort%3Areactions-%2B1-desc)[![Bugs](https://camo.githubusercontent.com/0470413c58206545145fa5b52e2c39ed7cbdc5f0c8ad3b297f24a048a142fae8/68747470733a2f2f696d672e736869656c64732e696f2f6769746875622f6973737565732f6d6963726f736f66742f7673636f64652f6275672e737667)](https://github.com/microsoft/vscode/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen+label%3Abug)[![Gitter](https://camo.githubusercontent.com/52f104396f3a7fd57a59ca2c5a99c2d685c2f44671c078bb592378650d658728/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f636861742d6f6e2532306769747465722d79656c6c6f772e737667)](https://gitter.im/Microsoft/vscode)

## The Repository

[Permalink: The Repository](http://github.com#the-repository)

This repository (" `Code - OSS`") is where we (Microsoft) develop the [Visual Studio Code](https://code.visualstudio.com) product together with the community. Not only do we work on code and issues here, we also publish our [roadmap](https://github.com/microsoft/vscode/wiki/Roadmap), [monthly iteration plans](https://github.com/microsoft/vscode/wiki/Iteration-Plans), and our [endgame plans](https://github.com/microsoft/vscode/wiki/Running-the-Endgame). This source code is available to everyone under the standard [MIT license](https://github.com/microsoft/vscode/blob/main/LICENSE.txt).

## Visual Studio Code

[Permalink: Visual Studio Code](http://github.com#visual-studio-code)

[![VS Code in action](https://user-images.githubusercontent.com/35271042/118224532-3842c400-b438-11eb-923d-a5f66fa6785a.png)](https://user-images.githubusercontent.com/35271042/118224532-3842c400-b438-11eb-923d-a5f66fa6785a.png)

[Visual Studio Code](https://code.visualstudio.com) is a distribution of the `Code - OSS` repository with Microsoft-specific customizations released under a traditional [Microsoft product license](https://code.visualstudio.com/License/).

[Visual Studio Code](https://code.visualstudio.com) combines the simplicity of a code editor with what developers need for their core edit-build-debug cycle. It provides comprehensive code editing, navigation, and understanding support along with lightweight debugging, a rich extensibility model, and lightweight integration with existing tools.

Visual Studio Code is updated monthly with new features and bug fixes. You can download it for Windows, macOS, and Linux on [Visual Studio Code's website](https://code.visualstudio.com/Download). To get the latest releases every day, install the [Insiders build](https://code.visualstudio.com/insiders).

## Contributing

[Permalink: Contributing](http://github.com#contributing)

There are many ways in which you can participate in this project, for example:

- [Submit bugs and feature requests](https://github.com/microsoft/vscode/issues), and help us verify as they are checked in
- Review [source code changes](https://github.com/microsoft/vscode/pulls)
- Review the [documentation](https://github.com/microsoft/vscode-docs) and make pull requests for anything from typos to additional and new content

If you are interested in fixing issues and contributing directly to the code base,
please see the document [How to Contribute](https://github.com/microsoft/vscode/wiki/How-to-Contribute), which covers the following:

- [How to build and run from source](https://github.com/microsoft/vscode/wiki/How-to-Contribute)
- [The development workflow, including debugging and running tests](https://github.com/microsoft/vscode/wiki/How-to-Contribute#debugging)
- [Coding guidelines](https://github.com/microsoft/vscode/wiki/Coding-Guidelines)
- [Submitting pull requests](https://github.com/microsoft/vscode/wiki/How-to-Contribute#pull-requests)
- [Finding an issue to work on](https://github.com/microsoft/vscode/wiki/How-to-Contribute#where-to-contribute)
- [Contributing to translations](https://aka.ms/vscodeloc)

## Feedback

[Permalink: Feedback](http://github.com#feedback)

- Ask a question on [Stack Overflow](https://stackoverflow.com/questions/tagged/vscode)
- [Request a new feature](http://github.com/microsoft/vscode/blob/main/CONTRIBUTING.md)
- Upvote [popular feature requests](https://github.com/microsoft/vscode/issues?q=is%3Aopen+is%3Aissue+label%3Afeature-request+sort%3Areactions-%2B1-desc)
- [File an issue](https://github.com/microsoft/vscode/issues)
- Connect with the extension author community on [GitHub Discussions](https://github.com/microsoft/vscode-discussions/discussions) or [Slack](https://aka.ms/vscode-dev-community)
- Follow [@code](https://twitter.com/code) and let us know what you think!

See our [wiki](https://github.com/microsoft/vscode/wiki/Feedback-Channels) for a description of each of these channels and information on some other available community-driven channels.

## Related Projects

[Permalink: Related Projects](http://github.com#related-projects)

Many of the core components and extensions to VS Code live in their own repositories on GitHub. For example, the [node debug adapter](https://github.com/microsoft/vscode-node-debug) and the [mono debug adapter](https://github.com/microsoft/vscode-mono-debug) repositories are separate from each other. For a complete list, please visit the [Related Projects](https://github.com/microsoft/vscode/wiki/Related-Projects) page on our [wiki](https://github.com/microsoft/vscode/wiki).

## Bundled Extensions

[Permalink: Bundled Extensions](http://github.com#bundled-extensions)

VS Code includes a set of built-in extensions located in the [extensions](http://github.com/microsoft/vscode/blob/main/extensions) folder, including grammars and snippets for many languages. Extensions that provide rich language support (code completion, Go to Definition) for a language have the suffix `language-features`. For example, the `json` extension provides coloring for `JSON` and the `json-language-features` extension provides rich language support for `JSON`.

## Development Container

[Permalink: Development Container](http://github.com#development-container)

This repository includes a Visual Studio Code Dev Containers / GitHub Codespaces development container.

- For [Dev Containers](https://aka.ms/vscode-remote/download/containers), use the **Dev Containers: Clone Repository in Container Volume...** command which creates a Docker volume for better disk I/O on macOS and Windows.
  - If you already have VS Code and Docker installed, you can also click [here](https://vscode.dev/redirect?url=vscode://ms-vscode-remote.remote-containers/cloneInVolume?url=https://github.com/microsoft/vscode) to get started. This will cause VS Code to automatically install the Dev Containers extension if needed, clone the source code into a container volume, and spin up a dev container for use.
- For Codespaces, install the [GitHub Codespaces](https://marketplace.visualstudio.com/items?itemName=GitHub.codespaces) extension in VS Code, and use the **Codespaces: Create New Codespace** command.


Docker / the Codespace should have at least **4 Cores and 6 GB of RAM (8 GB recommended)** to run full build. See the [development container README](http://github.com/microsoft/vscode/blob/main/.devcontainer/README.md) for more information.

## Code of Conduct

[Permalink: Code of Conduct](http://github.com#code-of-conduct)

This project has adopted the [Microsoft Open Source Code of Conduct](https://opensource.microsoft.com/codeofconduct/). For more information see the [Code of Conduct FAQ](https://opensource.microsoft.com/codeofconduct/faq/) or contact [<EMAIL>](mailto:<EMAIL>) with any additional questions or comments.

## License

[Permalink: License](http://github.com#license)

Copyright (c) Microsoft Corporation. All rights reserved.

Licensed under the [MIT](http://github.com/microsoft/vscode/blob/main/LICENSE.txt) license.

## About

Visual Studio Code


[code.visualstudio.com](https://code.visualstudio.com "https://code.visualstudio.com")

### Topics

[electron](http://github.com/topics/electron "Topic: electron") [microsoft](http://github.com/topics/microsoft "Topic: microsoft") [editor](http://github.com/topics/editor "Topic: editor") [typescript](http://github.com/topics/typescript "Topic: typescript") [visual-studio-code](http://github.com/topics/visual-studio-code "Topic: visual-studio-code")

### Resources

[Readme](http://github.com#readme-ov-file)

### License

[MIT license](http://github.com#MIT-1-ov-file)

### Code of conduct

[Code of conduct](http://github.com#coc-ov-file)

### Security policy

[Security policy](http://github.com#security-ov-file)

### Uh oh!

There was an error while loading. Please reload this page.

[Activity](http://github.com/microsoft/vscode/activity)

[Custom properties](http://github.com/microsoft/vscode/custom-properties)

### Stars

[**173k**\
stars](http://github.com/microsoft/vscode/stargazers)

### Watchers

[**3.4k**\
watching](http://github.com/microsoft/vscode/watchers)

### Forks

[**33k**\
forks](http://github.com/microsoft/vscode/forks)

[Report repository](http://github.com/contact/report-content?content_url=https%3A%2F%2Fgithub.com%2Fmicrosoft%2Fvscode&report=microsoft+%28user%29)

## [Releases\  168](http://github.com/microsoft/vscode/releases)

[May 2025\
Latest\
\
Jun 12, 2025](http://github.com/microsoft/vscode/releases/tag/1.101.0)

[\+ 167 releases](http://github.com/microsoft/vscode/releases)

### Uh oh!

There was an error while loading. Please reload this page.

## [Contributors\  2,205](http://github.com/microsoft/vscode/graphs/contributors)

- [![@bpasero](https://avatars.githubusercontent.com/u/900690?s=64&v=4)](https://github.com/bpasero)
- [![@jrieken](https://avatars.githubusercontent.com/u/1794099?s=64&v=4)](https://github.com/jrieken)
- [![@Tyriar](https://avatars.githubusercontent.com/u/2193314?s=64&v=4)](https://github.com/Tyriar)
- [![@joaomoreno](https://avatars.githubusercontent.com/u/22350?s=64&v=4)](https://github.com/joaomoreno)
- [![@mjbvz](https://avatars.githubusercontent.com/u/12821956?s=64&v=4)](https://github.com/mjbvz)
- [![@sandy081](https://avatars.githubusercontent.com/u/10746682?s=64&v=4)](https://github.com/sandy081)
- [![@alexdima](https://avatars.githubusercontent.com/u/5047891?s=64&v=4)](https://github.com/alexdima)
- [![@isidorn](https://avatars.githubusercontent.com/u/1926584?s=64&v=4)](https://github.com/isidorn)
- [![@roblourens](https://avatars.githubusercontent.com/u/323878?s=64&v=4)](https://github.com/roblourens)
- [![@rebornix](https://avatars.githubusercontent.com/u/876920?s=64&v=4)](https://github.com/rebornix)
- [![@aeschli](https://avatars.githubusercontent.com/u/6461412?s=64&v=4)](https://github.com/aeschli)
- [![@meganrogge](https://avatars.githubusercontent.com/u/29464607?s=64&v=4)](https://github.com/meganrogge)
- [![@alexr00](https://avatars.githubusercontent.com/u/38270282?s=64&v=4)](https://github.com/alexr00)
- [![@connor4312](https://avatars.githubusercontent.com/u/2230985?s=64&v=4)](https://github.com/connor4312)

[\+ 2,191 contributors](http://github.com/microsoft/vscode/graphs/contributors)

## Languages

- [TypeScript95.5%](http://github.com/microsoft/vscode/search?l=typescript)
- [CSS1.4%](http://github.com/microsoft/vscode/search?l=css)
- [JavaScript1.1%](http://github.com/microsoft/vscode/search?l=javascript)
- [Rust0.7%](http://github.com/microsoft/vscode/search?l=rust)
- [HTML0.5%](http://github.com/microsoft/vscode/search?l=html)
- [Inno Setup0.4%](http://github.com/microsoft/vscode/search?l=inno-setup)
- Other0.4%

## Footer

[GitHub Homepage](https://github.com)
© 2025 GitHub, Inc.


### Footer navigation

- [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
- [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
- [Security](https://github.com/security)
- [Status](https://www.githubstatus.com/)
- [Docs](https://docs.github.com/)
- [Contact](https://support.github.com?tags=dotcom-footer)
- Manage cookies

- Do not share my personal information


You can’t perform that action at this time.

---

# Media Assets

## Favicon
- https://github.com/favicon.ico

## Screenshot
- Status: Available
- Dimensions: 1200x800
- Full Page: No
- Captured: 2025-06-13T03:55:55.604Z
